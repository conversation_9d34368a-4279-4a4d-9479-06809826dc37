using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using SimpleAccountingApp.Data;
using SimpleAccountingApp.Models;
using Microsoft.EntityFrameworkCore;

namespace SimpleAccountingApp.Windows
{
    public partial class CustomersWindow : Window
    {
        private ObservableCollection<Customer> customers = new();
        private AccountingDbContext _context;

        public CustomersWindow()
        {
            InitializeComponent();
            _context = new AccountingDbContext();
            LoadCustomers();
        }

        private async void LoadCustomers()
        {
            try
            {
                await _context.InitializeDatabaseAsync();
                var customersList = await _context.Customers
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.CustomerName)
                    .ToListAsync();

                customers.Clear();
                foreach (var customer in customersList)
                {
                    customers.Add(customer);
                }

                CustomersDataGrid.ItemsSource = customers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العملاء: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }


        private void CustomersDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            bool hasSelection = CustomersDataGrid.SelectedItem != null;
            EditCustomerButton.IsEnabled = hasSelection;
            DeleteCustomerButton.IsEnabled = hasSelection;
        }

        private async void AddCustomerButton_Click(object sender, RoutedEventArgs e)
        {
            var addCustomerWindow = new AddCustomerWindow();
            if (addCustomerWindow.ShowDialog() == true)
            {
                // الحصول على العميل الجديد من النافذة
                var newCustomer = addCustomerWindow.NewCustomer;
                if (newCustomer != null)
                {
                    try
                    {
                        // حفظ العميل في قاعدة البيانات
                        _context.Customers.Add(newCustomer);
                        await _context.SaveChangesAsync();

                        // إضافة العميل الجديد إلى القائمة
                        customers.Add(newCustomer);

                        // تحديث عرض البيانات
                        RefreshCustomersGrid();

                        MessageBox.Show("تم إضافة العميل بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حفظ العميل: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void RefreshCustomersGrid()
        {
            CustomersDataGrid.ItemsSource = null;
            CustomersDataGrid.ItemsSource = customers;
        }

        private async void EditCustomerButton_Click(object sender, RoutedEventArgs e)
        {
            if (CustomersDataGrid.SelectedItem is Customer selectedCustomer)
            {
                var editCustomerWindow = new AddCustomerWindow(selectedCustomer);
                if (editCustomerWindow.ShowDialog() == true)
                {
                    try
                    {
                        // تحديث العميل في قاعدة البيانات
                        _context.Customers.Update(selectedCustomer);
                        await _context.SaveChangesAsync();

                        // تحديث عرض البيانات
                        RefreshCustomersGrid();

                        MessageBox.Show("تم تعديل العميل بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في تحديث العميل: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private async void DeleteCustomerButton_Click(object sender, RoutedEventArgs e)
        {
            if (CustomersDataGrid.SelectedItem is Customer selectedCustomer)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف العميل '{selectedCustomer.CustomerName}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // حذف العميل من قاعدة البيانات
                        _context.Customers.Remove(selectedCustomer);
                        await _context.SaveChangesAsync();

                        // حذف العميل من القائمة
                        customers.Remove(selectedCustomer);

                        // تحديث عرض البيانات
                        RefreshCustomersGrid();

                        MessageBox.Show("تم حذف العميل بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف العميل: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            string searchText = SearchTextBox.Text.ToLower();
            if (string.IsNullOrWhiteSpace(searchText))
            {
                CustomersDataGrid.ItemsSource = customers;
            }
            else
            {
                var filteredCustomers = customers.Where(c => 
                    c.CustomerName.ToLower().Contains(searchText) ||
                    c.CustomerCode.ToLower().Contains(searchText) ||
                    c.Phone.Contains(searchText) ||
                    c.Mobile.Contains(searchText) ||
                    c.Email.ToLower().Contains(searchText)
                ).ToList();
                
                CustomersDataGrid.ItemsSource = filteredCustomers;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

}
