using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace SimpleAccountingApp.Windows
{
    public partial class CustomersWindow : Window
    {
        private ObservableCollection<Customer> customers;

        public CustomersWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            customers = new ObservableCollection<Customer>
            {
                new Customer
                {
                    CustomerCode = "C001",
                    CustomerName = "شركة الأمل للتجارة",
                    CustomerType = "شركة",
                    Phone = "011-1234567",
                    Mobile = "**********",
                    Email = "<EMAIL>",
                    Address = "الرياض، حي الملك فهد",
                    City = "الرياض",
                    Country = "السعودية",
                    TaxNumber = "*********",
                    CreditLimit = 50000,
                    CurrentBalance = 15000,
                    PaymentTerms = 30,
                    IsActive = true,
                    Notes = "عميل مميز"
                },
                new Customer
                {
                    CustomerCode = "C002",
                    CustomerName = "أحمد محمد العلي",
                    CustomerType = "فرد",
                    Phone = "011-7654321",
                    Mobile = "0507654321",
                    Email = "<EMAIL>",
                    Address = "جدة، حي الصفا",
                    City = "جدة",
                    Country = "السعودية",
                    TaxNumber = "",
                    CreditLimit = 10000,
                    CurrentBalance = 5000,
                    PaymentTerms = 15,
                    IsActive = true,
                    Notes = ""
                },
                new Customer
                {
                    CustomerCode = "C003",
                    CustomerName = "مؤسسة النور للمقاولات",
                    CustomerType = "مؤسسة",
                    Phone = "013-9876543",
                    Mobile = "0509876543",
                    Email = "<EMAIL>",
                    Address = "الدمام، حي الشاطئ",
                    City = "الدمام",
                    Country = "السعودية",
                    TaxNumber = "*********",
                    CreditLimit = 100000,
                    CurrentBalance = 25000,
                    PaymentTerms = 45,
                    IsActive = true,
                    Notes = "مقاول معتمد"
                },
                new Customer
                {
                    CustomerCode = "C004",
                    CustomerName = "فاطمة سالم القحطاني",
                    CustomerType = "فرد",
                    Phone = "017-5555555",
                    Mobile = "0555555555",
                    Email = "<EMAIL>",
                    Address = "أبها، حي المنهل",
                    City = "أبها",
                    Country = "السعودية",
                    TaxNumber = "",
                    CreditLimit = 5000,
                    CurrentBalance = 0,
                    PaymentTerms = 7,
                    IsActive = false,
                    Notes = "عميل متوقف"
                }
            };

            CustomersDataGrid.ItemsSource = customers;
        }

        private void CustomersDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            bool hasSelection = CustomersDataGrid.SelectedItem != null;
            EditCustomerButton.IsEnabled = hasSelection;
            DeleteCustomerButton.IsEnabled = hasSelection;
        }

        private void AddCustomerButton_Click(object sender, RoutedEventArgs e)
        {
            var addCustomerWindow = new AddCustomerWindow();
            if (addCustomerWindow.ShowDialog() == true)
            {
                // الحصول على العميل الجديد من النافذة
                var newCustomer = addCustomerWindow.NewCustomer;
                if (newCustomer != null)
                {
                    // إضافة العميل الجديد إلى القائمة
                    customers.Add(newCustomer);

                    // تحديث عرض البيانات
                    RefreshCustomersGrid();

                    MessageBox.Show("تم إضافة العميل بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void RefreshCustomersGrid()
        {
            CustomersDataGrid.ItemsSource = null;
            CustomersDataGrid.ItemsSource = customers;
        }

        private void EditCustomerButton_Click(object sender, RoutedEventArgs e)
        {
            if (CustomersDataGrid.SelectedItem is Customer selectedCustomer)
            {
                var editCustomerWindow = new AddCustomerWindow(selectedCustomer);
                if (editCustomerWindow.ShowDialog() == true)
                {
                    // تحديث عرض البيانات
                    RefreshCustomersGrid();

                    MessageBox.Show("تم تعديل العميل بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void DeleteCustomerButton_Click(object sender, RoutedEventArgs e)
        {
            if (CustomersDataGrid.SelectedItem is Customer selectedCustomer)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف العميل '{selectedCustomer.CustomerName}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    customers.Remove(selectedCustomer);

                    // تحديث عرض البيانات
                    RefreshCustomersGrid();

                    MessageBox.Show("تم حذف العميل بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            string searchText = SearchTextBox.Text.ToLower();
            if (string.IsNullOrWhiteSpace(searchText))
            {
                CustomersDataGrid.ItemsSource = customers;
            }
            else
            {
                var filteredCustomers = customers.Where(c => 
                    c.CustomerName.ToLower().Contains(searchText) ||
                    c.CustomerCode.ToLower().Contains(searchText) ||
                    c.Phone.Contains(searchText) ||
                    c.Mobile.Contains(searchText) ||
                    c.Email.ToLower().Contains(searchText)
                ).ToList();
                
                CustomersDataGrid.ItemsSource = filteredCustomers;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    // نموذج بيانات العميل
    public class Customer
    {
        public string CustomerCode { get; set; } = "";
        public string CustomerName { get; set; } = "";
        public string CustomerType { get; set; } = "";
        public string Address { get; set; } = "";
        public string City { get; set; } = "";
        public string Country { get; set; } = "السعودية";
        public string Phone { get; set; } = "";
        public string Mobile { get; set; } = "";
        public string Email { get; set; } = "";
        public string TaxNumber { get; set; } = "";
        public decimal CreditLimit { get; set; }
        public decimal CurrentBalance { get; set; }
        public int PaymentTerms { get; set; } = 30;
        public bool IsActive { get; set; } = true;
        public string Notes { get; set; } = "";
        
        public string IsActiveText => IsActive ? "نشط" : "غير نشط";
    }
}
