using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using SimpleAccountingApp.Models;
using SimpleAccountingApp.Data;
using Microsoft.EntityFrameworkCore;

namespace SimpleAccountingApp.Windows
{
    public partial class SelectEmployeeWindow : Window
    {
        public Employee? SelectedEmployee { get; private set; }
        private ObservableCollection<Employee> employees = new();
        private ObservableCollection<Employee> filteredEmployees = new();
        private AccountingDbContext _context;

        public SelectEmployeeWindow()
        {
            InitializeComponent();
            _context = new AccountingDbContext();
            LoadEmployees();
        }

        private async void LoadEmployees()
        {
            try
            {
                await _context.InitializeDatabaseAsync();
                var employeesList = await _context.Employees
                    .Where(e => e.Status == EmployeeStatus.Active)
                    .OrderBy(e => e.Department)
                    .ThenBy(e => e.FirstName)
                    .ToListAsync();
                
                employees.Clear();
                filteredEmployees.Clear();
                
                foreach (var employee in employeesList)
                {
                    employees.Add(employee);
                    filteredEmployees.Add(employee);
                }

                EmployeesDataGrid.ItemsSource = filteredEmployees;
                
                // تحميل الأقسام
                await LoadDepartments();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الموظفين: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadDepartments()
        {
            try
            {
                var departments = await _context.Employees
                    .Where(e => e.Status == EmployeeStatus.Active)
                    .Select(e => e.Department)
                    .Distinct()
                    .OrderBy(d => d)
                    .ToListAsync();

                DepartmentComboBox.Items.Clear();
                DepartmentComboBox.Items.Add(new ComboBoxItem { Content = "جميع الأقسام", Tag = "", IsSelected = true });
                
                foreach (var dept in departments)
                {
                    DepartmentComboBox.Items.Add(new ComboBoxItem { Content = dept, Tag = dept });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الأقسام: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterEmployees();
        }

        private void DepartmentComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterEmployees();
        }

        private void FilterEmployees()
        {
            var searchText = SearchTextBox.Text?.ToLower() ?? "";
            var selectedDept = "";
            
            if (DepartmentComboBox.SelectedItem is ComboBoxItem deptItem)
            {
                selectedDept = deptItem.Tag?.ToString() ?? "";
            }

            filteredEmployees.Clear();
            
            foreach (var employee in employees)
            {
                bool matchesSearch = string.IsNullOrEmpty(searchText) ||
                    employee.FullName.ToLower().Contains(searchText) ||
                    employee.EmployeeCode.ToLower().Contains(searchText) ||
                    employee.JobTitle.ToLower().Contains(searchText);

                bool matchesDept = string.IsNullOrEmpty(selectedDept) ||
                    employee.Department == selectedDept;

                if (matchesSearch && matchesDept)
                {
                    filteredEmployees.Add(employee);
                }
            }

            EmployeesDataGrid.Items.Refresh();
        }

        private void EmployeesDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            SelectEmployee();
        }

        private void SelectButton_Click(object sender, RoutedEventArgs e)
        {
            SelectEmployee();
        }

        private void SelectEmployee()
        {
            if (EmployeesDataGrid.SelectedItem is Employee selectedEmployee)
            {
                SelectedEmployee = selectedEmployee;
                DialogResult = true;
                Close();
            }
            else
            {
                MessageBox.Show("يرجى اختيار موظف.", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
