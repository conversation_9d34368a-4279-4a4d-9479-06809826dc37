<Window x:Class="SimpleAccountingApp.Windows.AddWarehouseWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة مخزن جديد - نظام المحاسبة المالية" 
        Height="600" Width="700"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#795548" Padding="15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="🏪" FontSize="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock x:Name="WindowTitle" 
                          Text="إضافة مخزن جديد" 
                          FontSize="18" 
                          FontWeight="Bold" 
                          Foreground="White" 
                          VerticalAlignment="Center"/>
            </StackPanel>
        </Border>
        
        <!-- Form Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- Right Column -->
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    <!-- Warehouse Code -->
                    <TextBlock Text="رقم المخزن *" FontWeight="Bold" Margin="0,10,0,5"/>
                    <TextBox x:Name="WarehouseCodeTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"/>
                    
                    <!-- Warehouse Name -->
                    <TextBlock Text="اسم المخزن *" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="WarehouseNameTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"/>
                    
                    <!-- Location -->
                    <TextBlock Text="الموقع *" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="LocationTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"/>
                    
                    <!-- Manager -->
                    <TextBlock Text="المسؤول" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="ManagerTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"/>
                    
                    <!-- Phone -->
                    <TextBlock Text="الهاتف" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="PhoneTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"/>
                </StackPanel>
                
                <!-- Left Column -->
                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                    <!-- Area -->
                    <TextBlock Text="المساحة (م²)" FontWeight="Bold" Margin="0,10,0,5"/>
                    <TextBox x:Name="AreaTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"
                            Text="0"/>
                    
                    <!-- Capacity -->
                    <TextBlock Text="السعة التخزينية" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="CapacityTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"
                            Text="0"/>
                    
                    <!-- Temperature Control -->
                    <TextBlock Text="التحكم في درجة الحرارة" FontWeight="Bold" Margin="0,15,0,5"/>
                    <ComboBox x:Name="TemperatureControlComboBox" 
                             Height="35" 
                             Padding="10"
                             FontSize="14">
                        <ComboBoxItem Content="غير مطلوب"/>
                        <ComboBoxItem Content="تبريد"/>
                        <ComboBoxItem Content="تجميد"/>
                        <ComboBoxItem Content="تدفئة"/>
                    </ComboBox>
                    
                    <!-- Security Level -->
                    <TextBlock Text="مستوى الأمان" FontWeight="Bold" Margin="0,15,0,5"/>
                    <ComboBox x:Name="SecurityLevelComboBox" 
                             Height="35" 
                             Padding="10"
                             FontSize="14">
                        <ComboBoxItem Content="عادي"/>
                        <ComboBoxItem Content="متوسط"/>
                        <ComboBoxItem Content="عالي"/>
                        <ComboBoxItem Content="سري"/>
                    </ComboBox>
                    
                    <!-- Description -->
                    <TextBlock Text="الوصف" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="DescriptionTextBox" 
                            Height="80" 
                            Padding="10"
                            FontSize="14"
                            TextWrapping="Wrap"
                            AcceptsReturn="True"
                            VerticalScrollBarVisibility="Auto"/>
                </StackPanel>
            </Grid>
        </ScrollViewer>
        
        <!-- Bottom Section -->
        <Border Grid.Row="2" Background="#F5F5F5" Padding="15" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- Is Active Checkbox -->
                <CheckBox x:Name="IsActiveCheckBox" 
                         Grid.Column="0"
                         Content="مخزن نشط" 
                         IsChecked="True" 
                         VerticalAlignment="Center"
                         FontWeight="Bold"/>
                
                <!-- Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="SaveButton" 
                           Content="💾 حفظ" 
                           Background="#4CAF50" 
                           Foreground="White" 
                           BorderThickness="0"
                           Padding="20,10" 
                           Margin="10,0"
                           FontSize="14"
                           FontWeight="Bold"
                           Click="SaveButton_Click"/>
                    
                    <Button x:Name="CancelButton" 
                           Content="❌ إلغاء" 
                           Background="#F44336" 
                           Foreground="White" 
                           BorderThickness="0"
                           Padding="20,10" 
                           Margin="10,0"
                           FontSize="14"
                           FontWeight="Bold"
                           Click="CancelButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
