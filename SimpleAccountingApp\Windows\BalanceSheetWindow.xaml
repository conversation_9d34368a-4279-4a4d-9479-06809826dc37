<Window x:Class="SimpleAccountingApp.Windows.BalanceSheetWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="الميزانية العمومية - نظام المحاسبة المالية" 
        Height="700" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" Padding="15">
            <DockPanel>
                <Button DockPanel.Dock="Left" 
                       Content="✖ إغلاق" 
                       Background="#1976D2" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,5" 
                       Click="CloseButton_Click"/>
                
                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                    <TextBlock Text="⚖️" FontSize="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="الميزانية العمومية" 
                              FontSize="20" 
                              FontWeight="Bold" 
                              Foreground="White" 
                              VerticalAlignment="Center"/>
                </StackPanel>
            </DockPanel>
        </Border>
        
        <!-- Date Selection -->
        <Border Grid.Row="1" Background="#F5F5F5" Padding="15" BorderBrush="#DDD" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="كما في تاريخ:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <DatePicker x:Name="AsOfDatePicker" Grid.Column="1" Height="35" SelectedDate="2024-12-31"/>
                
                <Button x:Name="RefreshButton" 
                       Grid.Column="2" 
                       Content="🔄 تحديث" 
                       Background="#2196F3" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="15,0,0,0"
                       Click="RefreshButton_Click"/>
            </Grid>
        </Border>
        
        <!-- Balance Sheet Content -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto" Padding="20">
            <Border Background="White" BorderBrush="#DDD" BorderThickness="1" CornerRadius="5">
                <StackPanel Margin="30">
                    <!-- Company Header -->
                    <TextBlock Text="شركة المحاسبة المالية المتقدمة" 
                              FontSize="20" 
                              FontWeight="Bold" 
                              HorizontalAlignment="Center" 
                              Margin="0,0,0,5"/>
                    <TextBlock Text="الميزانية العمومية" 
                              FontSize="16" 
                              FontWeight="Bold" 
                              HorizontalAlignment="Center" 
                              Margin="0,0,0,5"/>
                    <TextBlock x:Name="AsOfDateText" 
                              Text="كما في 2024/12/31" 
                              FontSize="14" 
                              HorizontalAlignment="Center" 
                              Margin="0,0,0,30"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Assets Side -->
                        <StackPanel Grid.Column="0">
                            <!-- Assets Header -->
                            <Border Background="#E3F2FD" Padding="10" Margin="0,0,0,10">
                                <TextBlock Text="الأصول" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center"/>
                            </Border>
                            
                            <!-- Current Assets -->
                            <Border Background="#F5F5F5" Padding="8" Margin="0,0,0,5">
                                <TextBlock Text="الأصول المتداولة" FontWeight="Bold"/>
                            </Border>
                            
                            <Grid Margin="15,0,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="100"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="النقدية والبنوك" Margin="0,3"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="125,000" HorizontalAlignment="Right" Margin="0,3"/>
                                
                                <TextBlock Grid.Row="1" Grid.Column="0" Text="العملاء" Margin="0,3"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="85,000" HorizontalAlignment="Right" Margin="0,3"/>
                                
                                <TextBlock Grid.Row="2" Grid.Column="0" Text="المخزون" Margin="0,3"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="180,000" HorizontalAlignment="Right" Margin="0,3"/>
                                
                                <Border Grid.Row="3" Grid.ColumnSpan="2" BorderBrush="#DDD" BorderThickness="0,1,0,0" Margin="0,5"/>
                                
                                <TextBlock Grid.Row="4" Grid.Column="0" Text="إجمالي الأصول المتداولة" FontWeight="Bold" Margin="0,3"/>
                                <TextBlock Grid.Row="4" Grid.Column="1" Text="390,000" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,3"/>
                            </Grid>
                            
                            <!-- Fixed Assets -->
                            <Border Background="#F5F5F5" Padding="8" Margin="0,15,0,5">
                                <TextBlock Text="الأصول الثابتة" FontWeight="Bold"/>
                            </Border>
                            
                            <Grid Margin="15,0,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="100"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="الأراضي والمباني" Margin="0,3"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="500,000" HorizontalAlignment="Right" Margin="0,3"/>
                                
                                <TextBlock Grid.Row="1" Grid.Column="0" Text="المعدات والآلات" Margin="0,3"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="150,000" HorizontalAlignment="Right" Margin="0,3"/>
                                
                                <TextBlock Grid.Row="2" Grid.Column="0" Text="ناقص: الإهلاك المتراكم" Margin="0,3"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="(80,000)" HorizontalAlignment="Right" Margin="0,3"/>
                                
                                <Border Grid.Row="3" Grid.ColumnSpan="2" BorderBrush="#DDD" BorderThickness="0,1,0,0" Margin="0,5"/>
                                
                                <TextBlock Grid.Row="4" Grid.Column="0" Text="صافي الأصول الثابتة" FontWeight="Bold" Margin="0,3"/>
                                <TextBlock Grid.Row="4" Grid.Column="1" Text="570,000" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,3"/>
                            </Grid>
                            
                            <!-- Total Assets -->
                            <Border Background="#C8E6C9" Padding="10" Margin="0,20,0,0">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="100"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBlock Grid.Column="0" Text="إجمالي الأصول" FontSize="16" FontWeight="Bold"/>
                                    <TextBlock Grid.Column="1" Text="960,000" FontSize="16" FontWeight="Bold" HorizontalAlignment="Right"/>
                                </Grid>
                            </Border>
                        </StackPanel>
                        
                        <!-- Separator -->
                        <Border Grid.Column="1" Background="#DDD" Width="1" Margin="10,0"/>
                        
                        <!-- Liabilities and Equity Side -->
                        <StackPanel Grid.Column="2">
                            <!-- Liabilities Header -->
                            <Border Background="#FFEBEE" Padding="10" Margin="0,0,0,10">
                                <TextBlock Text="الخصوم وحقوق الملكية" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center"/>
                            </Border>
                            
                            <!-- Current Liabilities -->
                            <Border Background="#F5F5F5" Padding="8" Margin="0,0,0,5">
                                <TextBlock Text="الخصوم المتداولة" FontWeight="Bold"/>
                            </Border>
                            
                            <Grid Margin="15,0,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="100"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="الموردون" Margin="0,3"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="65,000" HorizontalAlignment="Right" Margin="0,3"/>
                                
                                <TextBlock Grid.Row="1" Grid.Column="0" Text="مصروفات مستحقة" Margin="0,3"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="25,000" HorizontalAlignment="Right" Margin="0,3"/>
                                
                                <TextBlock Grid.Row="2" Grid.Column="0" Text="قروض قصيرة الأجل" Margin="0,3"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="50,000" HorizontalAlignment="Right" Margin="0,3"/>
                                
                                <Border Grid.Row="3" Grid.ColumnSpan="2" BorderBrush="#DDD" BorderThickness="0,1,0,0" Margin="0,5"/>
                                
                                <TextBlock Grid.Row="4" Grid.Column="0" Text="إجمالي الخصوم المتداولة" FontWeight="Bold" Margin="0,3"/>
                                <TextBlock Grid.Row="4" Grid.Column="1" Text="140,000" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,3"/>
                            </Grid>
                            
                            <!-- Long-term Liabilities -->
                            <Border Background="#F5F5F5" Padding="8" Margin="0,15,0,5">
                                <TextBlock Text="الخصوم طويلة الأجل" FontWeight="Bold"/>
                            </Border>
                            
                            <Grid Margin="15,0,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="100"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="قروض طويلة الأجل" Margin="0,3"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="200,000" HorizontalAlignment="Right" Margin="0,3"/>
                                
                                <Border Grid.Row="1" Grid.ColumnSpan="2" BorderBrush="#DDD" BorderThickness="0,1,0,0" Margin="0,5"/>
                                
                                <TextBlock Grid.Row="2" Grid.Column="0" Text="إجمالي الخصوم طويلة الأجل" FontWeight="Bold" Margin="0,3"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="200,000" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,3"/>
                            </Grid>
                            
                            <!-- Equity -->
                            <Border Background="#F5F5F5" Padding="8" Margin="0,15,0,5">
                                <TextBlock Text="حقوق الملكية" FontWeight="Bold"/>
                            </Border>
                            
                            <Grid Margin="15,0,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="100"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="رأس المال" Margin="0,3"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="400,000" HorizontalAlignment="Right" Margin="0,3"/>
                                
                                <TextBlock Grid.Row="1" Grid.Column="0" Text="الأرباح المحتجزة" Margin="0,3"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="220,000" HorizontalAlignment="Right" Margin="0,3"/>
                                
                                <Border Grid.Row="2" Grid.ColumnSpan="2" BorderBrush="#DDD" BorderThickness="0,1,0,0" Margin="0,5"/>
                                
                                <TextBlock Grid.Row="3" Grid.Column="0" Text="إجمالي حقوق الملكية" FontWeight="Bold" Margin="0,3"/>
                                <TextBlock Grid.Row="3" Grid.Column="1" Text="620,000" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,3"/>
                            </Grid>
                            
                            <!-- Total Liabilities and Equity -->
                            <Border Background="#C8E6C9" Padding="10" Margin="0,20,0,0">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="100"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBlock Grid.Column="0" Text="إجمالي الخصوم وحقوق الملكية" FontSize="16" FontWeight="Bold"/>
                                    <TextBlock Grid.Column="1" Text="960,000" FontSize="16" FontWeight="Bold" HorizontalAlignment="Right"/>
                                </Grid>
                            </Border>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>
        </ScrollViewer>
        
        <!-- Footer Buttons -->
        <Border Grid.Row="3" Background="#F5F5F5" Padding="15" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="PrintButton" 
                       Content="🖨️ طباعة" 
                       Background="#2196F3" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="20,10" 
                       Margin="10,0"
                       FontSize="14"
                       FontWeight="Bold"
                       Click="PrintButton_Click"/>
                
                <Button x:Name="ExportButton" 
                       Content="📤 تصدير" 
                       Background="#4CAF50" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="20,10" 
                       Margin="10,0"
                       FontSize="14"
                       FontWeight="Bold"
                       Click="ExportButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
