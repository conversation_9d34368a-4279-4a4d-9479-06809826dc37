using System.Windows;
using System.Windows.Controls;

namespace SimpleAccountingApp.Windows
{
    public partial class AddCustomerWindow : Window
    {
        private Customer? _editingCustomer;
        private bool _isEditMode;

        public AddCustomerWindow()
        {
            InitializeComponent();
            _isEditMode = false;
            GenerateCustomerCode();
        }

        public AddCustomerWindow(Customer customerToEdit)
        {
            InitializeComponent();
            _editingCustomer = customerToEdit;
            _isEditMode = true;
            
            WindowTitle.Text = "تعديل العميل";
            this.Title = "تعديل العميل - نظام المحاسبة المالية";
            
            LoadCustomerData();
        }

        private void GenerateCustomerCode()
        {
            // إنشاء رقم عميل تلقائي
            Random random = new Random();
            CustomerCodeTextBox.Text = "C" + (random.Next(100, 999)).ToString();
        }

        private void LoadCustomerData()
        {
            if (_editingCustomer != null)
            {
                CustomerCodeTextBox.Text = _editingCustomer.CustomerCode;
                CustomerNameTextBox.Text = _editingCustomer.CustomerName;
                
                // تحديد نوع العميل
                foreach (ComboBoxItem item in CustomerTypeComboBox.Items)
                {
                    if (item.Content.ToString() == _editingCustomer.CustomerType)
                    {
                        CustomerTypeComboBox.SelectedItem = item;
                        break;
                    }
                }
                
                PhoneTextBox.Text = _editingCustomer.Phone;
                MobileTextBox.Text = _editingCustomer.Mobile;
                EmailTextBox.Text = _editingCustomer.Email;
                AddressTextBox.Text = _editingCustomer.Address;
                CityTextBox.Text = _editingCustomer.City;
                CountryTextBox.Text = _editingCustomer.Country;
                TaxNumberTextBox.Text = _editingCustomer.TaxNumber;
                CreditLimitTextBox.Text = _editingCustomer.CreditLimit.ToString();
                PaymentTermsTextBox.Text = _editingCustomer.PaymentTerms.ToString();
                IsActiveCheckBox.IsChecked = _editingCustomer.IsActive;
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                if (_isEditMode)
                {
                    UpdateCustomer();
                }
                else
                {
                    CreateNewCustomer();
                }
                
                this.DialogResult = true;
                this.Close();
            }
        }

        private bool ValidateInput()
        {
            // التحقق من رقم العميل
            if (string.IsNullOrWhiteSpace(CustomerCodeTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم العميل", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CustomerCodeTextBox.Focus();
                return false;
            }

            // التحقق من اسم العميل
            if (string.IsNullOrWhiteSpace(CustomerNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العميل", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CustomerNameTextBox.Focus();
                return false;
            }

            // التحقق من نوع العميل
            if (CustomerTypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع العميل", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CustomerTypeComboBox.Focus();
                return false;
            }

            // التحقق من حد الائتمان
            if (!decimal.TryParse(CreditLimitTextBox.Text, out _))
            {
                MessageBox.Show("يرجى إدخال حد ائتمان صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CreditLimitTextBox.Focus();
                return false;
            }

            // التحقق من مدة السداد
            if (!int.TryParse(PaymentTermsTextBox.Text, out _))
            {
                MessageBox.Show("يرجى إدخال مدة سداد صحيحة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                PaymentTermsTextBox.Focus();
                return false;
            }

            return true;
        }

        private void CreateNewCustomer()
        {
            var newCustomer = new Customer
            {
                CustomerCode = CustomerCodeTextBox.Text.Trim(),
                CustomerName = CustomerNameTextBox.Text.Trim(),
                CustomerType = ((ComboBoxItem)CustomerTypeComboBox.SelectedItem).Content.ToString()!,
                Phone = PhoneTextBox.Text.Trim(),
                Mobile = MobileTextBox.Text.Trim(),
                Email = EmailTextBox.Text.Trim(),
                Address = AddressTextBox.Text.Trim(),
                City = CityTextBox.Text.Trim(),
                Country = CountryTextBox.Text.Trim(),
                TaxNumber = TaxNumberTextBox.Text.Trim(),
                CreditLimit = decimal.Parse(CreditLimitTextBox.Text),
                PaymentTerms = int.Parse(PaymentTermsTextBox.Text),
                IsActive = IsActiveCheckBox.IsChecked ?? true,
                CurrentBalance = 0
            };

            // في التطبيق الحقيقي، سيتم حفظ العميل في قاعدة البيانات
            // TODO: حفظ في قاعدة البيانات
        }

        private void UpdateCustomer()
        {
            if (_editingCustomer != null)
            {
                _editingCustomer.CustomerCode = CustomerCodeTextBox.Text.Trim();
                _editingCustomer.CustomerName = CustomerNameTextBox.Text.Trim();
                _editingCustomer.CustomerType = ((ComboBoxItem)CustomerTypeComboBox.SelectedItem).Content.ToString()!;
                _editingCustomer.Phone = PhoneTextBox.Text.Trim();
                _editingCustomer.Mobile = MobileTextBox.Text.Trim();
                _editingCustomer.Email = EmailTextBox.Text.Trim();
                _editingCustomer.Address = AddressTextBox.Text.Trim();
                _editingCustomer.City = CityTextBox.Text.Trim();
                _editingCustomer.Country = CountryTextBox.Text.Trim();
                _editingCustomer.TaxNumber = TaxNumberTextBox.Text.Trim();
                _editingCustomer.CreditLimit = decimal.Parse(CreditLimitTextBox.Text);
                _editingCustomer.PaymentTerms = int.Parse(PaymentTermsTextBox.Text);
                _editingCustomer.IsActive = IsActiveCheckBox.IsChecked ?? true;

                // في التطبيق الحقيقي، سيتم تحديث العميل في قاعدة البيانات
                // TODO: تحديث في قاعدة البيانات
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            this.DialogResult = false;
            this.Close();
        }
    }
}
