<Window x:Class="SimpleAccountingApp.Windows.AddSalesInvoiceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="فاتورة مبيعات جديدة - نظام المحاسبة المالية" 
        Height="750" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#4CAF50" Padding="15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="🧾" FontSize="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock x:Name="WindowTitle" 
                          Text="فاتورة مبيعات جديدة" 
                          FontSize="18" 
                          FontWeight="Bold" 
                          Foreground="White" 
                          VerticalAlignment="Center"/>
            </StackPanel>
        </Border>
        
        <!-- Invoice Header Info -->
        <Border Grid.Row="1" Background="#F9F9F9" Padding="15" BorderBrush="#DDD" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- Invoice Number -->
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="رقم الفاتورة *" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="InvoiceNumberTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"/>
                </StackPanel>
                
                <!-- Invoice Date -->
                <StackPanel Grid.Column="1" Margin="10,0">
                    <TextBlock Text="تاريخ الفاتورة *" FontWeight="Bold" Margin="0,0,0,5"/>
                    <DatePicker x:Name="InvoiceDatePicker" 
                               Height="35" 
                               FontSize="14"
                               SelectedDate="{x:Static sys:DateTime.Now}"
                               xmlns:sys="clr-namespace:System;assembly=mscorlib"/>
                </StackPanel>
                
                <!-- Customer -->
                <StackPanel Grid.Column="2" Margin="10,0,0,0">
                    <TextBlock Text="العميل *" FontWeight="Bold" Margin="0,0,0,5"/>
                    <ComboBox x:Name="CustomerComboBox" 
                             Height="35" 
                             Padding="10"
                             FontSize="14"
                             DisplayMemberPath="CustomerName"
                             SelectedValuePath="CustomerId"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Invoice Items -->
        <Grid Grid.Row="2" Margin="15">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- Items Header -->
            <Border Grid.Row="0" Background="#E3F2FD" Padding="10" Margin="0,0,0,5">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="أصناف الفاتورة" FontWeight="Bold" FontSize="16"/>
                    <Button x:Name="AddItemButton" 
                           Content="➕ إضافة صنف" 
                           Background="#2196F3" 
                           Foreground="White" 
                           BorderThickness="0"
                           Padding="10,5" 
                           Margin="20,0,0,0"
                           Click="AddItemButton_Click"/>
                </StackPanel>
            </Border>
            
            <!-- Items DataGrid -->
            <DataGrid x:Name="ItemsDataGrid" 
                     Grid.Row="1"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="True"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     AlternatingRowBackground="#F9F9F9">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم الصنف" 
                                       Binding="{Binding ProductCode}" 
                                       Width="100" IsReadOnly="True"/>
                    
                    <DataGridTextColumn Header="اسم الصنف" 
                                       Binding="{Binding ProductName}" 
                                       Width="200" IsReadOnly="True"/>
                    
                    <DataGridTextColumn Header="الكمية" 
                                       Binding="{Binding Quantity}" 
                                       Width="80"/>
                    
                    <DataGridTextColumn Header="سعر الوحدة" 
                                       Binding="{Binding UnitPrice, StringFormat=N2}" 
                                       Width="100"/>
                    
                    <DataGridTextColumn Header="الخصم" 
                                       Binding="{Binding DiscountAmount, StringFormat=N2}" 
                                       Width="80"/>
                    
                    <DataGridTextColumn Header="الضريبة" 
                                       Binding="{Binding TaxAmount, StringFormat=N2}" 
                                       Width="80" IsReadOnly="True"/>
                    
                    <DataGridTextColumn Header="المجموع" 
                                       Binding="{Binding TotalAmount, StringFormat=N2}" 
                                       Width="120" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>
        
        <!-- Invoice Totals -->
        <Border Grid.Row="3" Background="#F5F5F5" Padding="15" BorderBrush="#DDD" BorderThickness="0,1,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="300"/>
                </Grid.ColumnDefinitions>
                
                <!-- Payment Method -->
                <StackPanel Grid.Column="0">
                    <TextBlock Text="طريقة الدفع" FontWeight="Bold" Margin="0,0,0,5"/>
                    <ComboBox x:Name="PaymentMethodComboBox" 
                             Width="200" 
                             Height="35" 
                             HorizontalAlignment="Right"
                             Padding="10"
                             FontSize="14">
                        <ComboBoxItem Content="نقداً"/>
                        <ComboBoxItem Content="آجل"/>
                        <ComboBoxItem Content="شيك"/>
                        <ComboBoxItem Content="بطاقة ائتمان"/>
                        <ComboBoxItem Content="تحويل بنكي"/>
                    </ComboBox>
                </StackPanel>
                
                <!-- Totals -->
                <StackPanel Grid.Column="1">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="المبلغ الفرعي:" FontWeight="Bold" Margin="0,5"/>
                        <TextBlock x:Name="SubTotalText" Grid.Row="0" Grid.Column="1" Text="0.00 ريال" HorizontalAlignment="Left" Margin="10,5,0,5"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="إجمالي الخصم:" FontWeight="Bold" Margin="0,5"/>
                        <TextBlock x:Name="TotalDiscountText" Grid.Row="1" Grid.Column="1" Text="0.00 ريال" HorizontalAlignment="Left" Margin="10,5,0,5"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="إجمالي الضريبة:" FontWeight="Bold" Margin="0,5"/>
                        <TextBlock x:Name="TotalTaxText" Grid.Row="2" Grid.Column="1" Text="0.00 ريال" HorizontalAlignment="Left" Margin="10,5,0,5"/>
                        
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="المبلغ الإجمالي:" FontWeight="Bold" FontSize="16" Margin="0,5" Foreground="Green"/>
                        <TextBlock x:Name="GrandTotalText" Grid.Row="3" Grid.Column="1" Text="0.00 ريال" FontWeight="Bold" FontSize="16" HorizontalAlignment="Left" Margin="10,5,0,5" Foreground="Green"/>
                    </Grid>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Buttons -->
        <Border Grid.Row="4" Background="#F5F5F5" Padding="15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton" 
                       Content="💾 حفظ الفاتورة" 
                       Background="#4CAF50" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="20,10" 
                       Margin="10,0"
                       FontSize="14"
                       FontWeight="Bold"
                       Click="SaveButton_Click"/>
                
                <Button x:Name="SaveAndPrintButton" 
                       Content="💾🖨️ حفظ وطباعة" 
                       Background="#2196F3" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="20,10" 
                       Margin="10,0"
                       FontSize="14"
                       FontWeight="Bold"
                       Click="SaveAndPrintButton_Click"/>
                
                <Button x:Name="CancelButton" 
                       Content="❌ إلغاء" 
                       Background="#F44336" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="20,10" 
                       Margin="10,0"
                       FontSize="14"
                       FontWeight="Bold"
                       Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
