using System.IO;
using System.Text;
using System.Windows;
using Microsoft.Win32;
using SimpleAccountingApp.Models;

namespace SimpleAccountingApp.Windows
{
    public partial class PayrollExportWindow : Window
    {
        private List<Payroll> _payrolls;

        public PayrollExportWindow(List<Payroll> payrolls)
        {
            InitializeComponent();
            _payrolls = payrolls;
            InitializeWindow();
        }

        private void InitializeWindow()
        {
            var currentDate = DateTime.Now;
            FileNameTextBox.Text = $"Payrolls_{currentDate:yyyyMMdd}.csv";
            SavePathTextBox.Text = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
        }

        private void BrowseButton_Click(object sender, RoutedEventArgs e)
        {
            var saveDialog = new SaveFileDialog();
            saveDialog.Title = "حفظ ملف الرواتب";
            saveDialog.Filter = "CSV Files (*.csv)|*.csv|All Files (*.*)|*.*";
            saveDialog.DefaultExt = "csv";
            saveDialog.FileName = FileNameTextBox.Text;

            if (saveDialog.ShowDialog() == true)
            {
                var directory = Path.GetDirectoryName(saveDialog.FileName);
                var fileName = Path.GetFileName(saveDialog.FileName);

                SavePathTextBox.Text = directory ?? "";
                FileNameTextBox.Text = fileName;
            }
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(FileNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم الملف.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (string.IsNullOrWhiteSpace(SavePathTextBox.Text))
                {
                    MessageBox.Show("يرجى اختيار مسار الحفظ.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var fileName = FileNameTextBox.Text;
                if (!fileName.EndsWith(".csv", StringComparison.OrdinalIgnoreCase))
                {
                    fileName += ".csv";
                }

                var fullPath = Path.Combine(SavePathTextBox.Text, fileName);

                // إنشاء محتوى CSV
                var csv = new StringBuilder();
                
                // إضافة العناوين
                if (IncludeDetailsCheckBox.IsChecked == true)
                {
                    csv.AppendLine("رقم الراتب,اسم الموظف,كود الموظف,القسم,المسمى الوظيفي,الشهر,السنة,تاريخ الراتب,الراتب الأساسي,بدل السكن,بدل المواصلات,بدل الطعام,بدلات أخرى,ساعات إضافية,أجر الساعات الإضافية,إجمالي الاستحقاقات,التأمينات الاجتماعية,ضريبة الدخل,قرض,خصومات أخرى,إجمالي الخصومات,صافي الراتب,الحالة,ملاحظات");
                }
                else
                {
                    csv.AppendLine("رقم الراتب,اسم الموظف,القسم,الشهر,السنة,إجمالي الاستحقاقات,إجمالي الخصومات,صافي الراتب,الحالة");
                }

                // إضافة البيانات
                foreach (var payroll in _payrolls)
                {
                    if (IncludeDetailsCheckBox.IsChecked == true)
                    {
                        csv.AppendLine($"{payroll.PayrollNumber}," +
                                     $"{payroll.Employee?.FullName ?? ""}," +
                                     $"{payroll.Employee?.EmployeeCode ?? ""}," +
                                     $"{payroll.Employee?.Department ?? ""}," +
                                     $"{payroll.Employee?.JobTitle ?? ""}," +
                                     $"{payroll.MonthName}," +
                                     $"{payroll.Year}," +
                                     $"{payroll.PayrollDate:yyyy/MM/dd}," +
                                     $"{payroll.BasicSalary:N2}," +
                                     $"{payroll.HousingAllowance:N2}," +
                                     $"{payroll.TransportationAllowance:N2}," +
                                     $"{payroll.FoodAllowance:N2}," +
                                     $"{payroll.OtherAllowances:N2}," +
                                     $"{payroll.OvertimeHours:N2}," +
                                     $"{payroll.OvertimePay:N2}," +
                                     $"{payroll.GrossSalary:N2}," +
                                     $"{payroll.SocialInsurance:N2}," +
                                     $"{payroll.IncomeTax:N2}," +
                                     $"{payroll.Loan:N2}," +
                                     $"{payroll.OtherDeductions:N2}," +
                                     $"{payroll.TotalDeductions:N2}," +
                                     $"{payroll.NetSalary:N2}," +
                                     $"{payroll.StatusText}," +
                                     $"\"{payroll.Notes?.Replace("\"", "\"\"")}\"");
                    }
                    else
                    {
                        csv.AppendLine($"{payroll.PayrollNumber}," +
                                     $"{payroll.Employee?.FullName ?? ""}," +
                                     $"{payroll.Employee?.Department ?? ""}," +
                                     $"{payroll.MonthName}," +
                                     $"{payroll.Year}," +
                                     $"{payroll.GrossSalary:N2}," +
                                     $"{payroll.TotalDeductions:N2}," +
                                     $"{payroll.NetSalary:N2}," +
                                     $"{payroll.StatusText}");
                    }
                }

                // حفظ الملف
                File.WriteAllText(fullPath, csv.ToString(), Encoding.UTF8);

                MessageBox.Show($"تم تصدير {_payrolls.Count} راتب بنجاح إلى:\n{fullPath}", 
                    "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
