<Window x:Class="SimpleAccountingApp.Windows.PayrollPrintWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="طباعة الراتب" Height="600" Width="800"
        WindowStartupLocation="CenterScreen" FlowDirection="RightToLeft">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="كشف راتب" 
                   FontSize="24" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- محتوى الراتب -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Name="PayrollContentPanel" Background="White" Margin="20">
                <!-- سيتم ملء المحتوى من الكود -->
            </StackPanel>
        </ScrollViewer>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Name="PrintButton" Content="طباعة" 
                    Width="100" Margin="0,0,10,0" Click="PrintButton_Click"/>
            <Button Name="CloseButton" Content="إغلاق" 
                    Width="100" Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
