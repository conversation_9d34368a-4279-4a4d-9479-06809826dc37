using System.ComponentModel.DataAnnotations;

namespace SimpleAccountingApp.Models
{
    public enum BankAccountType
    {
        Current = 1,        // جاري
        Savings = 2,        // توفير
        FixedDeposit = 3,   // وديعة ثابتة
        Investment = 4      // استثماري
    }

    public class BankAccount
    {
        [Key]
        public int BankAccountId { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string AccountNumber { get; set; } = "";
        
        [Required]
        [MaxLength(200)]
        public string AccountName { get; set; } = "";
        
        [Required]
        [MaxLength(200)]
        public string BankName { get; set; } = "";
        
        [MaxLength(50)]
        public string BranchName { get; set; } = "";
        
        [MaxLength(50)]
        public string IBAN { get; set; } = "";
        
        [MaxLength(50)]
        public string SwiftCode { get; set; } = "";
        
        public BankAccountType AccountType { get; set; } = BankAccountType.Current;
        
        public decimal Balance { get; set; }
        public decimal CreditLimit { get; set; }
        
        [MaxLength(50)]
        public string Currency { get; set; } = "SAR";
        
        public bool IsActive { get; set; } = true;
        
        [MaxLength(1000)]
        public string Notes { get; set; } = "";
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        // خصائص محسوبة
        public string AccountTypeText => GetAccountTypeText(AccountType);
        public string IsActiveText => IsActive ? "نشط" : "غير نشط";
        public decimal AvailableBalance => Balance + CreditLimit;
        
        private static string GetAccountTypeText(BankAccountType type)
        {
            return type switch
            {
                BankAccountType.Current => "جاري",
                BankAccountType.Savings => "توفير",
                BankAccountType.FixedDeposit => "وديعة ثابتة",
                BankAccountType.Investment => "استثماري",
                _ => "غير محدد"
            };
        }
    }
}
