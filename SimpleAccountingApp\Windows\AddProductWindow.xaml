<Window x:Class="SimpleAccountingApp.Windows.AddProductWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة صنف جديد - نظام المحاسبة المالية" 
        Height="700" Width="800"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#9C27B0" Padding="15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📦" FontSize="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock x:Name="WindowTitle" 
                          Text="إضافة صنف جديد" 
                          FontSize="18" 
                          FontWeight="Bold" 
                          Foreground="White" 
                          VerticalAlignment="Center"/>
            </StackPanel>
        </Border>
        
        <!-- Form Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- Right Column -->
                <StackPanel Grid.Column="0" Margin="0,0,15,0">
                    <!-- Product Code -->
                    <TextBlock Text="رقم الصنف *" FontWeight="Bold" Margin="0,10,0,5"/>
                    <TextBox x:Name="ProductCodeTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"/>
                    
                    <!-- Product Name -->
                    <TextBlock Text="اسم الصنف *" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="ProductNameTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"/>
                    
                    <!-- Barcode -->
                    <TextBlock Text="الباركود" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="BarcodeTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"/>
                    
                    <!-- Category -->
                    <TextBlock Text="الفئة *" FontWeight="Bold" Margin="0,15,0,5"/>
                    <ComboBox x:Name="CategoryComboBox" 
                             Height="35" 
                             Padding="10"
                             FontSize="14"
                             IsEditable="True">
                        <ComboBoxItem Content="أجهزة كمبيوتر"/>
                        <ComboBoxItem Content="طابعات"/>
                        <ComboBoxItem Content="مستلزمات مكتبية"/>
                        <ComboBoxItem Content="ملحقات كمبيوتر"/>
                        <ComboBoxItem Content="برمجيات"/>
                        <ComboBoxItem Content="أثاث مكتبي"/>
                    </ComboBox>
                    
                    <!-- Unit -->
                    <TextBlock Text="الوحدة *" FontWeight="Bold" Margin="0,15,0,5"/>
                    <ComboBox x:Name="UnitComboBox" 
                             Height="35" 
                             Padding="10"
                             FontSize="14"
                             IsEditable="True">
                        <ComboBoxItem Content="قطعة"/>
                        <ComboBoxItem Content="علبة"/>
                        <ComboBoxItem Content="كيلو"/>
                        <ComboBoxItem Content="متر"/>
                        <ComboBoxItem Content="لتر"/>
                        <ComboBoxItem Content="طن"/>
                    </ComboBox>
                    
                    <!-- Purchase Price -->
                    <TextBlock Text="سعر الشراء *" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="PurchasePriceTextBox"
                            Height="35"
                            Padding="10"
                            FontSize="14"
                            Text="0"
                            TextChanged="PriceTextBox_TextChanged"/>
                    
                    <!-- Sale Price -->
                    <TextBlock Text="سعر البيع *" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="SalePriceTextBox"
                            Height="35"
                            Padding="10"
                            FontSize="14"
                            Text="0"
                            TextChanged="PriceTextBox_TextChanged"/>
                </StackPanel>
                
                <!-- Left Column -->
                <StackPanel Grid.Column="1" Margin="15,0,0,0">
                    <!-- Current Stock -->
                    <TextBlock Text="الكمية الحالية" FontWeight="Bold" Margin="0,10,0,5"/>
                    <TextBox x:Name="CurrentStockTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"
                            Text="0"/>
                    
                    <!-- Minimum Stock -->
                    <TextBlock Text="الحد الأدنى للمخزون" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="MinimumStockTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"
                            Text="0"/>
                    
                    <!-- Maximum Stock -->
                    <TextBlock Text="الحد الأقصى للمخزون" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="MaximumStockTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"
                            Text="0"/>
                    
                    <!-- Profit Calculation -->
                    <Border Background="#E8F5E8" Padding="10" Margin="0,15,0,0" CornerRadius="5">
                        <StackPanel>
                            <TextBlock Text="حساب الربح" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                            <TextBlock x:Name="ProfitMarginText" Text="هامش الربح: 0.00 ريال" FontSize="14"/>
                            <TextBlock x:Name="ProfitPercentageText" Text="نسبة الربح: 0.00%" FontSize="14" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Description -->
                    <TextBlock Text="الوصف" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="DescriptionTextBox" 
                            Height="80" 
                            Padding="10"
                            FontSize="14"
                            TextWrapping="Wrap"
                            AcceptsReturn="True"
                            VerticalScrollBarVisibility="Auto"/>
                </StackPanel>
            </Grid>
        </ScrollViewer>
        
        <!-- Bottom Section -->
        <Border Grid.Row="2" Background="#F5F5F5" Padding="15" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- Is Active Checkbox -->
                <CheckBox x:Name="IsActiveCheckBox" 
                         Grid.Column="0"
                         Content="صنف نشط" 
                         IsChecked="True" 
                         VerticalAlignment="Center"
                         FontWeight="Bold"/>
                
                <!-- Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="SaveButton" 
                           Content="💾 حفظ" 
                           Background="#4CAF50" 
                           Foreground="White" 
                           BorderThickness="0"
                           Padding="20,10" 
                           Margin="10,0"
                           FontSize="14"
                           FontWeight="Bold"
                           Click="SaveButton_Click"/>
                    
                    <Button x:Name="CancelButton" 
                           Content="❌ إلغاء" 
                           Background="#F44336" 
                           Foreground="White" 
                           BorderThickness="0"
                           Padding="20,10" 
                           Margin="10,0"
                           FontSize="14"
                           FontWeight="Bold"
                           Click="CancelButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
