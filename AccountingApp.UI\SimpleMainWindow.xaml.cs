using System.Windows;
using System.Windows.Threading;

namespace AccountingApp.UI
{
    public partial class SimpleMainWindow : Window
    {
        private readonly DispatcherTimer _timer;

        public SimpleMainWindow()
        {
            InitializeComponent();
            
            // إعداد المؤقت لعرض التاريخ والوقت
            _timer = new DispatcherTimer();
            _timer.Interval = TimeSpan.FromSeconds(1);
            _timer.Tick += Timer_Tick;
            _timer.Start();
            
            // عرض التاريخ والوقت فوراً
            UpdateDateTime();
        }

        private void Timer_Tick(object? sender, EventArgs e)
        {
            UpdateDateTime();
        }

        private void UpdateDateTime()
        {
            DateTimeTextBlock.Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm:ss");
        }

        private void DashboardButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("مرحباً بك في لوحة التحكم!", "لوحة التحكم", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من تسجيل الخروج؟",
                "تأكيد تسجيل الخروج",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                var loginWindow = new SimpleLoginWindow();
                loginWindow.Show();
                this.Close();
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _timer?.Stop();
            base.OnClosed(e);
        }
    }
}
