using System.ComponentModel.DataAnnotations;

namespace SimpleAccountingApp.Models
{
    public enum EmployeeStatus
    {
        Active = 1,        // نشط
        Inactive = 2,      // غير نشط
        OnLeave = 3,       // في إجازة
        Terminated = 4     // منتهي الخدمة
    }

    public enum MaritalStatus
    {
        Single = 1,        // أعزب
        Married = 2,       // متزوج
        Divorced = 3,      // مطلق
        Widowed = 4        // أرمل
    }

    public class Employee
    {
        [Key]
        public int EmployeeId { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string EmployeeCode { get; set; } = "";
        
        [Required]
        [MaxLength(100)]
        public string FirstName { get; set; } = "";
        
        [Required]
        [MaxLength(100)]
        public string LastName { get; set; } = "";
        
        [MaxLength(50)]
        public string NationalId { get; set; } = "";
        
        public DateTime DateOfBirth { get; set; }
        
        [MaxLength(10)]
        public string Gender { get; set; } = "";
        
        public MaritalStatus MaritalStatus { get; set; } = MaritalStatus.Single;
        
        [MaxLength(50)]
        public string Phone { get; set; } = "";
        
        [MaxLength(50)]
        public string Mobile { get; set; } = "";
        
        [MaxLength(100)]
        public string Email { get; set; } = "";
        
        [MaxLength(500)]
        public string Address { get; set; } = "";
        
        [MaxLength(100)]
        public string City { get; set; } = "";
        
        [MaxLength(100)]
        public string Country { get; set; } = "السعودية";
        
        [Required]
        [MaxLength(200)]
        public string JobTitle { get; set; } = "";
        
        [Required]
        [MaxLength(200)]
        public string Department { get; set; } = "";
        
        public DateTime HireDate { get; set; } = DateTime.Now;
        
        public DateTime? TerminationDate { get; set; }
        
        public decimal BasicSalary { get; set; }
        
        public decimal Allowances { get; set; }
        
        public decimal Deductions { get; set; }
        
        public EmployeeStatus Status { get; set; } = EmployeeStatus.Active;
        
        [MaxLength(1000)]
        public string Notes { get; set; } = "";
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        // خصائص محسوبة
        public string FullName => $"{FirstName} {LastName}";
        public string StatusText => GetStatusText(Status);
        public string MaritalStatusText => GetMaritalStatusText(MaritalStatus);
        public decimal NetSalary => BasicSalary + Allowances - Deductions;
        public int Age => DateTime.Now.Year - DateOfBirth.Year;
        public int YearsOfService => DateTime.Now.Year - HireDate.Year;
        
        private static string GetStatusText(EmployeeStatus status)
        {
            return status switch
            {
                EmployeeStatus.Active => "نشط",
                EmployeeStatus.Inactive => "غير نشط",
                EmployeeStatus.OnLeave => "في إجازة",
                EmployeeStatus.Terminated => "منتهي الخدمة",
                _ => "غير محدد"
            };
        }
        
        private static string GetMaritalStatusText(MaritalStatus status)
        {
            return status switch
            {
                MaritalStatus.Single => "أعزب",
                MaritalStatus.Married => "متزوج",
                MaritalStatus.Divorced => "مطلق",
                MaritalStatus.Widowed => "أرمل",
                _ => "غير محدد"
            };
        }
    }
}
