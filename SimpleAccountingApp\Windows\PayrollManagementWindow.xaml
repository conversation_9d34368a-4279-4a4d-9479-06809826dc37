<Window x:Class="SimpleAccountingApp.Windows.PayrollManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الرواتب الشاملة" Height="700" Width="1200"
        WindowStartupLocation="CenterScreen" FlowDirection="RightToLeft">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="إدارة الرواتب الشاملة" 
                   FontSize="24" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- فلاتر البحث -->
        <Border Grid.Row="1" BorderBrush="Gray" BorderThickness="1" 
                Padding="15" Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal">
                <Label Content="الشهر:" FontWeight="Bold" VerticalAlignment="Center"/>
                <ComboBox Name="MonthFilterComboBox" Width="120" Margin="5,0,20,0">
                    <ComboBoxItem Content="جميع الشهور" Tag="0" IsSelected="True"/>
                    <ComboBoxItem Content="يناير" Tag="1"/>
                    <ComboBoxItem Content="فبراير" Tag="2"/>
                    <ComboBoxItem Content="مارس" Tag="3"/>
                    <ComboBoxItem Content="أبريل" Tag="4"/>
                    <ComboBoxItem Content="مايو" Tag="5"/>
                    <ComboBoxItem Content="يونيو" Tag="6"/>
                    <ComboBoxItem Content="يوليو" Tag="7"/>
                    <ComboBoxItem Content="أغسطس" Tag="8"/>
                    <ComboBoxItem Content="سبتمبر" Tag="9"/>
                    <ComboBoxItem Content="أكتوبر" Tag="10"/>
                    <ComboBoxItem Content="نوفمبر" Tag="11"/>
                    <ComboBoxItem Content="ديسمبر" Tag="12"/>
                </ComboBox>

                <Label Content="السنة:" FontWeight="Bold" VerticalAlignment="Center"/>
                <ComboBox Name="YearFilterComboBox" Width="100" Margin="5,0,20,0"/>

                <Label Content="القسم:" FontWeight="Bold" VerticalAlignment="Center"/>
                <ComboBox Name="DepartmentFilterComboBox" Width="150" Margin="5,0,20,0"/>

                <Button Name="FilterButton" Content="بحث" Width="80" Margin="5,0,10,0" Click="FilterButton_Click"/>
                <Button Name="ClearFilterButton" Content="مسح" Width="80" Margin="5,0,0,0" Click="ClearFilterButton_Click"/>
            </StackPanel>
        </Border>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="0,0,0,20">
            <Button Name="GeneratePayrollButton" Content="إنشاء رواتب الشهر" 
                    Width="150" Click="GeneratePayrollButton_Click"/>
            <Button Name="AddPayrollButton" Content="إضافة راتب" 
                    Width="120" Margin="10,0,0,0" Click="AddPayrollButton_Click"/>
            <Button Name="EditPayrollButton" Content="تعديل" 
                    Width="100" Margin="10,0,0,0" Click="EditPayrollButton_Click"/>
            <Button Name="DeletePayrollButton" Content="حذف" 
                    Width="100" Margin="10,0,0,0" Click="DeletePayrollButton_Click"/>
            <Button Name="PrintPayrollButton" Content="طباعة" 
                    Width="100" Margin="10,0,0,0" Click="PrintPayrollButton_Click"/>
            <Button Name="ExportPayrollButton" Content="تصدير Excel" 
                    Width="120" Margin="10,0,0,0" Click="ExportPayrollButton_Click"/>
            <Button Name="RefreshButton" Content="تحديث" 
                    Width="100" Margin="10,0,0,0" Click="RefreshButton_Click"/>
        </StackPanel>

        <!-- جدول الرواتب -->
        <DataGrid Grid.Row="3" Name="PayrollsDataGrid" 
                  AutoGenerateColumns="False" 
                  CanUserAddRows="False" 
                  CanUserDeleteRows="False"
                  IsReadOnly="True"
                  GridLinesVisibility="All"
                  HeadersVisibility="Column"
                  SelectionMode="Single">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="رقم الراتب" Binding="{Binding PayrollNumber}" Width="120"/>
                <DataGridTextColumn Header="الموظف" Binding="{Binding Employee.FullName}" Width="150"/>
                <DataGridTextColumn Header="القسم" Binding="{Binding Employee.Department}" Width="120"/>
                <DataGridTextColumn Header="المسمى" Binding="{Binding Employee.JobTitle}" Width="150"/>
                <DataGridTextColumn Header="الشهر" Binding="{Binding MonthName}" Width="80"/>
                <DataGridTextColumn Header="السنة" Binding="{Binding Year}" Width="80"/>
                <DataGridTextColumn Header="التاريخ" Binding="{Binding PayrollDate, StringFormat='{}{0:yyyy/MM/dd}'}" Width="100"/>
                <DataGridTextColumn Header="الراتب الأساسي" Binding="{Binding BasicSalary, StringFormat='{}{0:N2} ريال'}" Width="120"/>
                <DataGridTextColumn Header="البدلات" Binding="{Binding TotalAllowances, StringFormat='{}{0:N2} ريال'}" Width="100"/>
                <DataGridTextColumn Header="الإضافي" Binding="{Binding OvertimePay, StringFormat='{}{0:N2} ريال'}" Width="100"/>
                <DataGridTextColumn Header="الإجمالي" Binding="{Binding GrossSalary, StringFormat='{}{0:N2} ريال'}" Width="120"/>
                <DataGridTextColumn Header="الخصومات" Binding="{Binding TotalDeductions, StringFormat='{}{0:N2} ريال'}" Width="120"/>
                <DataGridTextColumn Header="الصافي" Binding="{Binding NetSalary, StringFormat='{}{0:N2} ريال'}" Width="120"/>
                <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="100"/>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</Window>
