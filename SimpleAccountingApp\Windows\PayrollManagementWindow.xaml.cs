using System.Collections.ObjectModel;
using System.Windows;
using SimpleAccountingApp.Models;
using SimpleAccountingApp.Data;
using Microsoft.EntityFrameworkCore;
using System.Windows.Controls;

namespace SimpleAccountingApp.Windows
{
    public partial class PayrollManagementWindow : Window
    {
        private ObservableCollection<Payroll> payrolls = new();
        private AccountingDbContext _context;

        public PayrollManagementWindow()
        {
            InitializeComponent();
            _context = new AccountingDbContext();
            InitializeWindow();
            LoadPayrolls();
        }

        private async void InitializeWindow()
        {
            // تحميل السنوات
            YearFilterComboBox.Items.Clear();
            var currentYear = DateTime.Now.Year;
            for (int year = currentYear - 5; year <= currentYear + 1; year++)
            {
                var item = new ComboBoxItem { Content = year.ToString(), Tag = year };
                if (year == currentYear)
                    item.IsSelected = true;
                YearFilterComboBox.Items.Add(item);
            }

            // تحميل الأقسام
            await LoadDepartments();
        }

        private async Task LoadDepartments()
        {
            try
            {
                await _context.InitializeDatabaseAsync();
                var departments = await _context.Employees
                    .Where(e => e.Status == EmployeeStatus.Active)
                    .Select(e => e.Department)
                    .Distinct()
                    .OrderBy(d => d)
                    .ToListAsync();

                DepartmentFilterComboBox.Items.Clear();
                DepartmentFilterComboBox.Items.Add(new ComboBoxItem { Content = "جميع الأقسام", Tag = "", IsSelected = true });
                
                foreach (var dept in departments)
                {
                    DepartmentFilterComboBox.Items.Add(new ComboBoxItem { Content = dept, Tag = dept });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الأقسام: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoadPayrolls()
        {
            try
            {
                await _context.InitializeDatabaseAsync();
                var payrollsList = await _context.Payrolls
                    .Include(p => p.Employee)
                    .OrderByDescending(p => p.Year)
                    .ThenByDescending(p => p.Month)
                    .ThenBy(p => p.Employee!.Department)
                    .ThenBy(p => p.Employee!.FirstName)
                    .ToListAsync();
                
                payrolls.Clear();
                foreach (var payroll in payrollsList)
                {
                    payrolls.Add(payroll);
                }

                PayrollsDataGrid.ItemsSource = payrolls;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الرواتب: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void FilterButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var query = _context.Payrolls.Include(p => p.Employee).AsQueryable();

                // فلتر الشهر
                if (MonthFilterComboBox.SelectedItem is ComboBoxItem monthItem && 
                    int.Parse(monthItem.Tag.ToString()!) > 0)
                {
                    var month = int.Parse(monthItem.Tag.ToString()!);
                    query = query.Where(p => p.Month == month);
                }

                // فلتر السنة
                if (YearFilterComboBox.SelectedItem is ComboBoxItem yearItem)
                {
                    var year = int.Parse(yearItem.Tag.ToString()!);
                    query = query.Where(p => p.Year == year);
                }

                // فلتر القسم
                if (DepartmentFilterComboBox.SelectedItem is ComboBoxItem deptItem && 
                    !string.IsNullOrEmpty(deptItem.Tag.ToString()))
                {
                    var department = deptItem.Tag.ToString()!;
                    query = query.Where(p => p.Employee!.Department == department);
                }

                var filteredPayrolls = await query
                    .OrderByDescending(p => p.Year)
                    .ThenByDescending(p => p.Month)
                    .ThenBy(p => p.Employee!.Department)
                    .ThenBy(p => p.Employee!.FirstName)
                    .ToListAsync();

                payrolls.Clear();
                foreach (var payroll in filteredPayrolls)
                {
                    payrolls.Add(payroll);
                }

                PayrollsDataGrid.Items.Refresh();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearFilterButton_Click(object sender, RoutedEventArgs e)
        {
            MonthFilterComboBox.SelectedIndex = 0;
            YearFilterComboBox.SelectedIndex = YearFilterComboBox.Items.Count - 2; // السنة الحالية
            DepartmentFilterComboBox.SelectedIndex = 0;
            LoadPayrolls();
        }

        private async void GeneratePayrollButton_Click(object sender, RoutedEventArgs e)
        {
            var generateWindow = new GeneratePayrollWindow();
            if (generateWindow.ShowDialog() == true)
            {
                LoadPayrolls();
            }
        }

        private async void AddPayrollButton_Click(object sender, RoutedEventArgs e)
        {
            var selectEmployeeWindow = new SelectEmployeeWindow();
            if (selectEmployeeWindow.ShowDialog() == true && selectEmployeeWindow.SelectedEmployee != null)
            {
                var payrollWindow = new AddPayrollWindow(selectEmployeeWindow.SelectedEmployee);
                if (payrollWindow.ShowDialog() == true)
                {
                    var newPayroll = payrollWindow.NewPayroll;
                    if (newPayroll != null)
                    {
                        try
                        {
                            _context.Payrolls.Add(newPayroll);
                            await _context.SaveChangesAsync();

                            LoadPayrolls();

                            MessageBox.Show("تم إضافة الراتب بنجاح!", "نجح", 
                                MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في حفظ الراتب: {ex.Message}", "خطأ", 
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
        }

        private async void EditPayrollButton_Click(object sender, RoutedEventArgs e)
        {
            if (PayrollsDataGrid.SelectedItem is Payroll selectedPayroll)
            {
                var payrollWindow = new AddPayrollWindow(selectedPayroll.Employee!, selectedPayroll);
                if (payrollWindow.ShowDialog() == true)
                {
                    try
                    {
                        _context.Payrolls.Update(selectedPayroll);
                        await _context.SaveChangesAsync();

                        PayrollsDataGrid.Items.Refresh();

                        MessageBox.Show("تم تعديل الراتب بنجاح!", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في تحديث الراتب: {ex.Message}", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار راتب للتعديل.", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void DeletePayrollButton_Click(object sender, RoutedEventArgs e)
        {
            if (PayrollsDataGrid.SelectedItem is Payroll selectedPayroll)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف راتب {selectedPayroll.Employee?.FullName} لشهر {selectedPayroll.MonthName} {selectedPayroll.Year}؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        _context.Payrolls.Remove(selectedPayroll);
                        await _context.SaveChangesAsync();

                        payrolls.Remove(selectedPayroll);
                        PayrollsDataGrid.Items.Refresh();

                        MessageBox.Show("تم حذف الراتب بنجاح!", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الراتب: {ex.Message}", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار راتب للحذف.", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void PrintPayrollButton_Click(object sender, RoutedEventArgs e)
        {
            if (PayrollsDataGrid.SelectedItem is Payroll selectedPayroll)
            {
                var printWindow = new PayrollPrintWindow(selectedPayroll);
                printWindow.ShowDialog();
            }
            else
            {
                MessageBox.Show("يرجى اختيار راتب للطباعة.", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void ExportPayrollButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var exportWindow = new PayrollExportWindow(payrolls.ToList());
                exportWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadPayrolls();
        }
    }
}
