<Window x:Class="SimpleAccountingApp.Windows.FinancialReportsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="التقارير المالية - نظام المحاسبة المالية" 
        Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#3F51B5" Padding="15">
            <DockPanel>
                <Button DockPanel.Dock="Left" 
                       Content="✖ إغلاق" 
                       Background="#303F9F" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,5" 
                       Click="CloseButton_Click"/>
                
                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                    <TextBlock Text="📈" FontSize="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="التقارير المالية" 
                              FontSize="20" 
                              FontWeight="Bold" 
                              Foreground="White" 
                              VerticalAlignment="Center"/>
                </StackPanel>
            </DockPanel>
        </Border>
        
        <!-- Reports Grid -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- Financial Reports Section -->
                <Border Grid.Row="0" Background="#E8EAF6" Padding="15" Margin="0,0,0,20" CornerRadius="5">
                    <StackPanel>
                        <TextBlock Text="📊 التقارير المالية الأساسية" FontSize="18" FontWeight="Bold" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Income Statement -->
                            <Border Grid.Column="0" Background="White" Padding="20" Margin="5" CornerRadius="5" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="📋" FontSize="32" HorizontalAlignment="Center" Foreground="#4CAF50"/>
                                    <TextBlock Text="قائمة الدخل" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                    <TextBlock Text="الإيرادات والمصروفات" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="IncomeStatementButton" 
                                           Content="عرض التقرير" 
                                           Background="#4CAF50" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           Click="IncomeStatementButton_Click"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- Balance Sheet -->
                            <Border Grid.Column="1" Background="White" Padding="20" Margin="5" CornerRadius="5" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="⚖️" FontSize="32" HorizontalAlignment="Center" Foreground="#2196F3"/>
                                    <TextBlock Text="الميزانية العمومية" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                    <TextBlock Text="الأصول والخصوم" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="BalanceSheetButton" 
                                           Content="عرض التقرير" 
                                           Background="#2196F3" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           Click="BalanceSheetButton_Click"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- Cash Flow -->
                            <Border Grid.Column="2" Background="White" Padding="20" Margin="5" CornerRadius="5" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="💰" FontSize="32" HorizontalAlignment="Center" Foreground="#FF9800"/>
                                    <TextBlock Text="قائمة التدفقات النقدية" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                    <TextBlock Text="التدفقات الداخلة والخارجة" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="CashFlowButton" 
                                           Content="عرض التقرير" 
                                           Background="#FF9800" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           Click="CashFlowButton_Click"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </StackPanel>
                </Border>
                
                <!-- Sales Reports Section -->
                <Border Grid.Row="1" Background="#E8F5E8" Padding="15" Margin="0,0,0,20" CornerRadius="5">
                    <StackPanel>
                        <TextBlock Text="🧾 تقارير المبيعات" FontSize="18" FontWeight="Bold" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Sales Summary -->
                            <Border Grid.Column="0" Background="White" Padding="20" Margin="5" CornerRadius="5" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="📊" FontSize="32" HorizontalAlignment="Center" Foreground="#4CAF50"/>
                                    <TextBlock Text="ملخص المبيعات" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                    <TextBlock Text="إجمالي المبيعات والأرباح" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="SalesSummaryButton" 
                                           Content="عرض التقرير" 
                                           Background="#4CAF50" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           Click="SalesSummaryButton_Click"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- Customer Sales -->
                            <Border Grid.Column="1" Background="White" Padding="20" Margin="5" CornerRadius="5" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="👥" FontSize="32" HorizontalAlignment="Center" Foreground="#4CAF50"/>
                                    <TextBlock Text="مبيعات العملاء" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                    <TextBlock Text="تفصيل مبيعات كل عميل" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="CustomerSalesButton" 
                                           Content="عرض التقرير" 
                                           Background="#4CAF50" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           Click="CustomerSalesButton_Click"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- Product Sales -->
                            <Border Grid.Column="2" Background="White" Padding="20" Margin="5" CornerRadius="5" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="📦" FontSize="32" HorizontalAlignment="Center" Foreground="#4CAF50"/>
                                    <TextBlock Text="مبيعات الأصناف" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                    <TextBlock Text="أداء الأصناف في المبيعات" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="ProductSalesButton" 
                                           Content="عرض التقرير" 
                                           Background="#4CAF50" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           Click="ProductSalesButton_Click"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </StackPanel>
                </Border>
                
                <!-- Purchase Reports Section -->
                <Border Grid.Row="2" Background="#FFF3E0" Padding="15" Margin="0,0,0,20" CornerRadius="5">
                    <StackPanel>
                        <TextBlock Text="📋 تقارير المشتريات" FontSize="18" FontWeight="Bold" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Purchase Summary -->
                            <Border Grid.Column="0" Background="White" Padding="20" Margin="5" CornerRadius="5" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="📊" FontSize="32" HorizontalAlignment="Center" Foreground="#FF9800"/>
                                    <TextBlock Text="ملخص المشتريات" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                    <TextBlock Text="إجمالي المشتريات والتكاليف" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="PurchaseSummaryButton" 
                                           Content="عرض التقرير" 
                                           Background="#FF9800" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           Click="PurchaseSummaryButton_Click"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- Supplier Purchases -->
                            <Border Grid.Column="1" Background="White" Padding="20" Margin="5" CornerRadius="5" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="🏢" FontSize="32" HorizontalAlignment="Center" Foreground="#FF9800"/>
                                    <TextBlock Text="مشتريات الموردين" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                    <TextBlock Text="تفصيل مشتريات كل مورد" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="SupplierPurchasesButton" 
                                           Content="عرض التقرير" 
                                           Background="#FF9800" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           Click="SupplierPurchasesButton_Click"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- Accounts Payable -->
                            <Border Grid.Column="2" Background="White" Padding="20" Margin="5" CornerRadius="5" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="💳" FontSize="32" HorizontalAlignment="Center" Foreground="#FF9800"/>
                                    <TextBlock Text="الحسابات الدائنة" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                    <TextBlock Text="المبالغ المستحقة للموردين" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="AccountsPayableButton" 
                                           Content="عرض التقرير" 
                                           Background="#FF9800" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           Click="AccountsPayableButton_Click"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </StackPanel>
                </Border>
                
                <!-- Inventory Reports Section -->
                <Border Grid.Row="3" Background="#F3E5F5" Padding="15" CornerRadius="5">
                    <StackPanel>
                        <TextBlock Text="📦 تقارير المخزون" FontSize="18" FontWeight="Bold" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Stock Valuation -->
                            <Border Grid.Column="0" Background="White" Padding="20" Margin="5" CornerRadius="5" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="💰" FontSize="32" HorizontalAlignment="Center" Foreground="#9C27B0"/>
                                    <TextBlock Text="تقييم المخزون" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                    <TextBlock Text="قيمة المخزون الحالية" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="StockValuationButton" 
                                           Content="عرض التقرير" 
                                           Background="#9C27B0" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           Click="StockValuationButton_Click"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- Stock Movement -->
                            <Border Grid.Column="1" Background="White" Padding="20" Margin="5" CornerRadius="5" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="🔄" FontSize="32" HorizontalAlignment="Center" Foreground="#9C27B0"/>
                                    <TextBlock Text="حركة المخزون" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                    <TextBlock Text="تفصيل حركة الأصناف" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="StockMovementReportButton" 
                                           Content="عرض التقرير" 
                                           Background="#9C27B0" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           Click="StockMovementReportButton_Click"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- Low Stock -->
                            <Border Grid.Column="2" Background="White" Padding="20" Margin="5" CornerRadius="5" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="⚠️" FontSize="32" HorizontalAlignment="Center" Foreground="#9C27B0"/>
                                    <TextBlock Text="المخزون المنخفض" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                    <TextBlock Text="الأصناف التي تحتاج تجديد" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="LowStockButton" 
                                           Content="عرض التقرير" 
                                           Background="#9C27B0" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           Click="LowStockButton_Click"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </StackPanel>
                </Border>
            </Grid>
        </ScrollViewer>
    </Grid>
</Window>
