using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using SimpleAccountingApp.Data;
using SimpleAccountingApp.Models;
using Microsoft.EntityFrameworkCore;

namespace SimpleAccountingApp.Windows
{
    public partial class BankAccountsWindow : Window
    {
        private ObservableCollection<BankAccount> bankAccounts = new();
        private AccountingDbContext _context;

        public BankAccountsWindow()
        {
            InitializeComponent();
            _context = new AccountingDbContext();
            LoadBankAccounts();
        }

        private async void LoadBankAccounts()
        {
            try
            {
                await _context.InitializeDatabaseAsync();
                var accountsList = await _context.BankAccounts
                    .Where(b => b.IsActive)
                    .OrderBy(b => b.BankName)
                    .ThenBy(b => b.AccountName)
                    .ToListAsync();
                
                bankAccounts.Clear();
                foreach (var account in accountsList)
                {
                    bankAccounts.Add(account);
                }

                BankAccountsDataGrid.ItemsSource = bankAccounts;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل حسابات البنوك: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void AddBankAccountButton_Click(object sender, RoutedEventArgs e)
        {
            var addBankAccountWindow = new AddBankAccountWindow();
            if (addBankAccountWindow.ShowDialog() == true)
            {
                var newBankAccount = addBankAccountWindow.NewBankAccount;
                if (newBankAccount != null)
                {
                    try
                    {
                        _context.BankAccounts.Add(newBankAccount);
                        await _context.SaveChangesAsync();

                        bankAccounts.Add(newBankAccount);
                        RefreshBankAccountsGrid();

                        MessageBox.Show("تم إضافة الحساب البنكي بنجاح!", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حفظ الحساب البنكي: {ex.Message}", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private async void EditBankAccountButton_Click(object sender, RoutedEventArgs e)
        {
            if (BankAccountsDataGrid.SelectedItem is BankAccount selectedAccount)
            {
                var editBankAccountWindow = new AddBankAccountWindow(selectedAccount);
                if (editBankAccountWindow.ShowDialog() == true)
                {
                    try
                    {
                        _context.BankAccounts.Update(selectedAccount);
                        await _context.SaveChangesAsync();

                        RefreshBankAccountsGrid();

                        MessageBox.Show("تم تعديل الحساب البنكي بنجاح!", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في تحديث الحساب البنكي: {ex.Message}", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار حساب بنكي للتعديل.", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void DeleteBankAccountButton_Click(object sender, RoutedEventArgs e)
        {
            if (BankAccountsDataGrid.SelectedItem is BankAccount selectedAccount)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الحساب البنكي '{selectedAccount.AccountName}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        _context.BankAccounts.Remove(selectedAccount);
                        await _context.SaveChangesAsync();

                        bankAccounts.Remove(selectedAccount);
                        RefreshBankAccountsGrid();

                        MessageBox.Show("تم حذف الحساب البنكي بنجاح!", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الحساب البنكي: {ex.Message}", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار حساب بنكي للحذف.", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void ViewTransactionsButton_Click(object sender, RoutedEventArgs e)
        {
            if (BankAccountsDataGrid.SelectedItem is BankAccount selectedAccount)
            {
                var transactionsWindow = new BankTransactionsWindow(selectedAccount);
                transactionsWindow.ShowDialog();
            }
            else
            {
                MessageBox.Show("يرجى اختيار حساب بنكي لعرض العمليات.", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadBankAccounts();
        }

        private void RefreshBankAccountsGrid()
        {
            BankAccountsDataGrid.Items.Refresh();
        }
    }
}
