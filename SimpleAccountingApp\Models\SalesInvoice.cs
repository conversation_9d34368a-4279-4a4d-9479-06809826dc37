using System.ComponentModel.DataAnnotations;

namespace SimpleAccountingApp.Models
{
    public enum InvoiceStatus
    {
        Draft = 1,      // مسودة
        Confirmed = 2,  // مؤكدة
        Paid = 3,       // مدفوعة
        PartiallyPaid = 4, // مدفوعة جزئياً
        Cancelled = 5   // ملغاة
    }

    public class SalesInvoice
    {
        [Key]
        public int InvoiceId { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string InvoiceNumber { get; set; } = "";
        
        public DateTime InvoiceDate { get; set; } = DateTime.Now;
        
        [Required]
        [MaxLength(200)]
        public string CustomerName { get; set; } = "";
        
        public decimal SubTotal { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        
        public InvoiceStatus Status { get; set; } = InvoiceStatus.Draft;
        
        [MaxLength(50)]
        public string PaymentMethod { get; set; } = "";
        
        [MaxLength(1000)]
        public string Notes { get; set; } = "";
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public string StatusText => GetStatusText(Status);
        
        private static string GetStatusText(InvoiceStatus status)
        {
            return status switch
            {
                InvoiceStatus.Draft => "مسودة",
                InvoiceStatus.Confirmed => "مؤكدة",
                InvoiceStatus.Paid => "مدفوعة",
                InvoiceStatus.PartiallyPaid => "مدفوعة جزئياً",
                InvoiceStatus.Cancelled => "ملغاة",
                _ => "غير محدد"
            };
        }
    }
}
