using System.Windows;
using System.Windows.Controls;
using SimpleAccountingApp.Models;

namespace SimpleAccountingApp.Windows
{
    public partial class AddPayrollWindow : Window
    {
        public Payroll? NewPayroll { get; private set; }
        private bool _isEditMode = false;
        private Payroll? _editingPayroll;
        private Employee _employee;

        public AddPayrollWindow(Employee employee)
        {
            InitializeComponent();
            _employee = employee;
            _isEditMode = false;
            InitializeWindow();
        }

        public AddPayrollWindow(Employee employee, Payroll payroll)
        {
            InitializeComponent();
            _employee = employee;
            _isEditMode = true;
            _editingPayroll = payroll;
            TitleTextBlock.Text = "تعديل راتب";
            InitializeWindow();
            LoadPayrollData();
        }

        private void InitializeWindow()
        {
            // إنشاء رقم راتب تلقائي
            if (!_isEditMode)
            {
                PayrollNumberTextBox.Text = GeneratePayrollNumber();
            }

            // تحديد التاريخ الحالي
            PayrollDatePicker.SelectedDate = DateTime.Now;
            YearTextBox.Text = DateTime.Now.Year.ToString();

            // تحديد الشهر الحالي
            var currentMonth = DateTime.Now.Month;
            foreach (ComboBoxItem item in MonthComboBox.Items)
            {
                if (int.Parse(item.Tag.ToString()!) == currentMonth)
                {
                    MonthComboBox.SelectedItem = item;
                    break;
                }
            }

            // تحميل بيانات الموظف الافتراضية
            BasicSalaryTextBox.Text = _employee.BasicSalary.ToString();
            
            // إضافة مستمعين للأحداث لحساب الراتب
            AddCalculationEventHandlers();
            CalculateSalary();
        }

        private void AddCalculationEventHandlers()
        {
            BasicSalaryTextBox.TextChanged += (s, e) => CalculateSalary();
            HousingAllowanceTextBox.TextChanged += (s, e) => CalculateSalary();
            TransportationAllowanceTextBox.TextChanged += (s, e) => CalculateSalary();
            FoodAllowanceTextBox.TextChanged += (s, e) => CalculateSalary();
            OtherAllowancesTextBox.TextChanged += (s, e) => CalculateSalary();
            OvertimeHoursTextBox.TextChanged += (s, e) => CalculateSalary();
            OvertimeRateTextBox.TextChanged += (s, e) => CalculateSalary();
            SocialInsuranceTextBox.TextChanged += (s, e) => CalculateSalary();
            IncomeTaxTextBox.TextChanged += (s, e) => CalculateSalary();
            LoanTextBox.TextChanged += (s, e) => CalculateSalary();
            OtherDeductionsTextBox.TextChanged += (s, e) => CalculateSalary();
        }

        private void CalculateSalary()
        {
            try
            {
                decimal basicSalary = GetDecimalValue(BasicSalaryTextBox.Text);
                decimal housingAllowance = GetDecimalValue(HousingAllowanceTextBox.Text);
                decimal transportationAllowance = GetDecimalValue(TransportationAllowanceTextBox.Text);
                decimal foodAllowance = GetDecimalValue(FoodAllowanceTextBox.Text);
                decimal otherAllowances = GetDecimalValue(OtherAllowancesTextBox.Text);
                decimal overtimeHours = GetDecimalValue(OvertimeHoursTextBox.Text);
                decimal overtimeRate = GetDecimalValue(OvertimeRateTextBox.Text);
                decimal socialInsurance = GetDecimalValue(SocialInsuranceTextBox.Text);
                decimal incomeTax = GetDecimalValue(IncomeTaxTextBox.Text);
                decimal loan = GetDecimalValue(LoanTextBox.Text);
                decimal otherDeductions = GetDecimalValue(OtherDeductionsTextBox.Text);

                decimal totalAllowances = housingAllowance + transportationAllowance + foodAllowance + otherAllowances;
                decimal overtimePay = overtimeHours * overtimeRate;
                decimal grossSalary = basicSalary + totalAllowances + overtimePay;
                decimal totalDeductions = socialInsurance + incomeTax + loan + otherDeductions;
                decimal netSalary = grossSalary - totalDeductions;

                SummaryTextBlock.Text = $"الراتب الأساسي: {basicSalary:N2} ريال\n" +
                                       $"إجمالي البدلات: {totalAllowances:N2} ريال\n" +
                                       $"أجر الساعات الإضافية: {overtimePay:N2} ريال\n" +
                                       $"الراتب الإجمالي: {grossSalary:N2} ريال\n" +
                                       $"إجمالي الخصومات: {totalDeductions:N2} ريال\n" +
                                       $"الراتب الصافي: {netSalary:N2} ريال";
            }
            catch
            {
                // تجاهل الأخطاء أثناء الحساب
            }
        }

        private decimal GetDecimalValue(string text)
        {
            return decimal.TryParse(text, out decimal value) ? value : 0;
        }

        private string GeneratePayrollNumber()
        {
            return $"PAY{DateTime.Now:yyyyMMddHHmmss}";
        }

        private void LoadPayrollData()
        {
            if (_editingPayroll != null)
            {
                PayrollNumberTextBox.Text = _editingPayroll.PayrollNumber;
                PayrollDatePicker.SelectedDate = _editingPayroll.PayrollDate;
                YearTextBox.Text = _editingPayroll.Year.ToString();
                BasicSalaryTextBox.Text = _editingPayroll.BasicSalary.ToString();
                HousingAllowanceTextBox.Text = _editingPayroll.HousingAllowance.ToString();
                TransportationAllowanceTextBox.Text = _editingPayroll.TransportationAllowance.ToString();
                FoodAllowanceTextBox.Text = _editingPayroll.FoodAllowance.ToString();
                OtherAllowancesTextBox.Text = _editingPayroll.OtherAllowances.ToString();
                OvertimeHoursTextBox.Text = _editingPayroll.OvertimeHours.ToString();
                OvertimeRateTextBox.Text = _editingPayroll.OvertimeRate.ToString();
                SocialInsuranceTextBox.Text = _editingPayroll.SocialInsurance.ToString();
                IncomeTaxTextBox.Text = _editingPayroll.IncomeTax.ToString();
                LoanTextBox.Text = _editingPayroll.Loan.ToString();
                OtherDeductionsTextBox.Text = _editingPayroll.OtherDeductions.ToString();
                NotesTextBox.Text = _editingPayroll.Notes;

                // تحديد الشهر
                foreach (ComboBoxItem item in MonthComboBox.Items)
                {
                    if (int.Parse(item.Tag.ToString()!) == _editingPayroll.Month)
                    {
                        MonthComboBox.SelectedItem = item;
                        break;
                    }
                }

                // تحديد الحالة
                foreach (ComboBoxItem item in StatusComboBox.Items)
                {
                    if (item.Tag.ToString() == _editingPayroll.Status.ToString())
                    {
                        StatusComboBox.SelectedItem = item;
                        break;
                    }
                }
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (MonthComboBox.SelectedItem == null)
                {
                    MessageBox.Show("يرجى اختيار الشهر.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (!int.TryParse(YearTextBox.Text, out int year) || year < 2000 || year > 2100)
                {
                    MessageBox.Show("يرجى إدخال سنة صحيحة.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (PayrollDatePicker.SelectedDate == null)
                {
                    MessageBox.Show("يرجى اختيار تاريخ الراتب.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var selectedMonth = (ComboBoxItem)MonthComboBox.SelectedItem;
                var selectedStatus = (ComboBoxItem)StatusComboBox.SelectedItem;

                if (_isEditMode && _editingPayroll != null)
                {
                    // تحديث البيانات الموجودة
                    _editingPayroll.PayrollNumber = PayrollNumberTextBox.Text.Trim();
                    _editingPayroll.PayrollDate = PayrollDatePicker.SelectedDate.Value;
                    _editingPayroll.Month = int.Parse(selectedMonth.Tag.ToString()!);
                    _editingPayroll.Year = year;
                    _editingPayroll.BasicSalary = GetDecimalValue(BasicSalaryTextBox.Text);
                    _editingPayroll.HousingAllowance = GetDecimalValue(HousingAllowanceTextBox.Text);
                    _editingPayroll.TransportationAllowance = GetDecimalValue(TransportationAllowanceTextBox.Text);
                    _editingPayroll.FoodAllowance = GetDecimalValue(FoodAllowanceTextBox.Text);
                    _editingPayroll.OtherAllowances = GetDecimalValue(OtherAllowancesTextBox.Text);
                    _editingPayroll.OvertimeHours = GetDecimalValue(OvertimeHoursTextBox.Text);
                    _editingPayroll.OvertimeRate = GetDecimalValue(OvertimeRateTextBox.Text);
                    _editingPayroll.SocialInsurance = GetDecimalValue(SocialInsuranceTextBox.Text);
                    _editingPayroll.IncomeTax = GetDecimalValue(IncomeTaxTextBox.Text);
                    _editingPayroll.Loan = GetDecimalValue(LoanTextBox.Text);
                    _editingPayroll.OtherDeductions = GetDecimalValue(OtherDeductionsTextBox.Text);
                    _editingPayroll.Status = Enum.Parse<PayrollStatus>(selectedStatus.Tag.ToString()!);
                    _editingPayroll.Notes = NotesTextBox.Text.Trim();

                    NewPayroll = _editingPayroll;
                }
                else
                {
                    // إنشاء راتب جديد
                    NewPayroll = new Payroll
                    {
                        PayrollNumber = PayrollNumberTextBox.Text.Trim(),
                        EmployeeId = _employee.EmployeeId,
                        PayrollDate = PayrollDatePicker.SelectedDate.Value,
                        Month = int.Parse(selectedMonth.Tag.ToString()!),
                        Year = year,
                        BasicSalary = GetDecimalValue(BasicSalaryTextBox.Text),
                        HousingAllowance = GetDecimalValue(HousingAllowanceTextBox.Text),
                        TransportationAllowance = GetDecimalValue(TransportationAllowanceTextBox.Text),
                        FoodAllowance = GetDecimalValue(FoodAllowanceTextBox.Text),
                        OtherAllowances = GetDecimalValue(OtherAllowancesTextBox.Text),
                        OvertimeHours = GetDecimalValue(OvertimeHoursTextBox.Text),
                        OvertimeRate = GetDecimalValue(OvertimeRateTextBox.Text),
                        SocialInsurance = GetDecimalValue(SocialInsuranceTextBox.Text),
                        IncomeTax = GetDecimalValue(IncomeTaxTextBox.Text),
                        Loan = GetDecimalValue(LoanTextBox.Text),
                        OtherDeductions = GetDecimalValue(OtherDeductionsTextBox.Text),
                        Status = Enum.Parse<PayrollStatus>(selectedStatus.Tag.ToString()!),
                        Notes = NotesTextBox.Text.Trim()
                    };
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
