using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace SimpleAccountingApp.Windows
{
    public partial class AccountsWindow : Window
    {
        private ObservableCollection<Account> accounts;

        public AccountsWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            // إنشاء بيانات تجريبية للحسابات
            accounts = new ObservableCollection<Account>
            {
                new Account
                {
                    AccountCode = "1000",
                    AccountName = "الأصول",
                    AccountType = "أصول",
                    IsActive = true,
                    OpeningBalance = 0,
                    CurrentBalance = 150000,
                    Description = "مجموعة الأصول الرئيسية",
                    SubAccounts = new ObservableCollection<Account>
                    {
                        new Account
                        {
                            AccountCode = "1100",
                            AccountName = "الأصول المتداولة",
                            AccountType = "أصول",
                            IsActive = true,
                            OpeningBalance = 0,
                            CurrentBalance = 100000,
                            Description = "الأصول قصيرة المدى",
                            SubAccounts = new ObservableCollection<Account>
                            {
                                new Account
                                {
                                    AccountCode = "1110",
                                    AccountName = "النقدية",
                                    AccountType = "أصول",
                                    IsActive = true,
                                    OpeningBalance = 50000,
                                    CurrentBalance = 75000,
                                    Description = "النقدية في الصندوق والبنوك"
                                },
                                new Account
                                {
                                    AccountCode = "1120",
                                    AccountName = "العملاء",
                                    AccountType = "أصول",
                                    IsActive = true,
                                    OpeningBalance = 20000,
                                    CurrentBalance = 25000,
                                    Description = "مستحقات العملاء"
                                }
                            }
                        },
                        new Account
                        {
                            AccountCode = "1200",
                            AccountName = "الأصول الثابتة",
                            AccountType = "أصول",
                            IsActive = true,
                            OpeningBalance = 50000,
                            CurrentBalance = 50000,
                            Description = "الأصول طويلة المدى",
                            SubAccounts = new ObservableCollection<Account>
                            {
                                new Account
                                {
                                    AccountCode = "1210",
                                    AccountName = "المباني والمعدات",
                                    AccountType = "أصول",
                                    IsActive = true,
                                    OpeningBalance = 50000,
                                    CurrentBalance = 50000,
                                    Description = "المباني والمعدات والآلات"
                                }
                            }
                        }
                    }
                },
                new Account
                {
                    AccountCode = "2000",
                    AccountName = "الخصوم",
                    AccountType = "خصوم",
                    IsActive = true,
                    OpeningBalance = 0,
                    CurrentBalance = 50000,
                    Description = "مجموعة الخصوم الرئيسية",
                    SubAccounts = new ObservableCollection<Account>
                    {
                        new Account
                        {
                            AccountCode = "2100",
                            AccountName = "الخصوم المتداولة",
                            AccountType = "خصوم",
                            IsActive = true,
                            OpeningBalance = 0,
                            CurrentBalance = 30000,
                            Description = "الخصوم قصيرة المدى",
                            SubAccounts = new ObservableCollection<Account>
                            {
                                new Account
                                {
                                    AccountCode = "2110",
                                    AccountName = "الموردين",
                                    AccountType = "خصوم",
                                    IsActive = true,
                                    OpeningBalance = 15000,
                                    CurrentBalance = 20000,
                                    Description = "مستحقات الموردين"
                                }
                            }
                        }
                    }
                },
                new Account
                {
                    AccountCode = "3000",
                    AccountName = "حقوق الملكية",
                    AccountType = "حقوق ملكية",
                    IsActive = true,
                    OpeningBalance = 100000,
                    CurrentBalance = 100000,
                    Description = "رأس المال وحقوق الملكية"
                },
                new Account
                {
                    AccountCode = "4000",
                    AccountName = "الإيرادات",
                    AccountType = "إيرادات",
                    IsActive = true,
                    OpeningBalance = 0,
                    CurrentBalance = 80000,
                    Description = "إيرادات المبيعات والخدمات"
                },
                new Account
                {
                    AccountCode = "5000",
                    AccountName = "المصروفات",
                    AccountType = "مصروفات",
                    IsActive = true,
                    OpeningBalance = 0,
                    CurrentBalance = 30000,
                    Description = "المصروفات التشغيلية والإدارية"
                }
            };

            AccountsTreeView.ItemsSource = accounts;
        }

        private void AccountsTreeView_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            if (e.NewValue is Account selectedAccount)
            {
                DisplayAccountDetails(selectedAccount);
                EditAccountButton.IsEnabled = true;
                DeleteAccountButton.IsEnabled = true;
            }
            else
            {
                ClearAccountDetails();
                EditAccountButton.IsEnabled = false;
                DeleteAccountButton.IsEnabled = false;
            }
        }

        private void DisplayAccountDetails(Account account)
        {
            AccountCodeText.Text = account.AccountCode;
            AccountNameText.Text = account.AccountName;
            AccountTypeText.Text = account.AccountType;
            ParentAccountText.Text = account.ParentAccount ?? "لا يوجد";
            OpeningBalanceText.Text = account.OpeningBalance.ToString("N2") + " ريال";
            CurrentBalanceText.Text = account.CurrentBalance.ToString("N2") + " ريال";
            IsActiveText.Text = account.IsActive ? "نشط" : "غير نشط";
            DescriptionText.Text = account.Description ?? "لا يوجد وصف";
        }

        private void ClearAccountDetails()
        {
            AccountCodeText.Text = "-";
            AccountNameText.Text = "-";
            AccountTypeText.Text = "-";
            ParentAccountText.Text = "-";
            OpeningBalanceText.Text = "-";
            CurrentBalanceText.Text = "-";
            IsActiveText.Text = "-";
            DescriptionText.Text = "-";
        }

        private void AddAccountButton_Click(object sender, RoutedEventArgs e)
        {
            var addAccountWindow = new AddAccountWindow();
            if (addAccountWindow.ShowDialog() == true)
            {
                // إضافة الحساب الجديد
                MessageBox.Show("تم إضافة الحساب بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: تحديث القائمة
            }
        }

        private void EditAccountButton_Click(object sender, RoutedEventArgs e)
        {
            if (AccountsTreeView.SelectedItem is Account selectedAccount)
            {
                var editAccountWindow = new AddAccountWindow(selectedAccount);
                if (editAccountWindow.ShowDialog() == true)
                {
                    MessageBox.Show("تم تعديل الحساب بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    DisplayAccountDetails(selectedAccount);
                }
            }
        }

        private void DeleteAccountButton_Click(object sender, RoutedEventArgs e)
        {
            if (AccountsTreeView.SelectedItem is Account selectedAccount)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الحساب '{selectedAccount.AccountName}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    // TODO: حذف الحساب من قاعدة البيانات
                    MessageBox.Show("تم حذف الحساب بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // TODO: تطبيق البحث
            string searchText = SearchTextBox.Text.ToLower();
            // فلترة الحسابات حسب النص المدخل
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    // نموذج بيانات الحساب
    public class Account
    {
        public string AccountCode { get; set; } = "";
        public string AccountName { get; set; } = "";
        public string AccountType { get; set; } = "";
        public string? ParentAccount { get; set; }
        public bool IsActive { get; set; } = true;
        public decimal OpeningBalance { get; set; }
        public decimal CurrentBalance { get; set; }
        public string? Description { get; set; }
        public ObservableCollection<Account> SubAccounts { get; set; } = new ObservableCollection<Account>();
    }
}
