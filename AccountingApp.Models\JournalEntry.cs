using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingApp.Models
{
    /// <summary>
    /// نموذج القيد المحاسبي - Journal Entry Model
    /// </summary>
    public class JournalEntry
    {
        [Key]
        public int JournalEntryId { get; set; }

        [Required]
        [StringLength(20)]
        public string EntryNumber { get; set; } = string.Empty;

        public DateTime EntryDate { get; set; } = DateTime.Now;

        public JournalEntryType EntryType { get; set; } = JournalEntryType.Manual;

        [StringLength(20)]
        public string? ReferenceNumber { get; set; }

        public int? ReferenceId { get; set; }

        [Required]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalDebit { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCredit { get; set; } = 0;

        public bool IsPosted { get; set; } = false;

        public DateTime? PostedDate { get; set; }

        public string? PostedBy { get; set; }

        public bool IsReversed { get; set; } = false;

        public DateTime? ReversedDate { get; set; }

        public string? ReversedBy { get; set; }

        public int? ReversalEntryId { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? ModifiedDate { get; set; }

        public string? CreatedBy { get; set; }

        public string? ModifiedBy { get; set; }

        // Navigation Properties
        public virtual ICollection<JournalEntryDetail> JournalEntryDetails { get; set; } = new List<JournalEntryDetail>();
    }

    /// <summary>
    /// نموذج تفاصيل القيد المحاسبي - Journal Entry Detail Model
    /// </summary>
    public class JournalEntryDetail
    {
        [Key]
        public int JournalEntryDetailId { get; set; }

        public int JournalEntryId { get; set; }

        [ForeignKey("JournalEntryId")]
        public virtual JournalEntry JournalEntry { get; set; } = null!;

        public int AccountId { get; set; }

        [ForeignKey("AccountId")]
        public virtual Account Account { get; set; } = null!;

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,2)")]
        public decimal DebitAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CreditAmount { get; set; } = 0;

        public int? CostCenterId { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }
    }

    /// <summary>
    /// نوع القيد المحاسبي - Journal Entry Type
    /// </summary>
    public enum JournalEntryType
    {
        Manual = 1,         // يدوي
        SalesInvoice = 2,   // فاتورة مبيعات
        PurchaseInvoice = 3, // فاتورة مشتريات
        SalesReturn = 4,    // مردود مبيعات
        PurchaseReturn = 5, // مردود مشتريات
        Receipt = 6,        // سند قبض
        Payment = 7,        // سند صرف
        Opening = 8,        // قيد افتتاحي
        Closing = 9,        // قيد إقفال
        Adjustment = 10     // قيد تسوية
    }

    /// <summary>
    /// نموذج سند القبض - Receipt Voucher Model
    /// </summary>
    public class ReceiptVoucher
    {
        [Key]
        public int ReceiptVoucherId { get; set; }

        [Required]
        [StringLength(20)]
        public string VoucherNumber { get; set; } = string.Empty;

        public DateTime VoucherDate { get; set; } = DateTime.Now;

        public int? CustomerId { get; set; }

        [ForeignKey("CustomerId")]
        public virtual Customer? Customer { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; } = 0;

        public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.Cash;

        [StringLength(50)]
        public string? CheckNumber { get; set; }

        public DateTime? CheckDate { get; set; }

        [StringLength(100)]
        public string? BankName { get; set; }

        [Required]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Notes { get; set; }

        public int? JournalEntryId { get; set; }

        [ForeignKey("JournalEntryId")]
        public virtual JournalEntry? JournalEntry { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? ModifiedDate { get; set; }

        public string? CreatedBy { get; set; }

        public string? ModifiedBy { get; set; }
    }

    /// <summary>
    /// نموذج سند الصرف - Payment Voucher Model
    /// </summary>
    public class PaymentVoucher
    {
        [Key]
        public int PaymentVoucherId { get; set; }

        [Required]
        [StringLength(20)]
        public string VoucherNumber { get; set; } = string.Empty;

        public DateTime VoucherDate { get; set; } = DateTime.Now;

        public int? SupplierId { get; set; }

        [ForeignKey("SupplierId")]
        public virtual Supplier? Supplier { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; } = 0;

        public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.Cash;

        [StringLength(50)]
        public string? CheckNumber { get; set; }

        public DateTime? CheckDate { get; set; }

        [StringLength(100)]
        public string? BankName { get; set; }

        [Required]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Notes { get; set; }

        public int? JournalEntryId { get; set; }

        [ForeignKey("JournalEntryId")]
        public virtual JournalEntry? JournalEntry { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? ModifiedDate { get; set; }

        public string? CreatedBy { get; set; }

        public string? ModifiedBy { get; set; }
    }
}
