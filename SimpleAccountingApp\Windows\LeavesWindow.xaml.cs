using System.Collections.ObjectModel;
using System.Windows;
using SimpleAccountingApp.Models;
using SimpleAccountingApp.Data;
using Microsoft.EntityFrameworkCore;

namespace SimpleAccountingApp.Windows
{
    public partial class LeavesWindow : Window
    {
        private Employee _employee;
        private ObservableCollection<Leave> leaves = new();
        private AccountingDbContext _context;

        public LeavesWindow(Employee employee)
        {
            InitializeComponent();
            _employee = employee;
            _context = new AccountingDbContext();
            InitializeWindow();
            LoadLeaves();
        }

        private void InitializeWindow()
        {
            TitleTextBlock.Text = $"إدارة الإجازات - {_employee.FullName}";
            EmployeeInfoTextBlock.Text = $"الموظف: {_employee.FullName} | القسم: {_employee.Department} | المسمى: {_employee.JobTitle}";
        }

        private async void LoadLeaves()
        {
            try
            {
                await _context.InitializeDatabaseAsync();
                var leavesList = await _context.Leaves
                    .Where(l => l.EmployeeId == _employee.EmployeeId)
                    .OrderByDescending(l => l.StartDate)
                    .ToListAsync();
                
                leaves.Clear();
                foreach (var leave in leavesList)
                {
                    leaves.Add(leave);
                }

                LeavesDataGrid.ItemsSource = leaves;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإجازات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void AddLeaveButton_Click(object sender, RoutedEventArgs e)
        {
            var leaveWindow = new AddLeaveWindow(_employee);
            if (leaveWindow.ShowDialog() == true)
            {
                var newLeave = leaveWindow.NewLeave;
                if (newLeave != null)
                {
                    try
                    {
                        _context.Leaves.Add(newLeave);
                        await _context.SaveChangesAsync();

                        leaves.Add(newLeave);
                        RefreshLeavesGrid();

                        MessageBox.Show("تم إضافة طلب الإجازة بنجاح!", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حفظ طلب الإجازة: {ex.Message}", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private async void EditLeaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (LeavesDataGrid.SelectedItem is Leave selectedLeave)
            {
                if (selectedLeave.Status != LeaveStatus.Pending)
                {
                    MessageBox.Show("لا يمكن تعديل إجازة معتمدة أو مرفوضة.", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var leaveWindow = new AddLeaveWindow(_employee, selectedLeave);
                if (leaveWindow.ShowDialog() == true)
                {
                    try
                    {
                        _context.Leaves.Update(selectedLeave);
                        await _context.SaveChangesAsync();

                        RefreshLeavesGrid();

                        MessageBox.Show("تم تعديل طلب الإجازة بنجاح!", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في تحديث طلب الإجازة: {ex.Message}", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار إجازة للتعديل.", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void DeleteLeaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (LeavesDataGrid.SelectedItem is Leave selectedLeave)
            {
                if (selectedLeave.Status == LeaveStatus.Approved)
                {
                    MessageBox.Show("لا يمكن حذف إجازة معتمدة.", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف طلب الإجازة؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        _context.Leaves.Remove(selectedLeave);
                        await _context.SaveChangesAsync();

                        leaves.Remove(selectedLeave);
                        RefreshLeavesGrid();

                        MessageBox.Show("تم حذف طلب الإجازة بنجاح!", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف طلب الإجازة: {ex.Message}", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار إجازة للحذف.", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void ApproveLeaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (LeavesDataGrid.SelectedItem is Leave selectedLeave)
            {
                if (selectedLeave.Status != LeaveStatus.Pending)
                {
                    MessageBox.Show("هذه الإجازة تم البت فيها مسبقاً.", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show(
                    "هل تريد اعتماد هذه الإجازة؟",
                    "اعتماد الإجازة",
                    MessageBoxButton.YesNoCancel,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    selectedLeave.Status = LeaveStatus.Approved;
                    selectedLeave.ApprovedBy = "المدير"; // يمكن تحسين هذا لاحقاً
                    selectedLeave.ApprovedDate = DateTime.Now;
                }
                else if (result == MessageBoxResult.No)
                {
                    selectedLeave.Status = LeaveStatus.Rejected;
                    selectedLeave.RejectionReason = "مرفوض من قبل الإدارة"; // يمكن إضافة نافذة لإدخال السبب
                }
                else
                {
                    return;
                }

                try
                {
                    _context.Leaves.Update(selectedLeave);
                    await _context.SaveChangesAsync();

                    RefreshLeavesGrid();

                    MessageBox.Show("تم تحديث حالة الإجازة بنجاح!", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحديث حالة الإجازة: {ex.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار إجازة للبت فيها.", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadLeaves();
        }

        private void RefreshLeavesGrid()
        {
            LeavesDataGrid.Items.Refresh();
        }
    }
}
