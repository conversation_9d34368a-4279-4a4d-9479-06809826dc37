using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace SimpleAccountingApp.Windows
{
    public partial class UsersManagementWindow : Window
    {
        private ObservableCollection<User> users;

        public UsersManagementWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            users = new ObservableCollection<User>
            {
                new User
                {
                    UserId = 1,
                    FullName = "أحمد محمد السالم",
                    Username = "admin",
                    Email = "<EMAIL>",
                    Role = "مدير النظام",
                    Department = "الإدارة",
                    Phone = "**********",
                    CreatedDate = DateTime.Now.AddMonths(-6),
                    LastLoginDate = DateTime.Now.AddHours(-2),
                    IsActive = true
                },
                new User
                {
                    UserId = 2,
                    FullName = "فاطمة علي القحطاني",
                    Username = "محاسب",
                    Email = "<EMAIL>",
                    Role = "محاسب",
                    Department = "المحاسبة",
                    Phone = "0509876543",
                    CreatedDate = DateTime.Now.AddMonths(-4),
                    LastLoginDate = DateTime.Now.AddDays(-1),
                    IsActive = true
                },
                new User
                {
                    UserId = 3,
                    FullName = "محمد عبدالله الغامدي",
                    Username = "مدير",
                    Email = "<EMAIL>",
                    Role = "مدير",
                    Department = "المبيعات",
                    Phone = "0555555555",
                    CreatedDate = DateTime.Now.AddMonths(-3),
                    LastLoginDate = DateTime.Now.AddHours(-5),
                    IsActive = true
                },
                new User
                {
                    UserId = 4,
                    FullName = "سارة أحمد المطيري",
                    Username = "مستخدم",
                    Email = "<EMAIL>",
                    Role = "مستخدم عادي",
                    Department = "المخازن",
                    Phone = "0544444444",
                    CreatedDate = DateTime.Now.AddMonths(-1),
                    LastLoginDate = DateTime.Now.AddDays(-3),
                    IsActive = false
                }
            };

            // إضافة خاصية IsActiveText
            foreach (var user in users)
            {
                user.IsActiveText = user.IsActive ? "نشط" : "غير نشط";
            }

            UsersDataGrid.ItemsSource = users;
        }

        private void UsersDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            bool hasSelection = UsersDataGrid.SelectedItem != null;
            EditUserButton.IsEnabled = hasSelection;
            DeleteUserButton.IsEnabled = hasSelection;
            ResetPasswordButton.IsEnabled = hasSelection;
        }

        private void AddUserButton_Click(object sender, RoutedEventArgs e)
        {
            var createUserWindow = new CreateUserWindow();
            if (createUserWindow.ShowDialog() == true)
            {
                MessageBox.Show("تم إضافة المستخدم بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: إعادة تحميل البيانات
            }
        }

        private void EditUserButton_Click(object sender, RoutedEventArgs e)
        {
            if (UsersDataGrid.SelectedItem is User selectedUser)
            {
                MessageBox.Show($"تعديل المستخدم {selectedUser.FullName} - قيد التطوير", "تعديل", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void DeleteUserButton_Click(object sender, RoutedEventArgs e)
        {
            if (UsersDataGrid.SelectedItem is User selectedUser)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف المستخدم '{selectedUser.FullName}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    users.Remove(selectedUser);
                    MessageBox.Show("تم حذف المستخدم بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void ResetPasswordButton_Click(object sender, RoutedEventArgs e)
        {
            if (UsersDataGrid.SelectedItem is User selectedUser)
            {
                var result = MessageBox.Show(
                    $"هل تريد إعادة تعيين كلمة المرور للمستخدم '{selectedUser.FullName}'؟\nكلمة المرور الجديدة ستكون: 123456",
                    "إعادة تعيين كلمة المرور",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // في التطبيق الحقيقي، سيتم تحديث كلمة المرور في قاعدة البيانات
                    MessageBox.Show("تم إعادة تعيين كلمة المرور بنجاح!\nكلمة المرور الجديدة: 123456", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            string searchText = SearchTextBox.Text.ToLower();
            if (string.IsNullOrWhiteSpace(searchText))
            {
                UsersDataGrid.ItemsSource = users;
            }
            else
            {
                var filteredUsers = users.Where(u => 
                    u.FullName.ToLower().Contains(searchText) ||
                    u.Username.ToLower().Contains(searchText) ||
                    u.Email.ToLower().Contains(searchText) ||
                    u.Role.ToLower().Contains(searchText) ||
                    u.Department.ToLower().Contains(searchText)
                ).ToList();
                
                UsersDataGrid.ItemsSource = filteredUsers;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
