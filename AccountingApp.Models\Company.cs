using System.ComponentModel.DataAnnotations;

namespace AccountingApp.Models
{
    /// <summary>
    /// نموذج الشركة - Company Model
    /// </summary>
    public class Company
    {
        [Key]
        public int CompanyId { get; set; }

        [Required]
        [StringLength(100)]
        public string CompanyName { get; set; } = string.Empty;

        [StringLength(200)]
        public string CompanyNameEnglish { get; set; } = string.Empty;

        [StringLength(500)]
        public string Address { get; set; } = string.Empty;

        [StringLength(20)]
        public string Phone { get; set; } = string.Empty;

        [StringLength(20)]
        public string Mobile { get; set; } = string.Empty;

        [StringLength(20)]
        public string Fax { get; set; } = string.Empty;

        [StringLength(100)]
        public string Email { get; set; } = string.Empty;

        [StringLength(100)]
        public string Website { get; set; } = string.Empty;

        [StringLength(50)]
        public string TaxNumber { get; set; } = string.Empty;

        [StringLength(50)]
        public string CommercialRegister { get; set; } = string.Empty;

        [StringLength(10)]
        public string Currency { get; set; } = "ريال";

        public byte[]? Logo { get; set; }

        public DateTime FinancialYearStart { get; set; } = new DateTime(DateTime.Now.Year, 1, 1);

        public DateTime FinancialYearEnd { get; set; } = new DateTime(DateTime.Now.Year, 12, 31);

        public decimal TaxRate { get; set; } = 15.0m; // ضريبة القيمة المضافة

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? ModifiedDate { get; set; }

        public string? ModifiedBy { get; set; }
    }
}
