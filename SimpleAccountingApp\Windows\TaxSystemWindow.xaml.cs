using System.Windows;

namespace SimpleAccountingApp.Windows
{
    public partial class TaxSystemWindow : Window
    {
        public TaxSystemWindow()
        {
            InitializeComponent();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        // VAT Section
        private void VATRegistrationButton_Click(object sender, RoutedEventArgs e)
        {
            var vatRegistrationWindow = new VATRegistrationWindow();
            vatRegistrationWindow.ShowDialog();
        }

        private void VATReturnsButton_Click(object sender, RoutedEventArgs e)
        {
            var vatReturnsWindow = new VATReturnsWindow();
            vatReturnsWindow.ShowDialog();
        }

        private void VATCalculatorButton_Click(object sender, RoutedEventArgs e)
        {
            var vatCalculatorWindow = new VATCalculatorWindow();
            vatCalculatorWindow.ShowDialog();
        }

        // Zakat Section
        private void ZakatCalculationButton_Click(object sender, RoutedEventArgs e)
        {
            var zakatCalculatorWindow = new ZakatCalculatorWindow();
            zakatCalculatorWindow.ShowDialog();
        }

        private void ZakatReturnsButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("إقرار الزكاة السنوي\n\nسيتم توجيهك إلى موقع هيئة الزكاة والضرائب والجمارك لتقديم الإقرار الإلكتروني.\n\nالموقع: https://zatca.gov.sa", 
                "إقرار الزكاة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ZakatPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("دفع الزكاة الإلكتروني\n\nيمكنك دفع الزكاة من خلال:\n• البوابة الإلكترونية لهيئة الزكاة والضرائب\n• تطبيق الجوال\n• البنوك المعتمدة\n\nللمزيد: https://zatca.gov.sa", 
                "دفع الزكاة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // E-Invoicing Section
        private void EInvoiceIntegrationButton_Click(object sender, RoutedEventArgs e)
        {
            var eInvoiceSetupWindow = new EInvoiceSetupWindow();
            eInvoiceSetupWindow.ShowDialog();
        }

        private void QRCodeButton_Click(object sender, RoutedEventArgs e)
        {
            var qrCodeWindow = new QRCodeGeneratorWindow();
            qrCodeWindow.ShowDialog();
        }

        private void InvoiceValidationButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("التحقق من صحة الفواتير الإلكترونية\n\nيتم التحقق من:\n• تنسيق الفاتورة\n• البيانات المطلوبة\n• رمز QR\n• التوقيع الرقمي\n• الامتثال للمعايير السعودية", 
                "التحقق من الفواتير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // Reports Section
        private void TaxReportsButton_Click(object sender, RoutedEventArgs e)
        {
            var taxReportsWindow = new TaxReportsWindow();
            taxReportsWindow.ShowDialog();
        }

        private void ComplianceCheckButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("فحص الامتثال الضريبي\n\nيتم فحص:\n• اكتمال الإقرارات الضريبية\n• دقة البيانات المالية\n• الامتثال للقوانين واللوائح\n• مواعيد التقديم والدفع\n• متطلبات الفوترة الإلكترونية", 
                "فحص الامتثال", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void HelpSupportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("المساعدة والدعم الفني\n\n📞 الخط الساخن: 19993\n🌐 الموقع الإلكتروني: https://zatca.gov.sa\n📧 البريد الإلكتروني: <EMAIL>\n\n📚 الأدلة الإرشادية:\n• دليل ضريبة القيمة المضافة\n• دليل الزكاة\n• دليل الفوترة الإلكترونية\n• الأسئلة الشائعة", 
                "المساعدة والدعم", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
