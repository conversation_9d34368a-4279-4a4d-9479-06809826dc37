﻿#pragma checksum "..\..\..\..\..\Windows\AddProductWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "6C5DBFAC3555CA87E8A15B656950AD3E4D3887F8"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleAccountingApp.Windows {
    
    
    /// <summary>
    /// AddProductWindow
    /// </summary>
    public partial class AddProductWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 23 "..\..\..\..\..\Windows\AddProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WindowTitle;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\..\..\Windows\AddProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ProductCodeTextBox;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\..\Windows\AddProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ProductNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\..\Windows\AddProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BarcodeTextBox;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\..\Windows\AddProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CategoryComboBox;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\..\Windows\AddProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox UnitComboBox;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\..\Windows\AddProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PurchasePriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\..\Windows\AddProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SalePriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\..\Windows\AddProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CurrentStockTextBox;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\..\Windows\AddProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MinimumStockTextBox;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\..\Windows\AddProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaximumStockTextBox;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\..\Windows\AddProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProfitMarginText;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\..\Windows\AddProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProfitPercentageText;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\..\Windows\AddProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\..\Windows\AddProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsActiveCheckBox;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\..\Windows\AddProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\..\..\Windows\AddProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleAccountingApp;component/windows/addproductwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\AddProductWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.WindowTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.ProductCodeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.ProductNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.BarcodeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.CategoryComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.UnitComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.PurchasePriceTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 100 "..\..\..\..\..\Windows\AddProductWindow.xaml"
            this.PurchasePriceTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.PriceTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.SalePriceTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 109 "..\..\..\..\..\Windows\AddProductWindow.xaml"
            this.SalePriceTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.PriceTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.CurrentStockTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.MinimumStockTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.MaximumStockTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.ProfitMarginText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.ProfitPercentageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.IsActiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 16:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 187 "..\..\..\..\..\Windows\AddProductWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 198 "..\..\..\..\..\Windows\AddProductWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

