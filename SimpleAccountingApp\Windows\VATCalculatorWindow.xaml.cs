using System.Globalization;
using System.Windows;
using System.Windows.Controls;

namespace SimpleAccountingApp.Windows
{
    public partial class VATCalculatorWindow : Window
    {
        public VATCalculatorWindow()
        {
            InitializeComponent();
            CalculateVAT();
        }

        private void CalculationType_Changed(object sender, RoutedEventArgs e)
        {
            if (AddVATRadio?.IsChecked == true)
            {
                AmountLabel.Text = "المبلغ بدون ضريبة (ريال سعودي)";
            }
            else
            {
                AmountLabel.Text = "المبلغ شامل الضريبة (ريال سعودي)";
            }
            CalculateVAT();
        }

        private void AmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateVAT();
        }

        private void CalculateVAT()
        {
            try
            {
                // الحصول على القيم المدخلة
                if (!decimal.TryParse(AmountTextBox?.Text, out decimal amount))
                    amount = 0;

                if (!decimal.TryParse(VATRateTextBox?.Text, out decimal vatRate))
                    vatRate = 15;

                decimal baseAmount = 0;
                decimal vatAmount = 0;
                decimal totalAmount = 0;

                if (AddVATRadio?.IsChecked == true)
                {
                    // إضافة الضريبة
                    baseAmount = amount;
                    vatAmount = baseAmount * (vatRate / 100);
                    totalAmount = baseAmount + vatAmount;
                }
                else
                {
                    // استخراج الضريبة
                    totalAmount = amount;
                    baseAmount = totalAmount / (1 + (vatRate / 100));
                    vatAmount = totalAmount - baseAmount;
                }

                // عرض النتائج
                if (BaseAmountResult != null)
                    BaseAmountResult.Text = $"{baseAmount:N2} ريال";
                
                if (VATAmountResult != null)
                    VATAmountResult.Text = $"{vatAmount:N2} ريال";
                
                if (TotalAmountResult != null)
                    TotalAmountResult.Text = $"{totalAmount:N2} ريال";
            }
            catch
            {
                // في حالة حدوث خطأ، عرض قيم صفر
                if (BaseAmountResult != null)
                    BaseAmountResult.Text = "0.00 ريال";
                
                if (VATAmountResult != null)
                    VATAmountResult.Text = "0.00 ريال";
                
                if (TotalAmountResult != null)
                    TotalAmountResult.Text = "0.00 ريال";
            }
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            AmountTextBox.Clear();
            VATRateTextBox.Text = "15";
            AddVATRadio.IsChecked = true;
            AmountTextBox.Focus();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
