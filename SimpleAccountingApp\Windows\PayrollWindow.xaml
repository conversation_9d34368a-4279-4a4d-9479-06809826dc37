<Window x:Class="SimpleAccountingApp.Windows.PayrollWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الرواتب" Height="600" Width="1000"
        WindowStartupLocation="CenterScreen" FlowDirection="RightToLeft">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Name="TitleTextBlock" Text="إدارة الرواتب" 
                   FontSize="20" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,10"/>

        <!-- معلومات الموظف -->
        <Border Grid.Row="1" BorderBrush="Gray" BorderThickness="1" 
                Padding="10" Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Name="EmployeeInfoTextBlock" FontSize="14" FontWeight="Bold"/>
            </StackPanel>
        </Border>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="0,0,0,20">
            <Button Name="AddPayrollButton" Content="إضافة راتب" 
                    Width="120" Click="AddPayrollButton_Click"/>
            <Button Name="EditPayrollButton" Content="تعديل" 
                    Width="100" Margin="10,0,0,0" Click="EditPayrollButton_Click"/>
            <Button Name="DeletePayrollButton" Content="حذف" 
                    Width="100" Margin="10,0,0,0" Click="DeletePayrollButton_Click"/>
            <Button Name="RefreshButton" Content="تحديث" 
                    Width="100" Margin="10,0,0,0" Click="RefreshButton_Click"/>
        </StackPanel>

        <!-- جدول الرواتب -->
        <DataGrid Grid.Row="3" Name="PayrollsDataGrid" 
                  AutoGenerateColumns="False" 
                  CanUserAddRows="False" 
                  CanUserDeleteRows="False"
                  IsReadOnly="True"
                  GridLinesVisibility="All"
                  HeadersVisibility="Column"
                  SelectionMode="Single">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="رقم الراتب" Binding="{Binding PayrollNumber}" Width="120"/>
                <DataGridTextColumn Header="الشهر" Binding="{Binding MonthName}" Width="80"/>
                <DataGridTextColumn Header="السنة" Binding="{Binding Year}" Width="80"/>
                <DataGridTextColumn Header="التاريخ" Binding="{Binding PayrollDate, StringFormat='{}{0:yyyy/MM/dd}'}" Width="100"/>
                <DataGridTextColumn Header="الراتب الأساسي" Binding="{Binding BasicSalary, StringFormat='{}{0:N2} ريال'}" Width="120"/>
                <DataGridTextColumn Header="البدلات" Binding="{Binding TotalAllowances, StringFormat='{}{0:N2} ريال'}" Width="100"/>
                <DataGridTextColumn Header="الإضافي" Binding="{Binding OvertimePay, StringFormat='{}{0:N2} ريال'}" Width="100"/>
                <DataGridTextColumn Header="الإجمالي" Binding="{Binding GrossSalary, StringFormat='{}{0:N2} ريال'}" Width="120"/>
                <DataGridTextColumn Header="الخصومات" Binding="{Binding TotalDeductions, StringFormat='{}{0:N2} ريال'}" Width="120"/>
                <DataGridTextColumn Header="الصافي" Binding="{Binding NetSalary, StringFormat='{}{0:N2} ريال'}" Width="120"/>
                <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="100"/>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</Window>
