<Window x:Class="AccountingApp.UI.SimpleLoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تسجيل الدخول - نظام المحاسبة المالية" 
        Height="500" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">
    
    <Grid>
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#E3F2FD" Offset="0"/>
                <GradientStop Color="#BBDEFB" Offset="1"/>
            </LinearGradientBrush>
        </Grid.Background>
        
        <Border Background="White" 
                CornerRadius="10" 
                Margin="40" 
                Padding="30"
                Effect="{DynamicResource MaterialDesignShadowDepth3}">
            <StackPanel>
                <!-- Logo -->
                <Ellipse Width="80" Height="80" 
                        Fill="#2196F3" 
                        HorizontalAlignment="Center"
                        Margin="0,0,0,20"/>
                
                <TextBlock Text="💼" 
                          FontSize="40" 
                          HorizontalAlignment="Center"
                          Margin="0,-60,0,40"
                          Foreground="White"/>
                
                <!-- Title -->
                <TextBlock Text="نظام المحاسبة المالية"
                          FontSize="24"
                          FontWeight="Bold"
                          HorizontalAlignment="Center"
                          Foreground="#1976D2"
                          Margin="0,0,0,10"/>
                
                <TextBlock Text="تسجيل الدخول"
                          FontSize="16"
                          HorizontalAlignment="Center"
                          Foreground="#666"
                          Margin="0,0,0,30"/>
                
                <!-- Login Form -->
                <TextBlock Text="اسم المستخدم:" 
                          FontSize="14" 
                          Margin="0,0,0,5"/>
                <TextBox x:Name="UsernameTextBox"
                        Height="35"
                        Padding="10"
                        FontSize="14"
                        Text="admin"
                        Margin="0,0,0,15"/>
                
                <TextBlock Text="كلمة المرور:" 
                          FontSize="14" 
                          Margin="0,0,0,5"/>
                <PasswordBox x:Name="PasswordBox"
                            Height="35"
                            Padding="10"
                            FontSize="14"
                            Margin="0,0,0,20"/>
                
                <!-- Login Button -->
                <Button x:Name="LoginButton"
                       Content="تسجيل الدخول"
                       Height="45"
                       FontSize="16"
                       FontWeight="Bold"
                       Background="#2196F3"
                       Foreground="White"
                       BorderThickness="0"
                       Cursor="Hand"
                       Margin="0,10,0,15"
                       Click="LoginButton_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                               CornerRadius="5"
                                               BorderThickness="{TemplateBinding BorderThickness}"
                                               BorderBrush="{TemplateBinding BorderBrush}">
                                            <ContentPresenter HorizontalAlignment="Center" 
                                                            VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#1976D2"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
                
                <!-- Error Message -->
                <TextBlock x:Name="ErrorTextBlock"
                          Foreground="Red"
                          HorizontalAlignment="Center"
                          TextWrapping="Wrap"
                          Visibility="Collapsed"/>
                
                <!-- Footer -->
                <TextBlock Text="© 2025 نظام المحاسبة المالية"
                          FontSize="12"
                          HorizontalAlignment="Center"
                          Foreground="#999"
                          Margin="0,20,0,0"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
