using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace SimpleAccountingApp.Windows
{
    public partial class AddPurchaseInvoiceWindow : Window
    {
        private PurchaseInvoice? _editingInvoice;
        private bool _isEditMode;
        private ObservableCollection<PurchaseInvoiceItem> _invoiceItems;
        private List<InvoiceSupplier> _suppliers;
        private List<InvoiceProduct> _products;

        public AddPurchaseInvoiceWindow()
        {
            InitializeComponent();
            _isEditMode = false;
            InitializeData();
            GenerateInvoiceNumber();
        }

        public AddPurchaseInvoiceWindow(PurchaseInvoice invoiceToEdit)
        {
            InitializeComponent();
            _editingInvoice = invoiceToEdit;
            _isEditMode = true;
            
            WindowTitle.Text = "تعديل فاتورة المشتريات";
            this.Title = "تعديل فاتورة المشتريات - نظام المحاسبة المالية";
            
            InitializeData();
            LoadInvoiceData();
        }

        private void InitializeData()
        {
            _invoiceItems = new ObservableCollection<PurchaseInvoiceItem>();
            ItemsDataGrid.ItemsSource = _invoiceItems;

            // تحميل الموردين
            _suppliers = new List<InvoiceSupplier>
            {
                new InvoiceSupplier { SupplierId = 1, SupplierName = "شركة التقنية المتقدمة" },
                new InvoiceSupplier { SupplierId = 2, SupplierName = "مؤسسة الخليج للمواد الغذائية" },
                new InvoiceSupplier { SupplierId = 3, SupplierName = "محمد أحمد للتجارة" },
                new InvoiceSupplier { SupplierId = 4, SupplierName = "شركة البناء الحديث" }
            };
            SupplierComboBox.ItemsSource = _suppliers;

            // تحميل الأصناف
            _products = new List<InvoiceProduct>
            {
                new InvoiceProduct { ProductId = 1, ProductCode = "P001", ProductName = "لابتوب ديل إنسبايرون", SalePrice = 2500 },
                new InvoiceProduct { ProductId = 2, ProductCode = "P002", ProductName = "طابعة HP ليزر", SalePrice = 800 },
                new InvoiceProduct { ProductId = 3, ProductCode = "P003", ProductName = "ورق A4", SalePrice = 25 },
                new InvoiceProduct { ProductId = 4, ProductCode = "P004", ProductName = "ماوس لاسلكي", SalePrice = 45 },
                new InvoiceProduct { ProductId = 5, ProductCode = "P005", ProductName = "كيبورد ميكانيكي", SalePrice = 120 }
            };

            PaymentMethodComboBox.SelectedIndex = 0;
            UpdateTotals();
        }

        private void GenerateInvoiceNumber()
        {
            Random random = new Random();
            InvoiceNumberTextBox.Text = "P" + DateTime.Now.ToString("yyyyMMdd") + random.Next(100, 999).ToString();
        }

        private void LoadInvoiceData()
        {
            if (_editingInvoice != null)
            {
                InvoiceNumberTextBox.Text = _editingInvoice.InvoiceNumber;
                InvoiceDatePicker.SelectedDate = _editingInvoice.InvoiceDate;
                
                // تحديد المورد
                var supplier = _suppliers.FirstOrDefault(s => s.SupplierName == _editingInvoice.SupplierName);
                if (supplier != null)
                {
                    SupplierComboBox.SelectedItem = supplier;
                }

                // تحديد طريقة الدفع
                foreach (ComboBoxItem item in PaymentMethodComboBox.Items)
                {
                    if (item.Content.ToString() == _editingInvoice.PaymentMethod)
                    {
                        PaymentMethodComboBox.SelectedItem = item;
                        break;
                    }
                }
            }
        }

        private void AddItemButton_Click(object sender, RoutedEventArgs e)
        {
            var selectProductWindow = new SelectProductWindow(_products);
            if (selectProductWindow.ShowDialog() == true && selectProductWindow.SelectedProduct != null)
            {
                var selectedProduct = selectProductWindow.SelectedProduct;
                
                // التحقق من عدم وجود الصنف مسبقاً
                var existingItem = _invoiceItems.FirstOrDefault(item => item.ProductCode == selectedProduct.ProductCode);
                if (existingItem != null)
                {
                    MessageBox.Show("هذا الصنف موجود بالفعل في الفاتورة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var invoiceItem = new PurchaseInvoiceItem
                {
                    ProductCode = selectedProduct.ProductCode,
                    ProductName = selectedProduct.ProductName,
                    Quantity = 1,
                    UnitPrice = selectedProduct.SalePrice,
                    DiscountAmount = 0,
                    TaxPercentage = 15,
                    TaxAmount = selectedProduct.SalePrice * 0.15m,
                    TotalAmount = selectedProduct.SalePrice + (selectedProduct.SalePrice * 0.15m)
                };

                _invoiceItems.Add(invoiceItem);
                UpdateTotals();
            }
        }

        private void UpdateTotals()
        {
            decimal subTotal = _invoiceItems.Sum(item => item.Quantity * item.UnitPrice);
            decimal totalDiscount = _invoiceItems.Sum(item => item.DiscountAmount);
            decimal totalTax = _invoiceItems.Sum(item => item.TaxAmount);
            decimal grandTotal = subTotal - totalDiscount + totalTax;

            SubTotalText.Text = subTotal.ToString("N2") + " ريال";
            TotalDiscountText.Text = totalDiscount.ToString("N2") + " ريال";
            TotalTaxText.Text = totalTax.ToString("N2") + " ريال";
            GrandTotalText.Text = grandTotal.ToString("N2") + " ريال";
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInvoice())
            {
                SaveInvoice();
                this.DialogResult = true;
                this.Close();
            }
        }

        private void SaveAndPrintButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInvoice())
            {
                SaveInvoice();
                MessageBox.Show("تم حفظ الفاتورة. الطباعة قيد التطوير.", "حفظ وطباعة", MessageBoxButton.OK, MessageBoxImage.Information);
                this.DialogResult = true;
                this.Close();
            }
        }

        private bool ValidateInvoice()
        {
            if (string.IsNullOrWhiteSpace(InvoiceNumberTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الفاتورة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (SupplierComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار المورد", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (_invoiceItems.Count == 0)
            {
                MessageBox.Show("يرجى إضافة أصناف للفاتورة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private void SaveInvoice()
        {
            // في التطبيق الحقيقي، سيتم حفظ الفاتورة في قاعدة البيانات
            // TODO: حفظ الفاتورة والأصناف في قاعدة البيانات
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            this.DialogResult = false;
            this.Close();
        }
    }

    // نماذج البيانات المساعدة
    public class PurchaseInvoiceItem
    {
        public string ProductCode { get; set; } = "";
        public string ProductName { get; set; } = "";
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxPercentage { get; set; } = 15;
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
    }

    public class InvoiceSupplier
    {
        public int SupplierId { get; set; }
        public string SupplierName { get; set; } = "";
    }
}
