using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace SimpleAccountingApp.Windows
{
    public partial class AddPurchaseInvoiceWindow : Window
    {
        private PurchaseInvoice? _editingInvoice;
        private bool _isEditMode;
        private ObservableCollection<PurchaseInvoiceItem> _invoiceItems;
        private List<InvoiceSupplier> _suppliers;
        private List<InvoiceProduct> _products;

        public PurchaseInvoice? NewInvoice { get; private set; }

        public AddPurchaseInvoiceWindow()
        {
            InitializeComponent();
            _isEditMode = false;
            InitializeData();
            GenerateInvoiceNumber();
        }

        public AddPurchaseInvoiceWindow(PurchaseInvoice invoiceToEdit)
        {
            InitializeComponent();
            _editingInvoice = invoiceToEdit;
            _isEditMode = true;
            
            WindowTitle.Text = "تعديل فاتورة المشتريات";
            this.Title = "تعديل فاتورة المشتريات - نظام المحاسبة المالية";
            
            InitializeData();
            LoadInvoiceData();
        }

        private void InitializeData()
        {
            _invoiceItems = new ObservableCollection<PurchaseInvoiceItem>();
            ItemsDataGrid.ItemsSource = _invoiceItems;

            // تحميل الموردين
            _suppliers = new List<InvoiceSupplier>
            {
                new InvoiceSupplier { SupplierId = 1, SupplierName = "شركة التقنية المتقدمة" },
                new InvoiceSupplier { SupplierId = 2, SupplierName = "مؤسسة الخليج للمواد الغذائية" },
                new InvoiceSupplier { SupplierId = 3, SupplierName = "محمد أحمد للتجارة" },
                new InvoiceSupplier { SupplierId = 4, SupplierName = "شركة البناء الحديث" }
            };
            SupplierComboBox.ItemsSource = _suppliers;

            // تحميل الأصناف
            _products = new List<InvoiceProduct>
            {
                new InvoiceProduct { ProductId = 1, ProductCode = "P001", ProductName = "لابتوب ديل إنسبايرون", SalePrice = 2500 },
                new InvoiceProduct { ProductId = 2, ProductCode = "P002", ProductName = "طابعة HP ليزر", SalePrice = 800 },
                new InvoiceProduct { ProductId = 3, ProductCode = "P003", ProductName = "ورق A4", SalePrice = 25 },
                new InvoiceProduct { ProductId = 4, ProductCode = "P004", ProductName = "ماوس لاسلكي", SalePrice = 45 },
                new InvoiceProduct { ProductId = 5, ProductCode = "P005", ProductName = "كيبورد ميكانيكي", SalePrice = 120 }
            };

            PaymentMethodComboBox.SelectedIndex = 0;
            UpdateTotals();
        }

        private void GenerateInvoiceNumber()
        {
            Random random = new Random();
            InvoiceNumberTextBox.Text = "P" + DateTime.Now.ToString("yyyyMMdd") + random.Next(100, 999).ToString();
        }

        private void LoadInvoiceData()
        {
            if (_editingInvoice != null)
            {
                InvoiceNumberTextBox.Text = _editingInvoice.InvoiceNumber;
                InvoiceDatePicker.SelectedDate = _editingInvoice.InvoiceDate;
                
                // تحديد المورد
                var supplier = _suppliers.FirstOrDefault(s => s.SupplierName == _editingInvoice.SupplierName);
                if (supplier != null)
                {
                    SupplierComboBox.SelectedItem = supplier;
                }

                // تحديد طريقة الدفع
                foreach (ComboBoxItem item in PaymentMethodComboBox.Items)
                {
                    if (item.Content.ToString() == _editingInvoice.PaymentMethod)
                    {
                        PaymentMethodComboBox.SelectedItem = item;
                        break;
                    }
                }
            }
        }

        private void AddItemButton_Click(object sender, RoutedEventArgs e)
        {
            var selectProductWindow = new SelectProductWindow(_products);
            if (selectProductWindow.ShowDialog() == true && selectProductWindow.SelectedProduct != null)
            {
                var selectedProduct = selectProductWindow.SelectedProduct;
                
                // التحقق من عدم وجود الصنف مسبقاً
                var existingItem = _invoiceItems.FirstOrDefault(item => item.ProductCode == selectedProduct.ProductCode);
                if (existingItem != null)
                {
                    MessageBox.Show("هذا الصنف موجود بالفعل في الفاتورة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var invoiceItem = new PurchaseInvoiceItem
                {
                    ProductCode = selectedProduct.ProductCode,
                    ProductName = selectedProduct.ProductName,
                    Quantity = 1,
                    UnitPrice = selectedProduct.SalePrice,
                    DiscountAmount = 0,
                    TaxPercentage = 15,
                    TaxAmount = selectedProduct.SalePrice * 0.15m,
                    TotalAmount = selectedProduct.SalePrice + (selectedProduct.SalePrice * 0.15m)
                };

                _invoiceItems.Add(invoiceItem);
                UpdateTotals();
            }
        }

        private void UpdateTotals()
        {
            decimal subTotal = _invoiceItems.Sum(item => item.Quantity * item.UnitPrice);
            decimal totalDiscount = _invoiceItems.Sum(item => item.DiscountAmount);

            // حساب ضريبة القيمة المضافة السعودية (15%) للمشتريات
            const decimal VAT_RATE = 15m;
            decimal taxableAmount = subTotal - totalDiscount;
            decimal totalTax = taxableAmount * (VAT_RATE / 100);
            decimal grandTotal = taxableAmount + totalTax;

            // تحديث ضريبة القيمة المضافة لكل صنف
            foreach (var item in _invoiceItems)
            {
                decimal itemTaxableAmount = (item.Quantity * item.UnitPrice) - item.DiscountAmount;
                item.TaxAmount = itemTaxableAmount * (VAT_RATE / 100);
                item.TaxPercentage = VAT_RATE;
                item.TotalAmount = itemTaxableAmount + item.TaxAmount;
            }

            SubTotalText.Text = subTotal.ToString("N2") + " ريال";
            TotalDiscountText.Text = totalDiscount.ToString("N2") + " ريال";
            TotalTaxText.Text = totalTax.ToString("N2") + " ريال (ضريبة القيمة المضافة 15%)";
            GrandTotalText.Text = grandTotal.ToString("N2") + " ريال";

            // حفظ معلومات الضريبة للتقارير الضريبية (ضريبة مدخلات)
            SaveInputVATInformation(subTotal, totalDiscount, totalTax, grandTotal);
        }

        private void SaveInputVATInformation(decimal subTotal, decimal discount, decimal vatAmount, decimal total)
        {
            // معلومات ضريبة المدخلات للتقارير والامتثال
            var inputVATInfo = new
            {
                InvoiceDate = DateTime.Now,
                InvoiceNumber = InvoiceNumberTextBox.Text,
                SupplierName = ((InvoiceSupplier?)SupplierComboBox.SelectedItem)?.SupplierName ?? "",
                SubTotal = subTotal,
                DiscountAmount = discount,
                TaxableAmount = subTotal - discount,
                VATRate = 15m,
                InputVATAmount = vatAmount, // ضريبة المدخلات
                TotalAmount = total,
                CompanyVATNumber = "300123456789003", // الرقم الضريبي للشركة
                IsInputVAT = true, // ضريبة مدخلات (قابلة للخصم)
                Currency = "SAR"
            };

            // في التطبيق الحقيقي، سيتم حفظ هذه المعلومات في قاعدة البيانات
            // لاستخدامها في حساب صافي ضريبة القيمة المضافة المستحقة
            // TODO: حفظ معلومات ضريبة المدخلات في قاعدة البيانات
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInvoice())
            {
                SaveInvoice();
                this.DialogResult = true;
                this.Close();
            }
        }

        private void SaveAndPrintButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInvoice())
            {
                SaveInvoice();
                MessageBox.Show("تم حفظ الفاتورة. الطباعة قيد التطوير.", "حفظ وطباعة", MessageBoxButton.OK, MessageBoxImage.Information);
                this.DialogResult = true;
                this.Close();
            }
        }

        private bool ValidateInvoice()
        {
            if (string.IsNullOrWhiteSpace(InvoiceNumberTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الفاتورة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (SupplierComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار المورد", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (_invoiceItems.Count == 0)
            {
                MessageBox.Show("يرجى إضافة أصناف للفاتورة\n\nاضغط على زر 'إضافة صنف' لإضافة أصناف إلى الفاتورة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private void SaveInvoice()
        {
            var selectedSupplier = (InvoiceSupplier?)SupplierComboBox.SelectedItem;
            var selectedPaymentMethod = ((ComboBoxItem?)PaymentMethodComboBox.SelectedItem)?.Content?.ToString() ?? "نقداً";

            // حساب المجاميع
            decimal subTotal = _invoiceItems.Sum(item => item.Quantity * item.UnitPrice);
            decimal totalDiscount = _invoiceItems.Sum(item => item.DiscountAmount);
            decimal totalTax = _invoiceItems.Sum(item => item.TaxAmount);
            decimal grandTotal = subTotal - totalDiscount + totalTax;

            // إنشاء الفاتورة الجديدة
            NewInvoice = new PurchaseInvoice
            {
                InvoiceNumber = InvoiceNumberTextBox.Text,
                InvoiceDate = InvoiceDatePicker.SelectedDate ?? DateTime.Now,
                SupplierName = selectedSupplier?.SupplierName ?? "",
                PaymentMethod = selectedPaymentMethod,
                SubTotal = subTotal,
                DiscountAmount = totalDiscount,
                TaxAmount = totalTax,
                TotalAmount = grandTotal,
                Status = InvoiceStatus.Confirmed,
                Notes = ""
            };

            // في التطبيق الحقيقي، سيتم حفظ الفاتورة في قاعدة البيانات
            // TODO: حفظ الفاتورة والأصناف في قاعدة البيانات
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            this.DialogResult = false;
            this.Close();
        }

        private void ItemsDataGrid_CellEditEnding(object sender, DataGridCellEditEndingEventArgs e)
        {
            // تحديث الحسابات عند تعديل أي خلية في الجدول
            if (e.EditAction == DataGridEditAction.Commit)
            {
                // استخدام Dispatcher لتأخير التحديث حتى يتم حفظ القيمة الجديدة
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    UpdateItemCalculations();
                    UpdateTotals();
                }), System.Windows.Threading.DispatcherPriority.Background);
            }
        }

        private void UpdateItemCalculations()
        {
            // تحديث حسابات كل صنف في الفاتورة
            foreach (var item in _invoiceItems)
            {
                // حساب المبلغ بعد الخصم
                decimal itemSubTotal = item.Quantity * item.UnitPrice;
                decimal itemAfterDiscount = itemSubTotal - item.DiscountAmount;

                // حساب الضريبة على المبلغ بعد الخصم
                item.TaxAmount = itemAfterDiscount * (item.TaxPercentage / 100);

                // حساب المبلغ الإجمالي
                item.TotalAmount = itemAfterDiscount + item.TaxAmount;
            }

            // تحديث عرض البيانات
            ItemsDataGrid.Items.Refresh();
        }
    }

    // نماذج البيانات المساعدة
    public class PurchaseInvoiceItem
    {
        public string ProductCode { get; set; } = "";
        public string ProductName { get; set; } = "";
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxPercentage { get; set; } = 15;
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
    }

    public class InvoiceSupplier
    {
        public int SupplierId { get; set; }
        public string SupplierName { get; set; } = "";
    }
}
