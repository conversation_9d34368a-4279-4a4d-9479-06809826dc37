﻿#pragma checksum "..\..\..\..\..\Windows\EmployeesWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "9B777B07BDC9C8EB17E2EC4B10BF03CF65DFEBED"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleAccountingApp.Windows {
    
    
    /// <summary>
    /// EmployeesWindow
    /// </summary>
    public partial class EmployeesWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 22 "..\..\..\..\..\Windows\EmployeesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddEmployeeButton;
        
        #line default
        #line hidden
        
        
        #line 24 "..\..\..\..\..\Windows\EmployeesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditEmployeeButton;
        
        #line default
        #line hidden
        
        
        #line 26 "..\..\..\..\..\Windows\EmployeesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteEmployeeButton;
        
        #line default
        #line hidden
        
        
        #line 28 "..\..\..\..\..\Windows\EmployeesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PayrollButton;
        
        #line default
        #line hidden
        
        
        #line 30 "..\..\..\..\..\Windows\EmployeesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LeavesButton;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\..\..\Windows\EmployeesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\..\..\Windows\EmployeesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid EmployeesDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleAccountingApp;component/windows/employeeswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\EmployeesWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AddEmployeeButton = ((System.Windows.Controls.Button)(target));
            
            #line 23 "..\..\..\..\..\Windows\EmployeesWindow.xaml"
            this.AddEmployeeButton.Click += new System.Windows.RoutedEventHandler(this.AddEmployeeButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.EditEmployeeButton = ((System.Windows.Controls.Button)(target));
            
            #line 25 "..\..\..\..\..\Windows\EmployeesWindow.xaml"
            this.EditEmployeeButton.Click += new System.Windows.RoutedEventHandler(this.EditEmployeeButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.DeleteEmployeeButton = ((System.Windows.Controls.Button)(target));
            
            #line 27 "..\..\..\..\..\Windows\EmployeesWindow.xaml"
            this.DeleteEmployeeButton.Click += new System.Windows.RoutedEventHandler(this.DeleteEmployeeButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.PayrollButton = ((System.Windows.Controls.Button)(target));
            
            #line 29 "..\..\..\..\..\Windows\EmployeesWindow.xaml"
            this.PayrollButton.Click += new System.Windows.RoutedEventHandler(this.PayrollButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.LeavesButton = ((System.Windows.Controls.Button)(target));
            
            #line 31 "..\..\..\..\..\Windows\EmployeesWindow.xaml"
            this.LeavesButton.Click += new System.Windows.RoutedEventHandler(this.LeavesButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 33 "..\..\..\..\..\Windows\EmployeesWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.EmployeesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

