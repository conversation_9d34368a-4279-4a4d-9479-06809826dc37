using System.Windows;
using System.Windows.Controls;
using SimpleAccountingApp.Models;

namespace SimpleAccountingApp.Windows
{
    public partial class AddEmployeeWindow : Window
    {
        public Employee? NewEmployee { get; private set; }
        private bool _isEditMode = false;
        private Employee? _editingEmployee;

        public AddEmployeeWindow()
        {
            InitializeComponent();
            _isEditMode = false;
            InitializeWindow();
        }

        public AddEmployeeWindow(Employee employee)
        {
            InitializeComponent();
            _isEditMode = true;
            _editingEmployee = employee;
            TitleTextBlock.Text = "تعديل موظف";
            InitializeWindow();
            LoadEmployeeData();
        }

        private void InitializeWindow()
        {
            // إنشاء كود موظف تلقائي
            if (!_isEditMode)
            {
                EmployeeCodeTextBox.Text = GenerateEmployeeCode();
            }

            // تحديد التاريخ الحالي
            HireDatePicker.SelectedDate = DateTime.Now;
            DateOfBirthPicker.SelectedDate = DateTime.Now.AddYears(-25); // افتراضي 25 سنة
        }

        private string GenerateEmployeeCode()
        {
            return $"EMP{DateTime.Now:yyyyMMddHHmmss}";
        }

        private void LoadEmployeeData()
        {
            if (_editingEmployee != null)
            {
                EmployeeCodeTextBox.Text = _editingEmployee.EmployeeCode;
                FirstNameTextBox.Text = _editingEmployee.FirstName;
                LastNameTextBox.Text = _editingEmployee.LastName;
                NationalIdTextBox.Text = _editingEmployee.NationalId;
                DateOfBirthPicker.SelectedDate = _editingEmployee.DateOfBirth;
                PhoneTextBox.Text = _editingEmployee.Phone;
                MobileTextBox.Text = _editingEmployee.Mobile;
                EmailTextBox.Text = _editingEmployee.Email;
                AddressTextBox.Text = _editingEmployee.Address;
                CityTextBox.Text = _editingEmployee.City;
                CountryTextBox.Text = _editingEmployee.Country;
                JobTitleTextBox.Text = _editingEmployee.JobTitle;
                DepartmentTextBox.Text = _editingEmployee.Department;
                HireDatePicker.SelectedDate = _editingEmployee.HireDate;
                BasicSalaryTextBox.Text = _editingEmployee.BasicSalary.ToString();
                AllowancesTextBox.Text = _editingEmployee.Allowances.ToString();
                DeductionsTextBox.Text = _editingEmployee.Deductions.ToString();
                NotesTextBox.Text = _editingEmployee.Notes;

                // تحديد الجنس
                foreach (ComboBoxItem item in GenderComboBox.Items)
                {
                    if (item.Tag.ToString() == _editingEmployee.Gender)
                    {
                        GenderComboBox.SelectedItem = item;
                        break;
                    }
                }

                // تحديد الحالة الاجتماعية
                foreach (ComboBoxItem item in MaritalStatusComboBox.Items)
                {
                    if (item.Tag.ToString() == _editingEmployee.MaritalStatus.ToString())
                    {
                        MaritalStatusComboBox.SelectedItem = item;
                        break;
                    }
                }

                // تحديد حالة الموظف
                foreach (ComboBoxItem item in StatusComboBox.Items)
                {
                    if (item.Tag.ToString() == _editingEmployee.Status.ToString())
                    {
                        StatusComboBox.SelectedItem = item;
                        break;
                    }
                }
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(EmployeeCodeTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال كود الموظف.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (string.IsNullOrWhiteSpace(FirstNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال الاسم الأول.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (string.IsNullOrWhiteSpace(LastNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال الاسم الأخير.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (string.IsNullOrWhiteSpace(JobTitleTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال المسمى الوظيفي.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (string.IsNullOrWhiteSpace(DepartmentTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال القسم.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (DateOfBirthPicker.SelectedDate == null)
                {
                    MessageBox.Show("يرجى اختيار تاريخ الميلاد.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (HireDatePicker.SelectedDate == null)
                {
                    MessageBox.Show("يرجى اختيار تاريخ التوظيف.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // التحقق من الراتب
                if (!decimal.TryParse(BasicSalaryTextBox.Text, out decimal basicSalary))
                {
                    basicSalary = 0;
                }

                if (!decimal.TryParse(AllowancesTextBox.Text, out decimal allowances))
                {
                    allowances = 0;
                }

                if (!decimal.TryParse(DeductionsTextBox.Text, out decimal deductions))
                {
                    deductions = 0;
                }

                if (_isEditMode && _editingEmployee != null)
                {
                    // تحديث البيانات الموجودة
                    _editingEmployee.EmployeeCode = EmployeeCodeTextBox.Text.Trim();
                    _editingEmployee.FirstName = FirstNameTextBox.Text.Trim();
                    _editingEmployee.LastName = LastNameTextBox.Text.Trim();
                    _editingEmployee.NationalId = NationalIdTextBox.Text.Trim();
                    _editingEmployee.DateOfBirth = DateOfBirthPicker.SelectedDate.Value;
                    _editingEmployee.Phone = PhoneTextBox.Text.Trim();
                    _editingEmployee.Mobile = MobileTextBox.Text.Trim();
                    _editingEmployee.Email = EmailTextBox.Text.Trim();
                    _editingEmployee.Address = AddressTextBox.Text.Trim();
                    _editingEmployee.City = CityTextBox.Text.Trim();
                    _editingEmployee.Country = CountryTextBox.Text.Trim();
                    _editingEmployee.JobTitle = JobTitleTextBox.Text.Trim();
                    _editingEmployee.Department = DepartmentTextBox.Text.Trim();
                    _editingEmployee.HireDate = HireDatePicker.SelectedDate.Value;
                    _editingEmployee.BasicSalary = basicSalary;
                    _editingEmployee.Allowances = allowances;
                    _editingEmployee.Deductions = deductions;
                    _editingEmployee.Notes = NotesTextBox.Text.Trim();

                    // تحديد الجنس
                    if (GenderComboBox.SelectedItem is ComboBoxItem selectedGender)
                    {
                        _editingEmployee.Gender = selectedGender.Tag.ToString()!;
                    }

                    // تحديد الحالة الاجتماعية
                    if (MaritalStatusComboBox.SelectedItem is ComboBoxItem selectedMaritalStatus)
                    {
                        _editingEmployee.MaritalStatus = Enum.Parse<MaritalStatus>(selectedMaritalStatus.Tag.ToString()!);
                    }

                    // تحديد حالة الموظف
                    if (StatusComboBox.SelectedItem is ComboBoxItem selectedStatus)
                    {
                        _editingEmployee.Status = Enum.Parse<EmployeeStatus>(selectedStatus.Tag.ToString()!);
                    }

                    NewEmployee = _editingEmployee;
                }
                else
                {
                    // إنشاء موظف جديد
                    var selectedGender = (ComboBoxItem)GenderComboBox.SelectedItem;
                    var selectedMaritalStatus = (ComboBoxItem)MaritalStatusComboBox.SelectedItem;
                    var selectedStatus = (ComboBoxItem)StatusComboBox.SelectedItem;

                    NewEmployee = new Employee
                    {
                        EmployeeCode = EmployeeCodeTextBox.Text.Trim(),
                        FirstName = FirstNameTextBox.Text.Trim(),
                        LastName = LastNameTextBox.Text.Trim(),
                        NationalId = NationalIdTextBox.Text.Trim(),
                        DateOfBirth = DateOfBirthPicker.SelectedDate.Value,
                        Gender = selectedGender?.Tag.ToString() ?? "Male",
                        MaritalStatus = selectedMaritalStatus != null ? Enum.Parse<MaritalStatus>(selectedMaritalStatus.Tag.ToString()!) : MaritalStatus.Single,
                        Phone = PhoneTextBox.Text.Trim(),
                        Mobile = MobileTextBox.Text.Trim(),
                        Email = EmailTextBox.Text.Trim(),
                        Address = AddressTextBox.Text.Trim(),
                        City = CityTextBox.Text.Trim(),
                        Country = CountryTextBox.Text.Trim(),
                        JobTitle = JobTitleTextBox.Text.Trim(),
                        Department = DepartmentTextBox.Text.Trim(),
                        HireDate = HireDatePicker.SelectedDate.Value,
                        BasicSalary = basicSalary,
                        Allowances = allowances,
                        Deductions = deductions,
                        Status = selectedStatus != null ? Enum.Parse<EmployeeStatus>(selectedStatus.Tag.ToString()!) : EmployeeStatus.Active,
                        Notes = NotesTextBox.Text.Trim()
                    };
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
