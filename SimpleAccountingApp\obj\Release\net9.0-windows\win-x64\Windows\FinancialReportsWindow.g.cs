﻿#pragma checksum "..\..\..\..\..\Windows\FinancialReportsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C0F0CA487A6EB6BE294807D5B43FE3AE2F5FF761"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleAccountingApp.Windows {
    
    
    /// <summary>
    /// FinancialReportsWindow
    /// </summary>
    public partial class FinancialReportsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 67 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button IncomeStatementButton;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BalanceSheetButton;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CashFlowButton;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SalesSummaryButton;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CustomerSalesButton;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ProductSalesButton;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PurchaseSummaryButton;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SupplierPurchasesButton;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AccountsPayableButton;
        
        #line default
        #line hidden
        
        
        #line 256 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StockValuationButton;
        
        #line default
        #line hidden
        
        
        #line 272 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StockMovementReportButton;
        
        #line default
        #line hidden
        
        
        #line 288 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LowStockButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleAccountingApp;component/windows/financialreportswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 26 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.IncomeStatementButton = ((System.Windows.Controls.Button)(target));
            
            #line 73 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
            this.IncomeStatementButton.Click += new System.Windows.RoutedEventHandler(this.IncomeStatementButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BalanceSheetButton = ((System.Windows.Controls.Button)(target));
            
            #line 89 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
            this.BalanceSheetButton.Click += new System.Windows.RoutedEventHandler(this.BalanceSheetButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CashFlowButton = ((System.Windows.Controls.Button)(target));
            
            #line 105 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
            this.CashFlowButton.Click += new System.Windows.RoutedEventHandler(this.CashFlowButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.SalesSummaryButton = ((System.Windows.Controls.Button)(target));
            
            #line 136 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
            this.SalesSummaryButton.Click += new System.Windows.RoutedEventHandler(this.SalesSummaryButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CustomerSalesButton = ((System.Windows.Controls.Button)(target));
            
            #line 152 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
            this.CustomerSalesButton.Click += new System.Windows.RoutedEventHandler(this.CustomerSalesButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ProductSalesButton = ((System.Windows.Controls.Button)(target));
            
            #line 168 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
            this.ProductSalesButton.Click += new System.Windows.RoutedEventHandler(this.ProductSalesButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.PurchaseSummaryButton = ((System.Windows.Controls.Button)(target));
            
            #line 199 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
            this.PurchaseSummaryButton.Click += new System.Windows.RoutedEventHandler(this.PurchaseSummaryButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.SupplierPurchasesButton = ((System.Windows.Controls.Button)(target));
            
            #line 215 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
            this.SupplierPurchasesButton.Click += new System.Windows.RoutedEventHandler(this.SupplierPurchasesButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.AccountsPayableButton = ((System.Windows.Controls.Button)(target));
            
            #line 231 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
            this.AccountsPayableButton.Click += new System.Windows.RoutedEventHandler(this.AccountsPayableButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.StockValuationButton = ((System.Windows.Controls.Button)(target));
            
            #line 262 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
            this.StockValuationButton.Click += new System.Windows.RoutedEventHandler(this.StockValuationButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.StockMovementReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 278 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
            this.StockMovementReportButton.Click += new System.Windows.RoutedEventHandler(this.StockMovementReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.LowStockButton = ((System.Windows.Controls.Button)(target));
            
            #line 294 "..\..\..\..\..\Windows\FinancialReportsWindow.xaml"
            this.LowStockButton.Click += new System.Windows.RoutedEventHandler(this.LowStockButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

