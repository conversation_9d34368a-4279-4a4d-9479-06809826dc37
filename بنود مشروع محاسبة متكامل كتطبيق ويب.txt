الوصف المستخدم في الشرح:
برومبت:
"قم بإنشاء برنامج حسابات متكامل بلغة بايثون يعمل عبر الويب، يُطلق عليه اسم المحاسب الشامل. يجب أن يكون البرنامج متقدماً وسهل الاستخدام، ويشمل جميع الميزات الأساسية لإدارة الحسابات المالية والمخزون. استخدم Django أو Flask لإنشاء الواجهة الخلفية، وقاعدة بيانات PostgreSQL أو MySQL لحفظ البيانات. استخدم Bootstrap لتصميم واجهة المستخدم بحيث تكون احترافية ومتجاوبة.

المميزات المطلوبة:

إدارة المشتريات

تسجيل الفواتير والموردين
تحديث المخزون تلقائياً عند الشراء
حساب التكاليف الإجمالية
إدارة المبيعات

إنشاء الفواتير للعملاء
متابعة عمليات الدفع
خصومات وعروض خاصة
إدارة المخزون والمخازن

تتبع كميات المنتجات
إدارة مواقع التخزين
تنبيهات عند انخفاض المخزون
إدارة العملاء والموردين

إضافة وتحديث بيانات العملاء والموردين
سجل التعاملات لكل عميل/مورد
إدارة الحسابات

الإيرادات والمصروفات
كشف الحسابات الختامية
إدارة الأصول والخصوم
التقارير المالية والتفصيلية

تقارير المبيعات والمشتريات
أرباح وخسائر
رسوم بيانية توضيحية
إدارة بيانات الشركة

معلومات الشركة والشعار
تخصيص الفواتير والتقارير
التقنيات المطلوبة:

بايثون (Django أو Flask) للواجهة الخلفية
Bootstrap + JavaScript + HTML/CSS للواجهة الأمامية
PostgreSQL أو MySQL لقواعد البيانات
استخدام REST API في النظام
يجب أن يكون البرنامج قوياً وآمناً، مع دعم تعدد المستخدمين، وأن يكون قابلاً للتطوير مستقبلاً. أضف لوحة تحكم Dashboard حديثة مع رسوم بيانية توضح بيانات الحسابات والمخزون."