<Application x:Class="AccountingApp.UI.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:AccountingApp.UI"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="SimpleLoginWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Cyan" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />

                <!-- Custom Styles -->
                <ResourceDictionary>
                    <!-- Arabic Font -->
                    <Style TargetType="{x:Type Control}" x:Key="ArabicFont">
                        <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial" />
                        <Setter Property="FlowDirection" Value="RightToLeft" />
                    </Style>

                    <!-- Window Style -->
                    <Style TargetType="{x:Type Window}" x:Key="MaterialWindow">
                        <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial" />
                        <Setter Property="FlowDirection" Value="RightToLeft" />
                        <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}" />
                        <Setter Property="TextElement.Foreground" Value="{DynamicResource MaterialDesignBody}" />
                        <Setter Property="TextElement.FontWeight" Value="Regular" />
                        <Setter Property="TextElement.FontSize" Value="13" />
                        <Setter Property="TextOptions.TextFormattingMode" Value="Ideal" />
                        <Setter Property="TextOptions.TextRenderingMode" Value="Auto" />
                    </Style>

                    <!-- Button Styles -->
                    <Style TargetType="{x:Type Button}" x:Key="PrimaryButton" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                        <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial" />
                        <Setter Property="FontSize" Value="14" />
                        <Setter Property="Padding" Value="16,8" />
                        <Setter Property="Margin" Value="4" />
                    </Style>

                    <Style TargetType="{x:Type Button}" x:Key="SecondaryButton" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
                        <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial" />
                        <Setter Property="FontSize" Value="14" />
                        <Setter Property="Padding" Value="16,8" />
                        <Setter Property="Margin" Value="4" />
                    </Style>

                    <!-- TextBox Style -->
                    <Style TargetType="{x:Type TextBox}" x:Key="MaterialTextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
                        <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial" />
                        <Setter Property="FontSize" Value="14" />
                        <Setter Property="Margin" Value="4" />
                        <Setter Property="Padding" Value="8" />
                    </Style>

                    <!-- PasswordBox Style -->
                    <Style TargetType="{x:Type PasswordBox}" x:Key="MaterialPasswordBox" BasedOn="{StaticResource MaterialDesignOutlinedPasswordBox}">
                        <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial" />
                        <Setter Property="FontSize" Value="14" />
                        <Setter Property="Margin" Value="4" />
                        <Setter Property="Padding" Value="8" />
                    </Style>

                    <!-- Label Style -->
                    <Style TargetType="{x:Type Label}" x:Key="MaterialLabel">
                        <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial" />
                        <Setter Property="FontSize" Value="14" />
                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}" />
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
