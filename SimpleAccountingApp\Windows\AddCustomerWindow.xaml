<Window x:Class="SimpleAccountingApp.Windows.AddCustomerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة عميل جديد - نظام المحاسبة المالية" 
        Height="650" Width="700"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#4CAF50" Padding="15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="👥" FontSize="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock x:Name="WindowTitle" 
                          Text="إضافة عميل جديد" 
                          FontSize="18" 
                          FontWeight="Bold" 
                          Foreground="White" 
                          VerticalAlignment="Center"/>
            </StackPanel>
        </Border>
        
        <!-- Form Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- Right Column -->
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    <!-- Customer Code -->
                    <TextBlock Text="رقم العميل *" FontWeight="Bold" Margin="0,10,0,5"/>
                    <TextBox x:Name="CustomerCodeTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"/>
                    
                    <!-- Customer Name -->
                    <TextBlock Text="اسم العميل *" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="CustomerNameTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"/>
                    
                    <!-- Customer Type -->
                    <TextBlock Text="نوع العميل *" FontWeight="Bold" Margin="0,15,0,5"/>
                    <ComboBox x:Name="CustomerTypeComboBox" 
                             Height="35" 
                             Padding="10"
                             FontSize="14">
                        <ComboBoxItem Content="فرد"/>
                        <ComboBoxItem Content="شركة"/>
                        <ComboBoxItem Content="مؤسسة"/>
                    </ComboBox>
                    
                    <!-- Phone -->
                    <TextBlock Text="الهاتف" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="PhoneTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"/>
                    
                    <!-- Mobile -->
                    <TextBlock Text="الجوال" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="MobileTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"/>
                    
                    <!-- Email -->
                    <TextBlock Text="البريد الإلكتروني" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="EmailTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"/>
                </StackPanel>
                
                <!-- Left Column -->
                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                    <!-- Address -->
                    <TextBlock Text="العنوان" FontWeight="Bold" Margin="0,10,0,5"/>
                    <TextBox x:Name="AddressTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"/>
                    
                    <!-- City -->
                    <TextBlock Text="المدينة" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="CityTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"/>
                    
                    <!-- Country -->
                    <TextBlock Text="الدولة" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="CountryTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"
                            Text="السعودية"/>
                    
                    <!-- Tax Number -->
                    <TextBlock Text="الرقم الضريبي" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="TaxNumberTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"/>
                    
                    <!-- Credit Limit -->
                    <TextBlock Text="حد الائتمان" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="CreditLimitTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"
                            Text="0"/>
                    
                    <!-- Payment Terms -->
                    <TextBlock Text="مدة السداد (أيام)" FontWeight="Bold" Margin="0,15,0,5"/>
                    <TextBox x:Name="PaymentTermsTextBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"
                            Text="30"/>
                </StackPanel>
            </Grid>
        </ScrollViewer>
        
        <!-- Bottom Section -->
        <Border Grid.Row="2" Background="#F5F5F5" Padding="15" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- Is Active Checkbox -->
                <CheckBox x:Name="IsActiveCheckBox" 
                         Grid.Column="0"
                         Content="عميل نشط" 
                         IsChecked="True" 
                         VerticalAlignment="Center"
                         FontWeight="Bold"/>
                
                <!-- Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="SaveButton" 
                           Content="💾 حفظ" 
                           Background="#4CAF50" 
                           Foreground="White" 
                           BorderThickness="0"
                           Padding="20,10" 
                           Margin="10,0"
                           FontSize="14"
                           FontWeight="Bold"
                           Click="SaveButton_Click"/>
                    
                    <Button x:Name="CancelButton" 
                           Content="❌ إلغاء" 
                           Background="#F44336" 
                           Foreground="White" 
                           BorderThickness="0"
                           Padding="20,10" 
                           Margin="10,0"
                           FontSize="14"
                           FontWeight="Bold"
                           Click="CancelButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
