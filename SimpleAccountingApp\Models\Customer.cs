using System.ComponentModel.DataAnnotations;

namespace SimpleAccountingApp.Models
{
    public class Customer
    {
        [Key]
        public int CustomerId { get; set; }

        [MaxLength(50)]
        public string CustomerCode { get; set; } = "";

        [Required]
        [MaxLength(200)]
        public string CustomerName { get; set; } = "";

        [MaxLength(50)]
        public string CustomerType { get; set; } = "";

        [MaxLength(500)]
        public string Address { get; set; } = "";

        [MaxLength(100)]
        public string City { get; set; } = "";

        [MaxLength(100)]
        public string Country { get; set; } = "السعودية";

        [MaxLength(50)]
        public string Phone { get; set; } = "";

        [MaxLength(50)]
        public string Mobile { get; set; } = "";

        [MaxLength(100)]
        public string Email { get; set; } = "";

        [MaxLength(50)]
        public string TaxNumber { get; set; } = "";

        public decimal CreditLimit { get; set; }
        public decimal CurrentBalance { get; set; }
        public int PaymentTerms { get; set; } = 30;
        public bool IsActive { get; set; } = true;

        [MaxLength(1000)]
        public string Notes { get; set; } = "";

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public string IsActiveText => IsActive ? "نشط" : "غير نشط";
    }
}
