<Window x:Class="SimpleAccountingApp.Windows.LeavesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الإجازات" Height="600" Width="1000"
        WindowStartupLocation="CenterScreen" FlowDirection="RightToLeft">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Name="TitleTextBlock" Text="إدارة الإجازات" 
                   FontSize="20" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,10"/>

        <!-- معلومات الموظف -->
        <Border Grid.Row="1" BorderBrush="Gray" BorderThickness="1" 
                Padding="10" Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Name="EmployeeInfoTextBlock" FontSize="14" FontWeight="Bold"/>
            </StackPanel>
        </Border>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="0,0,0,20">
            <Button Name="AddLeaveButton" Content="طلب إجازة" 
                    Width="120" Click="AddLeaveButton_Click"/>
            <Button Name="EditLeaveButton" Content="تعديل" 
                    Width="100" Margin="10,0,0,0" Click="EditLeaveButton_Click"/>
            <Button Name="DeleteLeaveButton" Content="حذف" 
                    Width="100" Margin="10,0,0,0" Click="DeleteLeaveButton_Click"/>
            <Button Name="ApproveLeaveButton" Content="اعتماد" 
                    Width="100" Margin="10,0,0,0" Click="ApproveLeaveButton_Click"/>
            <Button Name="RefreshButton" Content="تحديث" 
                    Width="100" Margin="10,0,0,0" Click="RefreshButton_Click"/>
        </StackPanel>

        <!-- جدول الإجازات -->
        <DataGrid Grid.Row="3" Name="LeavesDataGrid" 
                  AutoGenerateColumns="False" 
                  CanUserAddRows="False" 
                  CanUserDeleteRows="False"
                  IsReadOnly="True"
                  GridLinesVisibility="All"
                  HeadersVisibility="Column"
                  SelectionMode="Single">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="رقم الإجازة" Binding="{Binding LeaveNumber}" Width="120"/>
                <DataGridTextColumn Header="نوع الإجازة" Binding="{Binding LeaveTypeText}" Width="100"/>
                <DataGridTextColumn Header="تاريخ البداية" Binding="{Binding StartDate, StringFormat='{}{0:yyyy/MM/dd}'}" Width="120"/>
                <DataGridTextColumn Header="تاريخ النهاية" Binding="{Binding EndDate, StringFormat='{}{0:yyyy/MM/dd}'}" Width="120"/>
                <DataGridTextColumn Header="عدد الأيام" Binding="{Binding Days}" Width="100"/>
                <DataGridTextColumn Header="السبب" Binding="{Binding Reason}" Width="200"/>
                <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="100"/>
                <DataGridTextColumn Header="المعتمد من" Binding="{Binding ApprovedBy}" Width="120"/>
                <DataGridTextColumn Header="تاريخ الاعتماد" Binding="{Binding ApprovedDate, StringFormat='{}{0:yyyy/MM/dd}'}" Width="120"/>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</Window>
