using System.Collections.ObjectModel;
using System.Windows;
using SimpleAccountingApp.Helpers;
using SimpleAccountingApp.Models;
using SimpleAccountingApp.Data;
using Microsoft.EntityFrameworkCore;

namespace SimpleAccountingApp.Windows
{
    public partial class StockReportWindow : Window
    {
        private List<Product> _products;
        private ObservableCollection<StockReportItem> _stockReport;
        private AccountingDbContext _context;

        public StockReportWindow(List<Product>? products = null)
        {
            InitializeComponent();
            _context = new AccountingDbContext();
            _products = products ?? new List<Product>();
            LoadStockReport();
        }

        private async void LoadStockReport()
        {
            try
            {
                await _context.InitializeDatabaseAsync();

                // إذا لم يتم تمرير منتجات، تحميلها من قاعدة البيانات
                if (!_products.Any())
                {
                    _products = await _context.Products.ToListAsync();

                    // إذا لم توجد منتجات، إنشاء منتجات تجريبية
                    if (!_products.Any())
                    {
                        await CreateSampleProducts();
                        _products = await _context.Products.ToListAsync();
                    }
                }

                _stockReport = new ObservableCollection<StockReportItem>();

                foreach (var product in _products)
                {
                    var stockItem = new StockReportItem
                    {
                        ProductCode = product.ProductCode,
                        ProductName = product.ProductName,
                        Category = product.Category,
                        Unit = product.Unit,
                        CurrentStock = product.CurrentStock,
                        MinimumStock = product.MinimumStock,
                        PurchasePrice = product.PurchasePrice,
                        StockValue = product.CurrentStock * product.PurchasePrice,
                        IsLowStock = product.CurrentStock < product.MinimumStock,
                        StockStatus = GetStockStatus(product)
                    };

                    _stockReport.Add(stockItem);
                }

                StockReportDataGrid.ItemsSource = _stockReport;
                UpdateSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تقرير المخزون: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task CreateSampleProducts()
        {
            var sampleProducts = new[]
            {
                new Product { ProductCode = "P001", ProductName = "لابتوب ديل", Category = "إلكترونيات", Unit = "قطعة", CurrentStock = 15, PurchasePrice = 2500, SalePrice = 2800, MinimumStock = 5 },
                new Product { ProductCode = "P002", ProductName = "طابعة HP", Category = "إلكترونيات", Unit = "قطعة", CurrentStock = 8, PurchasePrice = 800, SalePrice = 950, MinimumStock = 3 },
                new Product { ProductCode = "P003", ProductName = "كرسي مكتب", Category = "أثاث", Unit = "قطعة", CurrentStock = 25, PurchasePrice = 350, SalePrice = 420, MinimumStock = 10 },
                new Product { ProductCode = "P004", ProductName = "طاولة اجتماعات", Category = "أثاث", Unit = "قطعة", CurrentStock = 5, PurchasePrice = 1200, SalePrice = 1450, MinimumStock = 2 },
                new Product { ProductCode = "P005", ProductName = "ورق A4", Category = "مستلزمات مكتبية", Unit = "علبة", CurrentStock = 50, PurchasePrice = 25, SalePrice = 30, MinimumStock = 20 },
                new Product { ProductCode = "P006", ProductName = "أقلام حبر", Category = "مستلزمات مكتبية", Unit = "علبة", CurrentStock = 30, PurchasePrice = 15, SalePrice = 20, MinimumStock = 15 }
            };

            _context.Products.AddRange(sampleProducts);
            await _context.SaveChangesAsync();
        }

        private string GetStockStatus(Product product)
        {
            if (product.CurrentStock == 0)
                return "نفد المخزون";
            else if (product.CurrentStock < product.MinimumStock)
                return "مخزون منخفض";
            else
                return "مخزون جيد";
        }

        private void UpdateSummary()
        {
            var totalProducts = _stockReport.Count;
            var lowStockItems = _stockReport.Count(item => item.IsLowStock);
            var outOfStockItems = _stockReport.Count(item => item.CurrentStock == 0);
            var totalValue = _stockReport.Sum(item => item.StockValue);

            TotalProductsText.Text = totalProducts.ToString();
            LowStockText.Text = lowStockItems.ToString();
            OutOfStockText.Text = outOfStockItems.ToString();
            TotalValueText.Text = totalValue.ToString("N2") + " ريال";
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            ReportHelper.PrintDataGrid(StockReportDataGrid, "تقرير المخزون");
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            ReportHelper.ExportDataGridToExcel(StockReportDataGrid, "تقرير المخزون");
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadStockReport();
            MessageBox.Show("تم تحديث التقرير بنجاح!", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    // نموذج بيانات تقرير المخزون
    public class StockReportItem
    {
        public string ProductCode { get; set; } = "";
        public string ProductName { get; set; } = "";
        public string Category { get; set; } = "";
        public string Unit { get; set; } = "";
        public decimal CurrentStock { get; set; }
        public decimal MinimumStock { get; set; }
        public decimal PurchasePrice { get; set; }
        public decimal StockValue { get; set; }
        public bool IsLowStock { get; set; }
        public string StockStatus { get; set; } = "";
    }
}
