using System.Collections.ObjectModel;
using System.Windows;
using SimpleAccountingApp.Helpers;

namespace SimpleAccountingApp.Windows
{
    public partial class StockReportWindow : Window
    {
        private List<Product> _products;
        private ObservableCollection<StockReportItem> _stockReport;

        public StockReportWindow(List<Product> products)
        {
            InitializeComponent();
            _products = products;
            LoadStockReport();
        }

        private void LoadStockReport()
        {
            _stockReport = new ObservableCollection<StockReportItem>();
            
            foreach (var product in _products)
            {
                var stockItem = new StockReportItem
                {
                    ProductCode = product.ProductCode,
                    ProductName = product.ProductName,
                    Category = product.Category,
                    Unit = product.Unit,
                    CurrentStock = product.CurrentStock,
                    MinimumStock = product.MinimumStock,
                    PurchasePrice = product.PurchasePrice,
                    StockValue = product.CurrentStock * product.PurchasePrice,
                    IsLowStock = product.IsLowStock,
                    StockStatus = GetStockStatus(product)
                };
                
                _stockReport.Add(stockItem);
            }
            
            StockReportDataGrid.ItemsSource = _stockReport;
            UpdateSummary();
        }

        private string GetStockStatus(Product product)
        {
            if (product.CurrentStock == 0)
                return "نفد المخزون";
            else if (product.IsLowStock)
                return "مخزون منخفض";
            else
                return "مخزون جيد";
        }

        private void UpdateSummary()
        {
            var totalProducts = _stockReport.Count;
            var lowStockItems = _stockReport.Count(item => item.IsLowStock);
            var outOfStockItems = _stockReport.Count(item => item.CurrentStock == 0);
            var totalValue = _stockReport.Sum(item => item.StockValue);

            TotalProductsText.Text = totalProducts.ToString();
            LowStockText.Text = lowStockItems.ToString();
            OutOfStockText.Text = outOfStockItems.ToString();
            TotalValueText.Text = totalValue.ToString("N2") + " ريال";
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            ReportHelper.PrintDataGrid(StockReportDataGrid, "تقرير المخزون");
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            ReportHelper.ExportDataGridToExcel(StockReportDataGrid, "تقرير المخزون");
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadStockReport();
            MessageBox.Show("تم تحديث التقرير بنجاح!", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    // نموذج بيانات تقرير المخزون
    public class StockReportItem
    {
        public string ProductCode { get; set; } = "";
        public string ProductName { get; set; } = "";
        public string Category { get; set; } = "";
        public string Unit { get; set; } = "";
        public decimal CurrentStock { get; set; }
        public decimal MinimumStock { get; set; }
        public decimal PurchasePrice { get; set; }
        public decimal StockValue { get; set; }
        public bool IsLowStock { get; set; }
        public string StockStatus { get; set; } = "";
    }
}
