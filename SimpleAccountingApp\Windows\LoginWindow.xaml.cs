using System.Windows;
using System.Windows.Input;

namespace SimpleAccountingApp.Windows
{
    public partial class LoginWindow : Window
    {
        public string LoggedInUser { get; private set; } = "";

        public LoginWindow()
        {
            InitializeComponent();
            LoadDefaultCredentials();
        }

        private void LoadDefaultCredentials()
        {
            // بيانات افتراضية للاختبار
            UsernameTextBox.Text = "admin";
            PasswordBox.Password = "123456";
        }

        private void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            string username = UsernameTextBox.Text.Trim();
            string password = PasswordBox.Password;

            if (ValidateLogin(username, password))
            {
                LoggedInUser = username;

                // عرض رسالة ترحيب
                MessageBox.Show($"مرحباً {username}، تم تسجيل الدخول بنجاح!",
                    "تسجيل دخول ناجح", MessageBoxButton.OK, MessageBoxImage.Information);

                this.DialogResult = true;
                this.Close();
            }
            else
            {
                MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ في تسجيل الدخول", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                PasswordBox.Clear();
                PasswordBox.Focus();
            }
        }

        private bool ValidateLogin(string username, string password)
        {
            // في التطبيق الحقيقي، سيتم التحقق من قاعدة البيانات
            var validUsers = new Dictionary<string, string>
            {
                { "admin", "123456" },
                { "محاسب", "password" },
                { "مدير", "manager123" },
                { "مستخدم", "user123" }
            };

            return validUsers.ContainsKey(username) && validUsers[username] == password;
        }

        private void CreateAccountButton_Click(object sender, RoutedEventArgs e)
        {
            var createUserWindow = new CreateUserWindow();
            if (createUserWindow.ShowDialog() == true)
            {
                MessageBox.Show("تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول.", 
                    "نجح إنشاء الحساب", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ForgotPassword_Click(object sender, MouseButtonEventArgs e)
        {
            MessageBox.Show("للحصول على كلمة مرور جديدة، يرجى التواصل مع مدير النظام", 
                "استعادة كلمة المرور", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            this.DialogResult = false;
            this.Close();
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                LoginButton_Click(this, new RoutedEventArgs());
            }
            base.OnKeyDown(e);
        }
    }
}
