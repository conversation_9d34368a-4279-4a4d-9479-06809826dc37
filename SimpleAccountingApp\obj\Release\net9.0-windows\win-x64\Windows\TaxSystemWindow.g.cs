﻿#pragma checksum "..\..\..\..\..\Windows\TaxSystemWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "873E2527E610ABA7F31B9E6B869C7DCB1CE0902C"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleAccountingApp.Windows {
    
    
    /// <summary>
    /// TaxSystemWindow
    /// </summary>
    public partial class TaxSystemWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 79 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button VATRegistrationButton;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button VATReturnsButton;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button VATCalculatorButton;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ZakatCalculationButton;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ZakatReturnsButton;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ZakatPaymentButton;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EInvoiceIntegrationButton;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button QRCodeButton;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InvoiceValidationButton;
        
        #line default
        #line hidden
        
        
        #line 277 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TaxReportsButton;
        
        #line default
        #line hidden
        
        
        #line 294 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ComplianceCheckButton;
        
        #line default
        #line hidden
        
        
        #line 311 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button HelpSupportButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleAccountingApp;component/windows/taxsystemwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 33 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.VATRegistrationButton = ((System.Windows.Controls.Button)(target));
            
            #line 86 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
            this.VATRegistrationButton.Click += new System.Windows.RoutedEventHandler(this.VATRegistrationButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.VATReturnsButton = ((System.Windows.Controls.Button)(target));
            
            #line 103 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
            this.VATReturnsButton.Click += new System.Windows.RoutedEventHandler(this.VATReturnsButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.VATCalculatorButton = ((System.Windows.Controls.Button)(target));
            
            #line 120 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
            this.VATCalculatorButton.Click += new System.Windows.RoutedEventHandler(this.VATCalculatorButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ZakatCalculationButton = ((System.Windows.Controls.Button)(target));
            
            #line 152 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
            this.ZakatCalculationButton.Click += new System.Windows.RoutedEventHandler(this.ZakatCalculationButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ZakatReturnsButton = ((System.Windows.Controls.Button)(target));
            
            #line 169 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
            this.ZakatReturnsButton.Click += new System.Windows.RoutedEventHandler(this.ZakatReturnsButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ZakatPaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 186 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
            this.ZakatPaymentButton.Click += new System.Windows.RoutedEventHandler(this.ZakatPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.EInvoiceIntegrationButton = ((System.Windows.Controls.Button)(target));
            
            #line 218 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
            this.EInvoiceIntegrationButton.Click += new System.Windows.RoutedEventHandler(this.EInvoiceIntegrationButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.QRCodeButton = ((System.Windows.Controls.Button)(target));
            
            #line 235 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
            this.QRCodeButton.Click += new System.Windows.RoutedEventHandler(this.QRCodeButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.InvoiceValidationButton = ((System.Windows.Controls.Button)(target));
            
            #line 252 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
            this.InvoiceValidationButton.Click += new System.Windows.RoutedEventHandler(this.InvoiceValidationButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.TaxReportsButton = ((System.Windows.Controls.Button)(target));
            
            #line 284 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
            this.TaxReportsButton.Click += new System.Windows.RoutedEventHandler(this.TaxReportsButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.ComplianceCheckButton = ((System.Windows.Controls.Button)(target));
            
            #line 301 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
            this.ComplianceCheckButton.Click += new System.Windows.RoutedEventHandler(this.ComplianceCheckButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.HelpSupportButton = ((System.Windows.Controls.Button)(target));
            
            #line 318 "..\..\..\..\..\Windows\TaxSystemWindow.xaml"
            this.HelpSupportButton.Click += new System.Windows.RoutedEventHandler(this.HelpSupportButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

