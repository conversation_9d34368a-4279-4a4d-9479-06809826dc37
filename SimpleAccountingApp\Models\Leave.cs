using System.ComponentModel.DataAnnotations;

namespace SimpleAccountingApp.Models
{
    public enum LeaveType
    {
        Annual = 1,        // سنوية
        Sick = 2,          // مرضية
        Emergency = 3,     // طارئة
        Maternity = 4,     // أمومة
        Paternity = 5,     // أبوة
        Unpaid = 6         // بدون راتب
    }

    public enum LeaveStatus
    {
        Pending = 1,       // معلق
        Approved = 2,      // معتمد
        Rejected = 3,      // مرفوض
        Cancelled = 4      // ملغي
    }

    public class Leave
    {
        [Key]
        public int LeaveId { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string LeaveNumber { get; set; } = "";
        
        public int EmployeeId { get; set; }
        public Employee? Employee { get; set; }
        
        public LeaveType LeaveType { get; set; }
        
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        
        public int Days { get; set; }
        
        [MaxLength(500)]
        public string Reason { get; set; } = "";
        
        public LeaveStatus Status { get; set; } = LeaveStatus.Pending;
        
        [MaxLength(100)]
        public string ApprovedBy { get; set; } = "";
        
        public DateTime? ApprovedDate { get; set; }
        
        [MaxLength(500)]
        public string RejectionReason { get; set; } = "";
        
        [MaxLength(1000)]
        public string Notes { get; set; } = "";
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        // خصائص محسوبة
        public string LeaveTypeText => GetLeaveTypeText(LeaveType);
        public string StatusText => GetStatusText(Status);
        public int CalculatedDays => (EndDate - StartDate).Days + 1;
        
        private static string GetLeaveTypeText(LeaveType type)
        {
            return type switch
            {
                LeaveType.Annual => "سنوية",
                LeaveType.Sick => "مرضية",
                LeaveType.Emergency => "طارئة",
                LeaveType.Maternity => "أمومة",
                LeaveType.Paternity => "أبوة",
                LeaveType.Unpaid => "بدون راتب",
                _ => "غير محدد"
            };
        }
        
        private static string GetStatusText(LeaveStatus status)
        {
            return status switch
            {
                LeaveStatus.Pending => "معلق",
                LeaveStatus.Approved => "معتمد",
                LeaveStatus.Rejected => "مرفوض",
                LeaveStatus.Cancelled => "ملغي",
                _ => "غير محدد"
            };
        }
    }
}
