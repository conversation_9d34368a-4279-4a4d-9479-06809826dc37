using System.ComponentModel.DataAnnotations;

namespace SimpleAccountingApp.Models
{
    public enum ExpenseType
    {
        Cash = 1,          // نقدي
        Bank = 2,          // بنكي
        Credit = 3,        // آجل
        Check = 4          // شيك
    }

    public enum ExpenseCategory
    {
        OfficeSupplies = 1,     // مستلزمات مكتبية
        Utilities = 2,          // مرافق
        Rent = 3,              // إيجار
        Salaries = 4,          // رواتب
        Marketing = 5,         // تسويق
        Travel = 6,            // سفر
        Maintenance = 7,       // صيانة
        Insurance = 8,         // تأمين
        Legal = 9,             // قانونية
        Other = 10             // أخرى
    }

    public class Expense
    {
        [Key]
        public int ExpenseId { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string ExpenseNumber { get; set; } = "";
        
        public DateTime ExpenseDate { get; set; } = DateTime.Now;
        
        [Required]
        [MaxLength(200)]
        public string ExpenseName { get; set; } = "";
        
        public ExpenseCategory Category { get; set; }
        
        public ExpenseType PaymentType { get; set; }
        
        public decimal Amount { get; set; }
        
        [MaxLength(200)]
        public string Vendor { get; set; } = "";
        
        [MaxLength(100)]
        public string InvoiceNumber { get; set; } = "";
        
        public int? BankAccountId { get; set; }
        public BankAccount? BankAccount { get; set; }
        
        [MaxLength(500)]
        public string Description { get; set; } = "";
        
        [MaxLength(1000)]
        public string Notes { get; set; } = "";
        
        public bool IsApproved { get; set; } = false;
        
        [MaxLength(100)]
        public string ApprovedBy { get; set; } = "";
        
        public DateTime? ApprovedDate { get; set; }
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        // خصائص محسوبة
        public string CategoryText => GetCategoryText(Category);
        public string PaymentTypeText => GetPaymentTypeText(PaymentType);
        public string ApprovalStatus => IsApproved ? "معتمد" : "غير معتمد";
        
        private static string GetCategoryText(ExpenseCategory category)
        {
            return category switch
            {
                ExpenseCategory.OfficeSupplies => "مستلزمات مكتبية",
                ExpenseCategory.Utilities => "مرافق",
                ExpenseCategory.Rent => "إيجار",
                ExpenseCategory.Salaries => "رواتب",
                ExpenseCategory.Marketing => "تسويق",
                ExpenseCategory.Travel => "سفر",
                ExpenseCategory.Maintenance => "صيانة",
                ExpenseCategory.Insurance => "تأمين",
                ExpenseCategory.Legal => "قانونية",
                ExpenseCategory.Other => "أخرى",
                _ => "غير محدد"
            };
        }
        
        private static string GetPaymentTypeText(ExpenseType type)
        {
            return type switch
            {
                ExpenseType.Cash => "نقدي",
                ExpenseType.Bank => "بنكي",
                ExpenseType.Credit => "آجل",
                ExpenseType.Check => "شيك",
                _ => "غير محدد"
            };
        }
    }
}
