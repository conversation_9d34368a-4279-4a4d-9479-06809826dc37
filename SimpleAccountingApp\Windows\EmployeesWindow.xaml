<Window x:Class="SimpleAccountingApp.Windows.EmployeesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة شؤون الموظفين" Height="600" Width="1000"
        WindowStartupLocation="CenterScreen" FlowDirection="RightToLeft">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="إدارة شؤون الموظفين" 
                   FontSize="24" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="0,0,0,20">
            <Button Name="AddEmployeeButton" Content="إضافة موظف" 
                    Width="120" Click="AddEmployeeButton_Click"/>
            <Button Name="EditEmployeeButton" Content="تعديل" 
                    Width="100" Margin="10,0,0,0" Click="EditEmployeeButton_Click"/>
            <Button Name="DeleteEmployeeButton" Content="حذف" 
                    Width="100" Margin="10,0,0,0" Click="DeleteEmployeeButton_Click"/>
            <Button Name="PayrollButton" Content="الرواتب" 
                    Width="100" Margin="10,0,0,0" Click="PayrollButton_Click"/>
            <Button Name="LeavesButton" Content="الإجازات" 
                    Width="100" Margin="10,0,0,0" Click="LeavesButton_Click"/>
            <Button Name="RefreshButton" Content="تحديث" 
                    Width="100" Margin="10,0,0,0" Click="RefreshButton_Click"/>
        </StackPanel>

        <!-- جدول البيانات -->
        <DataGrid Grid.Row="2" Name="EmployeesDataGrid" 
                  AutoGenerateColumns="False" 
                  CanUserAddRows="False" 
                  CanUserDeleteRows="False"
                  IsReadOnly="True"
                  GridLinesVisibility="All"
                  HeadersVisibility="Column"
                  SelectionMode="Single">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="كود الموظف" Binding="{Binding EmployeeCode}" Width="100"/>
                <DataGridTextColumn Header="الاسم الكامل" Binding="{Binding FullName}" Width="200"/>
                <DataGridTextColumn Header="المسمى الوظيفي" Binding="{Binding JobTitle}" Width="150"/>
                <DataGridTextColumn Header="القسم" Binding="{Binding Department}" Width="120"/>
                <DataGridTextColumn Header="الراتب الأساسي" Binding="{Binding BasicSalary, StringFormat='{}{0:N2} ريال'}" Width="120"/>
                <DataGridTextColumn Header="صافي الراتب" Binding="{Binding NetSalary, StringFormat='{}{0:N2} ريال'}" Width="120"/>
                <DataGridTextColumn Header="تاريخ التوظيف" Binding="{Binding HireDate, StringFormat='{}{0:yyyy/MM/dd}'}" Width="120"/>
                <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="100"/>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</Window>
