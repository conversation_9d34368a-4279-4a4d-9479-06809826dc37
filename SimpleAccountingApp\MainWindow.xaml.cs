﻿using System.Windows;
using System.Windows.Threading;
using SimpleAccountingApp.Windows;

namespace SimpleAccountingApp;

public partial class MainWindow : Window
{
    private DispatcherTimer timer;

    public MainWindow()
    {
        InitializeComponent();

        // إعداد المؤقت للتاريخ والوقت
        timer = new DispatcherTimer();
        timer.Interval = TimeSpan.FromSeconds(1);
        timer.Tick += Timer_Tick;
        timer.Start();

        // عرض التاريخ والوقت فوراً
        UpdateDateTime();

        // التأكد من أن النافذة تظهر في المقدمة
        this.WindowState = WindowState.Normal;
        this.Activate();
        this.Focus();

        // إضافة معالج للحدث Loaded
        this.Loaded += MainWindow_Loaded;
    }

    private void MainWindow_Loaded(object sender, RoutedEventArgs e)
    {
        // التأكد من ظهور النافذة عند التحميل
        this.Activate();
        this.Topmost = true;
        this.Topmost = false; // إعادة تعيين لتجنب البقاء في المقدمة دائماً
    }

    private void Timer_Tick(object? sender, EventArgs e)
    {
        UpdateDateTime();
    }

    private void UpdateDateTime()
    {
        DateTimeText.Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm:ss");
    }

    // أحداث الأزرار
    private void AccountsButton_Click(object sender, RoutedEventArgs e)
    {
        var accountsWindow = new AccountsWindow();
        accountsWindow.ShowDialog();
    }

    private void CustomersButton_Click(object sender, RoutedEventArgs e)
    {
        var customersWindow = new CustomersWindow();
        customersWindow.ShowDialog();
    }

    private void SuppliersButton_Click(object sender, RoutedEventArgs e)
    {
        var suppliersWindow = new SuppliersWindow();
        suppliersWindow.ShowDialog();
    }

    private void ProductsButton_Click(object sender, RoutedEventArgs e)
    {
        var productsWindow = new ProductsWindow();
        productsWindow.ShowDialog();
    }

    private void WarehousesButton_Click(object sender, RoutedEventArgs e)
    {
        var warehousesWindow = new WarehousesWindow();
        warehousesWindow.ShowDialog();
    }

    private void SalesButton_Click(object sender, RoutedEventArgs e)
    {
        var salesInvoicesWindow = new SalesInvoicesWindow();
        salesInvoicesWindow.ShowDialog();
    }

    private void PurchasesButton_Click(object sender, RoutedEventArgs e)
    {
        var purchaseInvoicesWindow = new PurchaseInvoicesWindow();
        purchaseInvoicesWindow.ShowDialog();
    }

    private void ReportsButton_Click(object sender, RoutedEventArgs e)
    {
        var reportsWindow = new FinancialReportsWindow();
        reportsWindow.ShowDialog();
    }

    private void NewSaleButton_Click(object sender, RoutedEventArgs e)
    {
        var addSalesInvoiceWindow = new AddSalesInvoiceWindow();
        addSalesInvoiceWindow.ShowDialog();
    }

    private void NewPurchaseButton_Click(object sender, RoutedEventArgs e)
    {
        var addPurchaseInvoiceWindow = new AddPurchaseInvoiceWindow();
        addPurchaseInvoiceWindow.ShowDialog();
    }

    private void ViewReportsButton_Click(object sender, RoutedEventArgs e)
    {
        var reportsWindow = new FinancialReportsWindow();
        reportsWindow.ShowDialog();
    }

    private void UsersButton_Click(object sender, RoutedEventArgs e)
    {
        var usersWindow = new UsersManagementWindow();
        usersWindow.ShowDialog();
    }

    private void TaxButton_Click(object sender, RoutedEventArgs e)
    {
        var taxWindow = new TaxSystemWindow();
        taxWindow.ShowDialog();
    }

    private void BankAccountsButton_Click(object sender, RoutedEventArgs e)
    {
        var bankAccountsWindow = new BankAccountsWindow();
        bankAccountsWindow.ShowDialog();
    }

    private void ExpensesButton_Click(object sender, RoutedEventArgs e)
    {
        var expensesWindow = new ExpensesWindow();
        expensesWindow.ShowDialog();
    }

    private void EmployeesButton_Click(object sender, RoutedEventArgs e)
    {
        var employeesWindow = new EmployeesWindow();
        employeesWindow.ShowDialog();
    }

    private void PayrollManagementButton_Click(object sender, RoutedEventArgs e)
    {
        var payrollWindow = new PayrollManagementWindow();
        payrollWindow.ShowDialog();
    }

    protected override void OnClosed(EventArgs e)
    {
        timer?.Stop();
        base.OnClosed(e);
    }
}