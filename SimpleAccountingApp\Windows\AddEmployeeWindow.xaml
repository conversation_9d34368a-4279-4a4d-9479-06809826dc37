<Window x:Class="SimpleAccountingApp.Windows.AddEmployeeWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة موظف" Height="700" Width="600"
        WindowStartupLocation="CenterScreen" FlowDirection="RightToLeft">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Name="TitleTextBlock" Text="إضافة موظف جديد" 
                   FontSize="18" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- نموذج البيانات -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- كود الموظف -->
                <Label Content="كود الموظف:" FontWeight="Bold"/>
                <TextBox Name="EmployeeCodeTextBox" Margin="0,0,0,10"/>

                <!-- الاسم الأول -->
                <Label Content="الاسم الأول:" FontWeight="Bold"/>
                <TextBox Name="FirstNameTextBox" Margin="0,0,0,10"/>

                <!-- الاسم الأخير -->
                <Label Content="الاسم الأخير:" FontWeight="Bold"/>
                <TextBox Name="LastNameTextBox" Margin="0,0,0,10"/>

                <!-- رقم الهوية -->
                <Label Content="رقم الهوية:" FontWeight="Bold"/>
                <TextBox Name="NationalIdTextBox" Margin="0,0,0,10"/>

                <!-- تاريخ الميلاد -->
                <Label Content="تاريخ الميلاد:" FontWeight="Bold"/>
                <DatePicker Name="DateOfBirthPicker" Margin="0,0,0,10"/>

                <!-- الجنس -->
                <Label Content="الجنس:" FontWeight="Bold"/>
                <ComboBox Name="GenderComboBox" Margin="0,0,0,10">
                    <ComboBoxItem Content="ذكر" Tag="Male"/>
                    <ComboBoxItem Content="أنثى" Tag="Female"/>
                </ComboBox>

                <!-- الحالة الاجتماعية -->
                <Label Content="الحالة الاجتماعية:" FontWeight="Bold"/>
                <ComboBox Name="MaritalStatusComboBox" Margin="0,0,0,10">
                    <ComboBoxItem Content="أعزب" Tag="Single"/>
                    <ComboBoxItem Content="متزوج" Tag="Married"/>
                    <ComboBoxItem Content="مطلق" Tag="Divorced"/>
                    <ComboBoxItem Content="أرمل" Tag="Widowed"/>
                </ComboBox>

                <!-- الهاتف -->
                <Label Content="الهاتف:" FontWeight="Bold"/>
                <TextBox Name="PhoneTextBox" Margin="0,0,0,10"/>

                <!-- الجوال -->
                <Label Content="الجوال:" FontWeight="Bold"/>
                <TextBox Name="MobileTextBox" Margin="0,0,0,10"/>

                <!-- البريد الإلكتروني -->
                <Label Content="البريد الإلكتروني:" FontWeight="Bold"/>
                <TextBox Name="EmailTextBox" Margin="0,0,0,10"/>

                <!-- العنوان -->
                <Label Content="العنوان:" FontWeight="Bold"/>
                <TextBox Name="AddressTextBox" Height="60" 
                         TextWrapping="Wrap" AcceptsReturn="True" 
                         VerticalScrollBarVisibility="Auto" Margin="0,0,0,10"/>

                <!-- المدينة -->
                <Label Content="المدينة:" FontWeight="Bold"/>
                <TextBox Name="CityTextBox" Margin="0,0,0,10"/>

                <!-- الدولة -->
                <Label Content="الدولة:" FontWeight="Bold"/>
                <TextBox Name="CountryTextBox" Text="السعودية" Margin="0,0,0,10"/>

                <!-- المسمى الوظيفي -->
                <Label Content="المسمى الوظيفي:" FontWeight="Bold"/>
                <TextBox Name="JobTitleTextBox" Margin="0,0,0,10"/>

                <!-- القسم -->
                <Label Content="القسم:" FontWeight="Bold"/>
                <TextBox Name="DepartmentTextBox" Margin="0,0,0,10"/>

                <!-- تاريخ التوظيف -->
                <Label Content="تاريخ التوظيف:" FontWeight="Bold"/>
                <DatePicker Name="HireDatePicker" Margin="0,0,0,10"/>

                <!-- الراتب الأساسي -->
                <Label Content="الراتب الأساسي:" FontWeight="Bold"/>
                <TextBox Name="BasicSalaryTextBox" Margin="0,0,0,10"/>

                <!-- البدلات -->
                <Label Content="البدلات:" FontWeight="Bold"/>
                <TextBox Name="AllowancesTextBox" Margin="0,0,0,10"/>

                <!-- الخصومات -->
                <Label Content="الخصومات:" FontWeight="Bold"/>
                <TextBox Name="DeductionsTextBox" Margin="0,0,0,10"/>

                <!-- حالة الموظف -->
                <Label Content="حالة الموظف:" FontWeight="Bold"/>
                <ComboBox Name="StatusComboBox" Margin="0,0,0,10">
                    <ComboBoxItem Content="نشط" Tag="Active" IsSelected="True"/>
                    <ComboBoxItem Content="غير نشط" Tag="Inactive"/>
                    <ComboBoxItem Content="في إجازة" Tag="OnLeave"/>
                    <ComboBoxItem Content="منتهي الخدمة" Tag="Terminated"/>
                </ComboBox>

                <!-- ملاحظات -->
                <Label Content="ملاحظات:" FontWeight="Bold"/>
                <TextBox Name="NotesTextBox" Height="60" 
                         TextWrapping="Wrap" AcceptsReturn="True" 
                         VerticalScrollBarVisibility="Auto" Margin="0,0,0,10"/>
            </StackPanel>
        </ScrollViewer>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Name="SaveButton" Content="حفظ" 
                    Width="100" Margin="0,0,10,0" Click="SaveButton_Click"/>
            <Button Name="CancelButton" Content="إلغاء" 
                    Width="100" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
