<Window x:Class="SimpleAccountingApp.Windows.AddLeaveWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="طلب إجازة" Height="500" Width="450"
        WindowStartupLocation="CenterScreen" FlowDirection="RightToLeft">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Name="TitleTextBlock" Text="طلب إجازة جديد" 
                   FontSize="18" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- نموذج البيانات -->
        <StackPanel Grid.Row="1">
            <!-- رقم الإجازة -->
            <Label Content="رقم الإجازة:" FontWeight="Bold"/>
            <TextBox Name="LeaveNumberTextBox" Margin="0,0,0,10" IsReadOnly="True"/>

            <!-- نوع الإجازة -->
            <Label Content="نوع الإجازة:" FontWeight="Bold"/>
            <ComboBox Name="LeaveTypeComboBox" Margin="0,0,0,10">
                <ComboBoxItem Content="سنوية" Tag="Annual"/>
                <ComboBoxItem Content="مرضية" Tag="Sick"/>
                <ComboBoxItem Content="طارئة" Tag="Emergency"/>
                <ComboBoxItem Content="أمومة" Tag="Maternity"/>
                <ComboBoxItem Content="أبوة" Tag="Paternity"/>
                <ComboBoxItem Content="بدون راتب" Tag="Unpaid"/>
            </ComboBox>

            <!-- تاريخ البداية -->
            <Label Content="تاريخ البداية:" FontWeight="Bold"/>
            <DatePicker Name="StartDatePicker" Margin="0,0,0,10" SelectedDateChanged="DatePicker_SelectedDateChanged"/>

            <!-- تاريخ النهاية -->
            <Label Content="تاريخ النهاية:" FontWeight="Bold"/>
            <DatePicker Name="EndDatePicker" Margin="0,0,0,10" SelectedDateChanged="DatePicker_SelectedDateChanged"/>

            <!-- عدد الأيام -->
            <Label Content="عدد الأيام:" FontWeight="Bold"/>
            <TextBox Name="DaysTextBox" Margin="0,0,0,10" IsReadOnly="True"/>

            <!-- السبب -->
            <Label Content="السبب:" FontWeight="Bold"/>
            <TextBox Name="ReasonTextBox" Height="80" 
                     TextWrapping="Wrap" AcceptsReturn="True" 
                     VerticalScrollBarVisibility="Auto" Margin="0,0,0,10"/>

            <!-- ملاحظات -->
            <Label Content="ملاحظات:" FontWeight="Bold"/>
            <TextBox Name="NotesTextBox" Height="60" 
                     TextWrapping="Wrap" AcceptsReturn="True" 
                     VerticalScrollBarVisibility="Auto" Margin="0,0,0,10"/>
        </StackPanel>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Name="SaveButton" Content="حفظ" 
                    Width="100" Margin="0,0,10,0" Click="SaveButton_Click"/>
            <Button Name="CancelButton" Content="إلغاء" 
                    Width="100" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
