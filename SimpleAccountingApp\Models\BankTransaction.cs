using System.ComponentModel.DataAnnotations;

namespace SimpleAccountingApp.Models
{
    public enum BankTransactionType
    {
        Deposit = 1,        // إيداع
        Withdrawal = 2,     // سحب
        Transfer = 3,       // تحويل
        Fee = 4,           // رسوم
        Interest = 5       // فوائد
    }

    public enum TransactionStatus
    {
        Pending = 1,       // معلق
        Completed = 2,     // مكتمل
        Cancelled = 3,     // ملغي
        Failed = 4         // فاشل
    }

    public class BankTransaction
    {
        [Key]
        public int TransactionId { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string TransactionNumber { get; set; } = "";
        
        public DateTime TransactionDate { get; set; } = DateTime.Now;
        
        public int BankAccountId { get; set; }
        public BankAccount? BankAccount { get; set; }
        
        public int? ToBankAccountId { get; set; }
        public BankAccount? ToBankAccount { get; set; }
        
        public BankTransactionType TransactionType { get; set; }
        
        public decimal Amount { get; set; }
        
        [MaxLength(200)]
        public string Description { get; set; } = "";
        
        [MaxLength(100)]
        public string Reference { get; set; } = "";
        
        public TransactionStatus Status { get; set; } = TransactionStatus.Pending;
        
        [MaxLength(1000)]
        public string Notes { get; set; } = "";
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        // خصائص محسوبة
        public string TransactionTypeText => GetTransactionTypeText(TransactionType);
        public string StatusText => GetStatusText(Status);
        
        private static string GetTransactionTypeText(BankTransactionType type)
        {
            return type switch
            {
                BankTransactionType.Deposit => "إيداع",
                BankTransactionType.Withdrawal => "سحب",
                BankTransactionType.Transfer => "تحويل",
                BankTransactionType.Fee => "رسوم",
                BankTransactionType.Interest => "فوائد",
                _ => "غير محدد"
            };
        }
        
        private static string GetStatusText(TransactionStatus status)
        {
            return status switch
            {
                TransactionStatus.Pending => "معلق",
                TransactionStatus.Completed => "مكتمل",
                TransactionStatus.Cancelled => "ملغي",
                TransactionStatus.Failed => "فاشل",
                _ => "غير محدد"
            };
        }
    }
}
