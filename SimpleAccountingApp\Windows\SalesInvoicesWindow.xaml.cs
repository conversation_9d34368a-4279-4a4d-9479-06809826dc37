using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace SimpleAccountingApp.Windows
{
    public partial class SalesInvoicesWindow : Window
    {
        private ObservableCollection<SalesInvoice> salesInvoices;

        public SalesInvoicesWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            salesInvoices = new ObservableCollection<SalesInvoice>
            {
                new SalesInvoice
                {
                    InvoiceNumber = "S001",
                    InvoiceDate = DateTime.Now.AddDays(-5),
                    CustomerName = "شركة الأمل للتجارة",
                    SubTotal = 5000,
                    DiscountAmount = 250,
                    TaxAmount = 712.50m,
                    TotalAmount = 5462.50m,
                    PaidAmount = 5462.50m,
                    RemainingAmount = 0,
                    Status = "مدفوعة",
                    PaymentMethod = "نقداً",
                    Notes = "فاتورة مدفوعة بالكامل"
                },
                new SalesInvoice
                {
                    InvoiceNumber = "S002",
                    InvoiceDate = DateTime.Now.AddDays(-3),
                    CustomerName = "أحمد محمد العلي",
                    SubTotal = 3200,
                    DiscountAmount = 0,
                    TaxAmount = 480,
                    TotalAmount = 3680,
                    PaidAmount = 2000,
                    RemainingAmount = 1680,
                    Status = "مدفوعة جزئياً",
                    PaymentMethod = "آجل",
                    Notes = "دفعة أولى"
                },
                new SalesInvoice
                {
                    InvoiceNumber = "S003",
                    InvoiceDate = DateTime.Now.AddDays(-1),
                    CustomerName = "مؤسسة النور للمقاولات",
                    SubTotal = 8500,
                    DiscountAmount = 500,
                    TaxAmount = 1200,
                    TotalAmount = 9200,
                    PaidAmount = 0,
                    RemainingAmount = 9200,
                    Status = "غير مدفوعة",
                    PaymentMethod = "آجل",
                    Notes = "فاتورة جديدة"
                },
                new SalesInvoice
                {
                    InvoiceNumber = "S004",
                    InvoiceDate = DateTime.Now,
                    CustomerName = "فاطمة سالم القحطاني",
                    SubTotal = 1500,
                    DiscountAmount = 75,
                    TaxAmount = 213.75m,
                    TotalAmount = 1638.75m,
                    PaidAmount = 1638.75m,
                    RemainingAmount = 0,
                    Status = "مدفوعة",
                    PaymentMethod = "بطاقة ائتمان",
                    Notes = "دفع فوري"
                }
            };

            InvoicesDataGrid.ItemsSource = salesInvoices;
        }

        private void InvoicesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            bool hasSelection = InvoicesDataGrid.SelectedItem != null;
            EditInvoiceButton.IsEnabled = hasSelection;
            DeleteInvoiceButton.IsEnabled = hasSelection;
            PrintInvoiceButton.IsEnabled = hasSelection;
        }

        private void AddInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var addInvoiceWindow = new AddSalesInvoiceWindow();
            if (addInvoiceWindow.ShowDialog() == true)
            {
                MessageBox.Show("تم إنشاء الفاتورة بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: إعادة تحميل البيانات
            }
        }

        private void EditInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is SalesInvoice selectedInvoice)
            {
                var editInvoiceWindow = new AddSalesInvoiceWindow(selectedInvoice);
                if (editInvoiceWindow.ShowDialog() == true)
                {
                    MessageBox.Show("تم تعديل الفاتورة بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    // TODO: تحديث البيانات
                }
            }
        }

        private void DeleteInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is SalesInvoice selectedInvoice)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الفاتورة '{selectedInvoice.InvoiceNumber}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    salesInvoices.Remove(selectedInvoice);
                    MessageBox.Show("تم حذف الفاتورة بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void PrintInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is SalesInvoice selectedInvoice)
            {
                MessageBox.Show($"طباعة الفاتورة {selectedInvoice.InvoiceNumber} - قيد التطوير", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            string searchText = SearchTextBox.Text.ToLower();
            if (string.IsNullOrWhiteSpace(searchText))
            {
                InvoicesDataGrid.ItemsSource = salesInvoices;
            }
            else
            {
                var filteredInvoices = salesInvoices.Where(inv => 
                    inv.InvoiceNumber.ToLower().Contains(searchText) ||
                    inv.CustomerName.ToLower().Contains(searchText) ||
                    inv.Status.ToLower().Contains(searchText)
                ).ToList();
                
                InvoicesDataGrid.ItemsSource = filteredInvoices;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    // نموذج بيانات فاتورة المبيعات
    public class SalesInvoice
    {
        public string InvoiceNumber { get; set; } = "";
        public DateTime InvoiceDate { get; set; }
        public string CustomerName { get; set; } = "";
        public decimal SubTotal { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public string Status { get; set; } = "";
        public string PaymentMethod { get; set; } = "";
        public string Notes { get; set; } = "";
        
        public string StatusText => Status;
    }
}
