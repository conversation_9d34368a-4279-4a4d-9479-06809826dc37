<Window x:Class="SimpleAccountingApp.Windows.AddPayrollWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة راتب" Height="600" Width="500"
        WindowStartupLocation="CenterScreen" FlowDirection="RightToLeft">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Name="TitleTextBlock" Text="إضافة راتب جديد" 
                   FontSize="18" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- نموذج البيانات -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- رقم الراتب -->
                <Label Content="رقم الراتب:" FontWeight="Bold"/>
                <TextBox Name="PayrollNumberTextBox" Margin="0,0,0,10" IsReadOnly="True"/>

                <!-- تاريخ الراتب -->
                <Label Content="تاريخ الراتب:" FontWeight="Bold"/>
                <DatePicker Name="PayrollDatePicker" Margin="0,0,0,10"/>

                <!-- الشهر -->
                <Label Content="الشهر:" FontWeight="Bold"/>
                <ComboBox Name="MonthComboBox" Margin="0,0,0,10">
                    <ComboBoxItem Content="يناير" Tag="1"/>
                    <ComboBoxItem Content="فبراير" Tag="2"/>
                    <ComboBoxItem Content="مارس" Tag="3"/>
                    <ComboBoxItem Content="أبريل" Tag="4"/>
                    <ComboBoxItem Content="مايو" Tag="5"/>
                    <ComboBoxItem Content="يونيو" Tag="6"/>
                    <ComboBoxItem Content="يوليو" Tag="7"/>
                    <ComboBoxItem Content="أغسطس" Tag="8"/>
                    <ComboBoxItem Content="سبتمبر" Tag="9"/>
                    <ComboBoxItem Content="أكتوبر" Tag="10"/>
                    <ComboBoxItem Content="نوفمبر" Tag="11"/>
                    <ComboBoxItem Content="ديسمبر" Tag="12"/>
                </ComboBox>

                <!-- السنة -->
                <Label Content="السنة:" FontWeight="Bold"/>
                <TextBox Name="YearTextBox" Margin="0,0,0,10"/>

                <!-- الراتب الأساسي -->
                <Label Content="الراتب الأساسي:" FontWeight="Bold"/>
                <TextBox Name="BasicSalaryTextBox" Margin="0,0,0,10"/>

                <!-- بدل السكن -->
                <Label Content="بدل السكن:" FontWeight="Bold"/>
                <TextBox Name="HousingAllowanceTextBox" Margin="0,0,0,10"/>

                <!-- بدل المواصلات -->
                <Label Content="بدل المواصلات:" FontWeight="Bold"/>
                <TextBox Name="TransportationAllowanceTextBox" Margin="0,0,0,10"/>

                <!-- بدل الطعام -->
                <Label Content="بدل الطعام:" FontWeight="Bold"/>
                <TextBox Name="FoodAllowanceTextBox" Margin="0,0,0,10"/>

                <!-- بدلات أخرى -->
                <Label Content="بدلات أخرى:" FontWeight="Bold"/>
                <TextBox Name="OtherAllowancesTextBox" Margin="0,0,0,10"/>

                <!-- ساعات إضافية -->
                <Label Content="ساعات إضافية:" FontWeight="Bold"/>
                <TextBox Name="OvertimeHoursTextBox" Margin="0,0,0,10"/>

                <!-- معدل الساعة الإضافية -->
                <Label Content="معدل الساعة الإضافية:" FontWeight="Bold"/>
                <TextBox Name="OvertimeRateTextBox" Margin="0,0,0,10"/>

                <!-- التأمينات الاجتماعية -->
                <Label Content="التأمينات الاجتماعية:" FontWeight="Bold"/>
                <TextBox Name="SocialInsuranceTextBox" Margin="0,0,0,10"/>

                <!-- ضريبة الدخل -->
                <Label Content="ضريبة الدخل:" FontWeight="Bold"/>
                <TextBox Name="IncomeTaxTextBox" Margin="0,0,0,10"/>

                <!-- قرض -->
                <Label Content="قرض:" FontWeight="Bold"/>
                <TextBox Name="LoanTextBox" Margin="0,0,0,10"/>

                <!-- خصومات أخرى -->
                <Label Content="خصومات أخرى:" FontWeight="Bold"/>
                <TextBox Name="OtherDeductionsTextBox" Margin="0,0,0,10"/>

                <!-- الحالة -->
                <Label Content="الحالة:" FontWeight="Bold"/>
                <ComboBox Name="StatusComboBox" Margin="0,0,0,10">
                    <ComboBoxItem Content="مسودة" Tag="Draft" IsSelected="True"/>
                    <ComboBoxItem Content="معتمد" Tag="Approved"/>
                    <ComboBoxItem Content="مدفوع" Tag="Paid"/>
                    <ComboBoxItem Content="ملغي" Tag="Cancelled"/>
                </ComboBox>

                <!-- ملاحظات -->
                <Label Content="ملاحظات:" FontWeight="Bold"/>
                <TextBox Name="NotesTextBox" Height="60" 
                         TextWrapping="Wrap" AcceptsReturn="True" 
                         VerticalScrollBarVisibility="Auto" Margin="0,0,0,10"/>

                <!-- ملخص الراتب -->
                <Border BorderBrush="Gray" BorderThickness="1" Padding="10" Margin="0,10,0,10">
                    <StackPanel>
                        <TextBlock Text="ملخص الراتب:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBlock Name="SummaryTextBlock" FontSize="12"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Name="SaveButton" Content="حفظ" 
                    Width="100" Margin="0,0,10,0" Click="SaveButton_Click"/>
            <Button Name="CancelButton" Content="إلغاء" 
                    Width="100" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
