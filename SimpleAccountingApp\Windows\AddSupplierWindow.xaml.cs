using System.Windows;
using System.Windows.Controls;

namespace SimpleAccountingApp.Windows
{
    public partial class AddSupplierWindow : Window
    {
        private Supplier? _editingSupplier;
        private bool _isEditMode;

        public AddSupplierWindow()
        {
            InitializeComponent();
            _isEditMode = false;
            GenerateSupplierCode();
        }

        public AddSupplierWindow(Supplier supplierToEdit)
        {
            InitializeComponent();
            _editingSupplier = supplierToEdit;
            _isEditMode = true;
            
            WindowTitle.Text = "تعديل المورد";
            this.Title = "تعديل المورد - نظام المحاسبة المالية";
            
            LoadSupplierData();
        }

        private void GenerateSupplierCode()
        {
            // إنشاء رقم مورد تلقائي
            Random random = new Random();
            SupplierCodeTextBox.Text = "S" + (random.Next(100, 999)).ToString();
        }

        private void LoadSupplierData()
        {
            if (_editingSupplier != null)
            {
                SupplierCodeTextBox.Text = _editingSupplier.SupplierCode;
                SupplierNameTextBox.Text = _editingSupplier.SupplierName;
                
                // تحديد نوع المورد
                foreach (ComboBoxItem item in SupplierTypeComboBox.Items)
                {
                    if (item.Content.ToString() == _editingSupplier.SupplierType)
                    {
                        SupplierTypeComboBox.SelectedItem = item;
                        break;
                    }
                }
                
                PhoneTextBox.Text = _editingSupplier.Phone;
                MobileTextBox.Text = _editingSupplier.Mobile;
                EmailTextBox.Text = _editingSupplier.Email;
                AddressTextBox.Text = _editingSupplier.Address;
                CityTextBox.Text = _editingSupplier.City;
                CountryTextBox.Text = _editingSupplier.Country;
                TaxNumberTextBox.Text = _editingSupplier.TaxNumber;
                PaymentTermsTextBox.Text = _editingSupplier.PaymentTerms.ToString();
                NotesTextBox.Text = _editingSupplier.Notes;
                IsActiveCheckBox.IsChecked = _editingSupplier.IsActive;
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                if (_isEditMode)
                {
                    UpdateSupplier();
                }
                else
                {
                    CreateNewSupplier();
                }
                
                this.DialogResult = true;
                this.Close();
            }
        }

        private bool ValidateInput()
        {
            // التحقق من رقم المورد
            if (string.IsNullOrWhiteSpace(SupplierCodeTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم المورد", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                SupplierCodeTextBox.Focus();
                return false;
            }

            // التحقق من اسم المورد
            if (string.IsNullOrWhiteSpace(SupplierNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المورد", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                SupplierNameTextBox.Focus();
                return false;
            }

            // التحقق من نوع المورد
            if (SupplierTypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع المورد", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                SupplierTypeComboBox.Focus();
                return false;
            }

            // التحقق من مدة السداد
            if (!int.TryParse(PaymentTermsTextBox.Text, out _))
            {
                MessageBox.Show("يرجى إدخال مدة سداد صحيحة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                PaymentTermsTextBox.Focus();
                return false;
            }

            return true;
        }

        private void CreateNewSupplier()
        {
            var newSupplier = new Supplier
            {
                SupplierCode = SupplierCodeTextBox.Text.Trim(),
                SupplierName = SupplierNameTextBox.Text.Trim(),
                SupplierType = ((ComboBoxItem)SupplierTypeComboBox.SelectedItem).Content.ToString()!,
                Phone = PhoneTextBox.Text.Trim(),
                Mobile = MobileTextBox.Text.Trim(),
                Email = EmailTextBox.Text.Trim(),
                Address = AddressTextBox.Text.Trim(),
                City = CityTextBox.Text.Trim(),
                Country = CountryTextBox.Text.Trim(),
                TaxNumber = TaxNumberTextBox.Text.Trim(),
                PaymentTerms = int.Parse(PaymentTermsTextBox.Text),
                Notes = NotesTextBox.Text.Trim(),
                IsActive = IsActiveCheckBox.IsChecked ?? true,
                CurrentBalance = 0
            };

            // في التطبيق الحقيقي، سيتم حفظ المورد في قاعدة البيانات
            // TODO: حفظ في قاعدة البيانات
        }

        private void UpdateSupplier()
        {
            if (_editingSupplier != null)
            {
                _editingSupplier.SupplierCode = SupplierCodeTextBox.Text.Trim();
                _editingSupplier.SupplierName = SupplierNameTextBox.Text.Trim();
                _editingSupplier.SupplierType = ((ComboBoxItem)SupplierTypeComboBox.SelectedItem).Content.ToString()!;
                _editingSupplier.Phone = PhoneTextBox.Text.Trim();
                _editingSupplier.Mobile = MobileTextBox.Text.Trim();
                _editingSupplier.Email = EmailTextBox.Text.Trim();
                _editingSupplier.Address = AddressTextBox.Text.Trim();
                _editingSupplier.City = CityTextBox.Text.Trim();
                _editingSupplier.Country = CountryTextBox.Text.Trim();
                _editingSupplier.TaxNumber = TaxNumberTextBox.Text.Trim();
                _editingSupplier.PaymentTerms = int.Parse(PaymentTermsTextBox.Text);
                _editingSupplier.Notes = NotesTextBox.Text.Trim();
                _editingSupplier.IsActive = IsActiveCheckBox.IsChecked ?? true;

                // في التطبيق الحقيقي، سيتم تحديث المورد في قاعدة البيانات
                // TODO: تحديث في قاعدة البيانات
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            this.DialogResult = false;
            this.Close();
        }
    }
}
