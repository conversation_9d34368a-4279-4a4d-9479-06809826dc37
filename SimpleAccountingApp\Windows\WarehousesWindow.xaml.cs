using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace SimpleAccountingApp.Windows
{
    public partial class WarehousesWindow : Window
    {
        private ObservableCollection<Warehouse> warehouses;

        public WarehousesWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            warehouses = new ObservableCollection<Warehouse>
            {
                new Warehouse
                {
                    WarehouseCode = "WH001",
                    WarehouseName = "المخزن الرئيسي",
                    Location = "الرياض، حي العليا، شارع الملك فهد",
                    Manager = "أحمد محمد السالم",
                    Phone = "011-2345678",
                    Area = 1500,
                    ProductCount = 125,
                    TotalValue = 450000,
                    IsActive = true,
                    Description = "المخزن الرئيسي للشركة"
                },
                new Warehouse
                {
                    WarehouseCode = "WH002",
                    WarehouseName = "مخزن الفرع الشرقي",
                    Location = "الدمام، حي الفيصلية، طريق الملك عبدالعزيز",
                    Manager = "فاطمة علي القحطاني",
                    Phone = "013-3456789",
                    Area = 800,
                    ProductCount = 85,
                    TotalValue = 280000,
                    IsActive = true,
                    Description = "مخزن الفرع الشرقي"
                },
                new Warehouse
                {
                    WarehouseCode = "WH003",
                    WarehouseName = "مخزن الفرع الغربي",
                    Location = "جدة، حي الروضة، شارع التحلية",
                    Manager = "محمد عبدالله الغامدي",
                    Phone = "012-4567890",
                    Area = 1200,
                    ProductCount = 95,
                    TotalValue = 320000,
                    IsActive = true,
                    Description = "مخزن الفرع الغربي"
                },
                new Warehouse
                {
                    WarehouseCode = "WH004",
                    WarehouseName = "مخزن المواد الخام",
                    Location = "الرياض، المنطقة الصناعية الثانية",
                    Manager = "سالم أحمد المطيري",
                    Phone = "011-5678901",
                    Area = 2000,
                    ProductCount = 45,
                    TotalValue = 180000,
                    IsActive = false,
                    Description = "مخزن المواد الخام - متوقف مؤقتاً"
                }
            };

            WarehousesDataGrid.ItemsSource = warehouses;
        }

        private void WarehousesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            bool hasSelection = WarehousesDataGrid.SelectedItem != null;
            EditWarehouseButton.IsEnabled = hasSelection;
            DeleteWarehouseButton.IsEnabled = hasSelection;
        }

        private void AddWarehouseButton_Click(object sender, RoutedEventArgs e)
        {
            var addWarehouseWindow = new AddWarehouseWindow();
            if (addWarehouseWindow.ShowDialog() == true)
            {
                MessageBox.Show("تم إضافة المخزن بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: إعادة تحميل البيانات
            }
        }

        private void EditWarehouseButton_Click(object sender, RoutedEventArgs e)
        {
            if (WarehousesDataGrid.SelectedItem is Warehouse selectedWarehouse)
            {
                var editWarehouseWindow = new AddWarehouseWindow(selectedWarehouse);
                if (editWarehouseWindow.ShowDialog() == true)
                {
                    MessageBox.Show("تم تعديل المخزن بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    // TODO: تحديث البيانات
                }
            }
        }

        private void DeleteWarehouseButton_Click(object sender, RoutedEventArgs e)
        {
            if (WarehousesDataGrid.SelectedItem is Warehouse selectedWarehouse)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف المخزن '{selectedWarehouse.WarehouseName}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    warehouses.Remove(selectedWarehouse);
                    MessageBox.Show("تم حذف المخزن بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void StockMovementButton_Click(object sender, RoutedEventArgs e)
        {
            var stockMovementWindow = new StockMovementWindow();
            stockMovementWindow.ShowDialog();
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            string searchText = SearchTextBox.Text.ToLower();
            if (string.IsNullOrWhiteSpace(searchText))
            {
                WarehousesDataGrid.ItemsSource = warehouses;
            }
            else
            {
                var filteredWarehouses = warehouses.Where(w => 
                    w.WarehouseName.ToLower().Contains(searchText) ||
                    w.WarehouseCode.ToLower().Contains(searchText) ||
                    w.Location.ToLower().Contains(searchText) ||
                    w.Manager.ToLower().Contains(searchText)
                ).ToList();
                
                WarehousesDataGrid.ItemsSource = filteredWarehouses;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    // نموذج بيانات المخزن
    public class Warehouse
    {
        public string WarehouseCode { get; set; } = "";
        public string WarehouseName { get; set; } = "";
        public string Location { get; set; } = "";
        public string Manager { get; set; } = "";
        public string Phone { get; set; } = "";
        public decimal Area { get; set; }
        public int ProductCount { get; set; }
        public decimal TotalValue { get; set; }
        public bool IsActive { get; set; } = true;
        public string Description { get; set; } = "";
        
        public string IsActiveText => IsActive ? "نشط" : "غير نشط";
    }
}
