using System.ComponentModel.DataAnnotations;

namespace SimpleAccountingApp.Models
{
    public class User
    {
        [Key]
        public int UserId { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string Username { get; set; } = "";
        
        [Required]
        [MaxLength(200)]
        public string Password { get; set; } = "";
        
        [Required]
        [MaxLength(200)]
        public string FullName { get; set; } = "";
        
        [MaxLength(100)]
        public string Email { get; set; } = "";
        
        [Required]
        [MaxLength(50)]
        public string Role { get; set; } = "";
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public bool IsActive { get; set; } = true;
    }
}
