<Window x:Class="SimpleAccountingApp.Windows.StockMovementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="حركة المخزون - نظام المحاسبة المالية" 
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#607D8B" Padding="15">
            <DockPanel>
                <Button DockPanel.Dock="Left" 
                       Content="✖ إغلاق" 
                       Background="#455A64" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,5" 
                       Click="CloseButton_Click"/>
                
                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                    <TextBlock Text="📊" FontSize="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="حركة المخزون" 
                              FontSize="20" 
                              FontWeight="Bold" 
                              Foreground="White" 
                              VerticalAlignment="Center"/>
                </StackPanel>
            </DockPanel>
        </Border>
        
        <!-- Filters -->
        <Border Grid.Row="1" Background="#F5F5F5" Padding="15" BorderBrush="#DDD" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- Date From -->
                <TextBlock Grid.Column="0" Text="من تاريخ:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <DatePicker x:Name="FromDatePicker"
                           Grid.Column="1"
                           Height="35"/>

                <!-- Date To -->
                <TextBlock Grid.Column="2" Text="إلى تاريخ:" VerticalAlignment="Center" Margin="15,0,10,0"/>
                <DatePicker x:Name="ToDatePicker"
                           Grid.Column="3"
                           Height="35"/>
                
                <!-- Movement Type -->
                <TextBlock Grid.Column="4" Text="نوع الحركة:" VerticalAlignment="Center" Margin="15,0,10,0"/>
                <ComboBox x:Name="MovementTypeComboBox" 
                         Grid.Column="5" 
                         Height="35"
                         Padding="10">
                    <ComboBoxItem Content="الكل"/>
                    <ComboBoxItem Content="إدخال"/>
                    <ComboBoxItem Content="إخراج"/>
                    <ComboBoxItem Content="تحويل"/>
                    <ComboBoxItem Content="تسوية"/>
                </ComboBox>
                
                <!-- Warehouse -->
                <TextBlock Grid.Column="6" Text="المخزن:" VerticalAlignment="Center" Margin="15,0,10,0"/>
                <ComboBox x:Name="WarehouseComboBox" 
                         Grid.Column="7" 
                         Height="35"
                         Padding="10"
                         DisplayMemberPath="WarehouseName"
                         SelectedValuePath="WarehouseCode"/>
                
                <!-- Search Button -->
                <Button x:Name="SearchButton" 
                       Grid.Column="8" 
                       Content="🔍 بحث" 
                       Background="#2196F3" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="15,0,0,0"
                       Click="SearchButton_Click"/>
            </Grid>
        </Border>
        
        <!-- Stock Movement DataGrid -->
        <Grid Grid.Row="2" Margin="10">
            <DataGrid x:Name="StockMovementDataGrid"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     AlternatingRowBackground="#F9F9F9">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="التاريخ" 
                                       Binding="{Binding MovementDate, StringFormat=yyyy/MM/dd HH:mm}" 
                                       Width="130"/>
                    
                    <DataGridTextColumn Header="رقم المرجع" 
                                       Binding="{Binding ReferenceNumber}" 
                                       Width="120"/>
                    
                    <DataGridTextColumn Header="نوع الحركة" 
                                       Binding="{Binding MovementType}" 
                                       Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding MovementType}" Value="إدخال">
                                        <Setter Property="Foreground" Value="Green"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding MovementType}" Value="إخراج">
                                        <Setter Property="Foreground" Value="Red"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding MovementType}" Value="تحويل">
                                        <Setter Property="Foreground" Value="Orange"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="المخزن" 
                                       Binding="{Binding WarehouseName}" 
                                       Width="150"/>
                    
                    <DataGridTextColumn Header="رقم الصنف" 
                                       Binding="{Binding ProductCode}" 
                                       Width="100"/>
                    
                    <DataGridTextColumn Header="اسم الصنف" 
                                       Binding="{Binding ProductName}" 
                                       Width="200"/>
                    
                    <DataGridTextColumn Header="الكمية" 
                                       Binding="{Binding Quantity, StringFormat=N2}" 
                                       Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="سعر الوحدة" 
                                       Binding="{Binding UnitPrice, StringFormat=N2}" 
                                       Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="القيمة الإجمالية" 
                                       Binding="{Binding TotalValue, StringFormat=N2}" 
                                       Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="المستخدم" 
                                       Binding="{Binding UserName}" 
                                       Width="120"/>
                    
                    <DataGridTextColumn Header="ملاحظات" 
                                       Binding="{Binding Notes}" 
                                       Width="200"/>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>
    </Grid>
</Window>
