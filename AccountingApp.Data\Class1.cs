using Microsoft.EntityFrameworkCore;
using AccountingApp.Models;

namespace AccountingApp.Data
{
    /// <summary>
    /// سياق قاعدة البيانات - Database Context
    /// </summary>
    public class AccountingDbContext : DbContext
    {
        public AccountingDbContext(DbContextOptions<AccountingDbContext> options) : base(options)
        {
        }

        // DbSets - مجموعات البيانات
        public DbSet<User> Users { get; set; }
        public DbSet<Company> Companies { get; set; }
        public DbSet<Account> Accounts { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<ProductCategory> ProductCategories { get; set; }
        public DbSet<Unit> Units { get; set; }
        public DbSet<Warehouse> Warehouses { get; set; }
        public DbSet<ProductStock> ProductStocks { get; set; }
        public DbSet<SalesInvoice> SalesInvoices { get; set; }
        public DbSet<SalesInvoiceDetail> SalesInvoiceDetails { get; set; }
        public DbSet<PurchaseInvoice> PurchaseInvoices { get; set; }
        public DbSet<PurchaseInvoiceDetail> PurchaseInvoiceDetails { get; set; }
        public DbSet<SalesReturn> SalesReturns { get; set; }
        public DbSet<SalesReturnDetail> SalesReturnDetails { get; set; }
        public DbSet<PurchaseReturn> PurchaseReturns { get; set; }
        public DbSet<PurchaseReturnDetail> PurchaseReturnDetails { get; set; }
        public DbSet<JournalEntry> JournalEntries { get; set; }
        public DbSet<JournalEntryDetail> JournalEntryDetails { get; set; }
        public DbSet<ReceiptVoucher> ReceiptVouchers { get; set; }
        public DbSet<PaymentVoucher> PaymentVouchers { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تكوين العلاقات والقيود
            ConfigureUserEntity(modelBuilder);
            ConfigureCompanyEntity(modelBuilder);
            ConfigureAccountEntity(modelBuilder);
            ConfigureProductEntity(modelBuilder);
            ConfigureInvoiceEntities(modelBuilder);
            ConfigureJournalEntryEntity(modelBuilder);

            // إضافة البيانات الأولية
            SeedData(modelBuilder);
        }

        private void ConfigureUserEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasIndex(e => e.Username).IsUnique();
                entity.Property(e => e.Username).IsRequired();
                entity.Property(e => e.FullName).IsRequired();
                entity.Property(e => e.PasswordHash).IsRequired();
            });
        }

        private void ConfigureCompanyEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Company>(entity =>
            {
                entity.Property(e => e.CompanyName).IsRequired();
                entity.Property(e => e.TaxRate).HasColumnType("decimal(5,2)");
            });
        }

        private void ConfigureAccountEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Account>(entity =>
            {
                entity.HasIndex(e => e.AccountCode).IsUnique();
                entity.Property(e => e.AccountCode).IsRequired();
                entity.Property(e => e.AccountName).IsRequired();

                entity.HasOne(e => e.ParentAccount)
                    .WithMany(e => e.SubAccounts)
                    .HasForeignKey(e => e.ParentAccountId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }

        private void ConfigureProductEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Product>(entity =>
            {
                entity.HasIndex(e => e.ProductCode).IsUnique();
                entity.Property(e => e.ProductCode).IsRequired();
                entity.Property(e => e.ProductName).IsRequired();
            });

            modelBuilder.Entity<ProductStock>(entity =>
            {
                entity.HasIndex(e => new { e.ProductId, e.WarehouseId }).IsUnique();
            });
        }

        private void ConfigureInvoiceEntities(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<SalesInvoice>(entity =>
            {
                entity.HasIndex(e => e.InvoiceNumber).IsUnique();
                entity.Property(e => e.InvoiceNumber).IsRequired();
            });

            modelBuilder.Entity<PurchaseInvoice>(entity =>
            {
                entity.HasIndex(e => e.InvoiceNumber).IsUnique();
                entity.Property(e => e.InvoiceNumber).IsRequired();
            });
        }

        private void ConfigureJournalEntryEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<JournalEntry>(entity =>
            {
                entity.HasIndex(e => e.EntryNumber).IsUnique();
                entity.Property(e => e.EntryNumber).IsRequired();
                entity.Property(e => e.Description).IsRequired();
            });
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // إضافة مستخدم افتراضي (Admin)
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    UserId = 1,
                    Username = "admin",
                    FullName = "مدير النظام",
                    PasswordHash = "$2a$11$8K1p/a0dL2LkqvMA87LzO.Mh5J6cd4q5X5J5J5J5J5J5J5J5J5J5J5", // admin123
                    Email = "<EMAIL>",
                    Role = UserRole.Admin,
                    IsActive = true,
                    CreatedDate = new DateTime(2025, 1, 1),
                    CreatedBy = "System"
                }
            );

            // إضافة شركة افتراضية
            modelBuilder.Entity<Company>().HasData(
                new Company
                {
                    CompanyId = 1,
                    CompanyName = "شركة المحاسبة المتقدمة",
                    CompanyNameEnglish = "Advanced Accounting Company",
                    Address = "الرياض، المملكة العربية السعودية",
                    Phone = "011-1234567",
                    Email = "<EMAIL>",
                    Currency = "ريال",
                    TaxRate = 15.0m,
                    CreatedDate = new DateTime(2025, 1, 1),
                    FinancialYearStart = new DateTime(2025, 1, 1),
                    FinancialYearEnd = new DateTime(2025, 12, 31)
                }
            );
        }
    }
}
