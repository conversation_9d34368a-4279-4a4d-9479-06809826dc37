<Window x:Class="SimpleAccountingApp.Windows.CreateUserWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إنشاء مستخدم جديد - نظام المحاسبة المالية" 
        Height="600" Width="500"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#4CAF50" Padding="15">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                <TextBlock Text="👤" FontSize="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock Text="إنشاء مستخدم جديد" 
                          FontSize="18" 
                          FontWeight="Bold" 
                          Foreground="White" 
                          VerticalAlignment="Center"/>
            </StackPanel>
        </Border>
        
        <!-- Form Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="30">
            <StackPanel>
                <!-- Full Name -->
                <TextBlock Text="الاسم الكامل *" FontWeight="Bold" Margin="0,10,0,5"/>
                <TextBox x:Name="FullNameTextBox" 
                        Height="35" 
                        Padding="10"
                        FontSize="14"
                        BorderBrush="#DDD"
                        BorderThickness="2"/>
                
                <!-- Username -->
                <TextBlock Text="اسم المستخدم *" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBox x:Name="UsernameTextBox" 
                        Height="35" 
                        Padding="10"
                        FontSize="14"
                        BorderBrush="#DDD"
                        BorderThickness="2"/>
                
                <!-- Email -->
                <TextBlock Text="البريد الإلكتروني" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBox x:Name="EmailTextBox" 
                        Height="35" 
                        Padding="10"
                        FontSize="14"
                        BorderBrush="#DDD"
                        BorderThickness="2"/>
                
                <!-- Password -->
                <TextBlock Text="كلمة المرور *" FontWeight="Bold" Margin="0,15,0,5"/>
                <PasswordBox x:Name="PasswordBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"
                            BorderBrush="#DDD"
                            BorderThickness="2"/>
                
                <!-- Confirm Password -->
                <TextBlock Text="تأكيد كلمة المرور *" FontWeight="Bold" Margin="0,15,0,5"/>
                <PasswordBox x:Name="ConfirmPasswordBox" 
                            Height="35" 
                            Padding="10"
                            FontSize="14"
                            BorderBrush="#DDD"
                            BorderThickness="2"/>
                
                <!-- User Role -->
                <TextBlock Text="دور المستخدم *" FontWeight="Bold" Margin="0,15,0,5"/>
                <ComboBox x:Name="UserRoleComboBox" 
                         Height="35" 
                         Padding="10"
                         FontSize="14"
                         BorderBrush="#DDD"
                         BorderThickness="2">
                    <ComboBoxItem Content="مدير النظام"/>
                    <ComboBoxItem Content="محاسب"/>
                    <ComboBoxItem Content="مستخدم عادي"/>
                    <ComboBoxItem Content="مراجع"/>
                </ComboBox>
                
                <!-- Department -->
                <TextBlock Text="القسم" FontWeight="Bold" Margin="0,15,0,5"/>
                <ComboBox x:Name="DepartmentComboBox" 
                         Height="35" 
                         Padding="10"
                         FontSize="14"
                         BorderBrush="#DDD"
                         BorderThickness="2">
                    <ComboBoxItem Content="المحاسبة"/>
                    <ComboBoxItem Content="المبيعات"/>
                    <ComboBoxItem Content="المشتريات"/>
                    <ComboBoxItem Content="المخازن"/>
                    <ComboBoxItem Content="الإدارة"/>
                </ComboBox>
                
                <!-- Phone -->
                <TextBlock Text="رقم الهاتف" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBox x:Name="PhoneTextBox" 
                        Height="35" 
                        Padding="10"
                        FontSize="14"
                        BorderBrush="#DDD"
                        BorderThickness="2"/>
                
                <!-- Permissions -->
                <TextBlock Text="الصلاحيات" FontWeight="Bold" Margin="0,20,0,10"/>
                <Border BorderBrush="#DDD" BorderThickness="1" Padding="15" CornerRadius="5">
                    <StackPanel>
                        <CheckBox x:Name="CanViewReportsCheckBox" Content="عرض التقارير" Margin="0,5"/>
                        <CheckBox x:Name="CanCreateInvoicesCheckBox" Content="إنشاء الفواتير" Margin="0,5"/>
                        <CheckBox x:Name="CanManageUsersCheckBox" Content="إدارة المستخدمين" Margin="0,5"/>
                        <CheckBox x:Name="CanManageProductsCheckBox" Content="إدارة الأصناف" Margin="0,5"/>
                        <CheckBox x:Name="CanAccessTaxSystemCheckBox" Content="الوصول لنظام الضرائب" Margin="0,5"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
        
        <!-- Buttons -->
        <Border Grid.Row="2" Background="#F5F5F5" Padding="15" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="CreateButton" 
                       Content="✅ إنشاء الحساب" 
                       Background="#4CAF50" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="20,10" 
                       Margin="10,0"
                       FontSize="14"
                       FontWeight="Bold"
                       Click="CreateButton_Click"/>
                
                <Button x:Name="CancelButton" 
                       Content="❌ إلغاء" 
                       Background="#F44336" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="20,10" 
                       Margin="10,0"
                       FontSize="14"
                       FontWeight="Bold"
                       Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
