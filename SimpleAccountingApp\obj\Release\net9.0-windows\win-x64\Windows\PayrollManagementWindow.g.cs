﻿#pragma checksum "..\..\..\..\..\Windows\PayrollManagementWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F969A825CA4D1BD9259ED6FFCFC5FC0565D5C392"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleAccountingApp.Windows {
    
    
    /// <summary>
    /// PayrollManagementWindow
    /// </summary>
    public partial class PayrollManagementWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 25 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox MonthFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox YearFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DepartmentFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FilterButton;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearFilterButton;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GeneratePayrollButton;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddPayrollButton;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditPayrollButton;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeletePayrollButton;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintPayrollButton;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportPayrollButton;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PayrollsDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleAccountingApp;component/windows/payrollmanagementwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.MonthFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 2:
            this.YearFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 3:
            this.DepartmentFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.FilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 47 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
            this.FilterButton.Click += new System.Windows.RoutedEventHandler(this.FilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ClearFilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 48 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
            this.ClearFilterButton.Click += new System.Windows.RoutedEventHandler(this.ClearFilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.GeneratePayrollButton = ((System.Windows.Controls.Button)(target));
            
            #line 56 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
            this.GeneratePayrollButton.Click += new System.Windows.RoutedEventHandler(this.GeneratePayrollButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.AddPayrollButton = ((System.Windows.Controls.Button)(target));
            
            #line 58 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
            this.AddPayrollButton.Click += new System.Windows.RoutedEventHandler(this.AddPayrollButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.EditPayrollButton = ((System.Windows.Controls.Button)(target));
            
            #line 60 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
            this.EditPayrollButton.Click += new System.Windows.RoutedEventHandler(this.EditPayrollButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.DeletePayrollButton = ((System.Windows.Controls.Button)(target));
            
            #line 62 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
            this.DeletePayrollButton.Click += new System.Windows.RoutedEventHandler(this.DeletePayrollButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.PrintPayrollButton = ((System.Windows.Controls.Button)(target));
            
            #line 64 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
            this.PrintPayrollButton.Click += new System.Windows.RoutedEventHandler(this.PrintPayrollButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ExportPayrollButton = ((System.Windows.Controls.Button)(target));
            
            #line 66 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
            this.ExportPayrollButton.Click += new System.Windows.RoutedEventHandler(this.ExportPayrollButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 68 "..\..\..\..\..\Windows\PayrollManagementWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.PayrollsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

