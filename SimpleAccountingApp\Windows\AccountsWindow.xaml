<Window x:Class="SimpleAccountingApp.Windows.AccountsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="دليل الحسابات - نظام المحاسبة المالية" 
        Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="50"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" Padding="15">
            <DockPanel>
                <Button DockPanel.Dock="Left" 
                       Content="✖ إغلاق" 
                       Background="#1976D2" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,5" 
                       Click="CloseButton_Click"/>
                
                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                    <TextBlock Text="📊" FontSize="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="دليل الحسابات" 
                              FontSize="20" 
                              FontWeight="Bold" 
                              Foreground="White" 
                              VerticalAlignment="Center"/>
                </StackPanel>
            </DockPanel>
        </Border>
        
        <!-- Toolbar -->
        <Border Grid.Row="1" Background="#F5F5F5" Padding="10" BorderBrush="#DDD" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <Button x:Name="AddAccountButton" 
                       Content="➕ إضافة حساب جديد" 
                       Background="#4CAF50" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="5,0"
                       Click="AddAccountButton_Click"/>
                
                <Button x:Name="EditAccountButton" 
                       Content="✏️ تعديل" 
                       Background="#FF9800" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="5,0"
                       IsEnabled="False"
                       Click="EditAccountButton_Click"/>
                
                <Button x:Name="DeleteAccountButton" 
                       Content="🗑️ حذف" 
                       Background="#F44336" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="5,0"
                       IsEnabled="False"
                       Click="DeleteAccountButton_Click"/>
                
                <Separator Margin="10,0"/>
                
                <TextBlock Text="البحث:" VerticalAlignment="Center" Margin="10,0,5,0"/>
                <TextBox x:Name="SearchTextBox" 
                        Width="200" 
                        Height="30"
                        Padding="5"
                        TextChanged="SearchTextBox_TextChanged"/>
            </StackPanel>
        </Border>
        
        <!-- Accounts TreeView -->
        <Border Grid.Row="2" Background="White" Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="300"/>
                </Grid.ColumnDefinitions>
                
                <!-- TreeView -->
                <TreeView x:Name="AccountsTreeView" 
                         Grid.Column="0"
                         Margin="0,0,10,0"
                         SelectedItemChanged="AccountsTreeView_SelectedItemChanged">
                    <TreeView.ItemTemplate>
                        <HierarchicalDataTemplate ItemsSource="{Binding SubAccounts}">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="{Binding AccountCode}" FontWeight="Bold" Margin="0,0,10,0"/>
                                <TextBlock Text="{Binding AccountName}"/>
                                <TextBlock Text="{Binding AccountType, StringFormat=' ({0})'}" 
                                          Foreground="#666" 
                                          FontStyle="Italic"/>
                            </StackPanel>
                        </HierarchicalDataTemplate>
                    </TreeView.ItemTemplate>
                </TreeView>
                
                <!-- Account Details Panel -->
                <Border Grid.Column="1" 
                       Background="#F9F9F9" 
                       Padding="15" 
                       BorderBrush="#DDD" 
                       BorderThickness="1">
                    <StackPanel x:Name="AccountDetailsPanel">
                        <TextBlock Text="تفاصيل الحساب" 
                                  FontSize="16" 
                                  FontWeight="Bold" 
                                  Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Text="رقم الحساب:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock x:Name="AccountCodeText" Grid.Row="0" Text="-" HorizontalAlignment="Left" Margin="100,5,0,5"/>
                            
                            <TextBlock Grid.Row="1" Text="اسم الحساب:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock x:Name="AccountNameText" Grid.Row="1" Text="-" HorizontalAlignment="Left" Margin="100,5,0,5"/>
                            
                            <TextBlock Grid.Row="2" Text="نوع الحساب:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock x:Name="AccountTypeText" Grid.Row="2" Text="-" HorizontalAlignment="Left" Margin="100,5,0,5"/>
                            
                            <TextBlock Grid.Row="3" Text="الحساب الأب:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock x:Name="ParentAccountText" Grid.Row="3" Text="-" HorizontalAlignment="Left" Margin="100,5,0,5"/>
                            
                            <TextBlock Grid.Row="4" Text="الرصيد الافتتاحي:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock x:Name="OpeningBalanceText" Grid.Row="4" Text="-" HorizontalAlignment="Left" Margin="100,5,0,5"/>
                            
                            <TextBlock Grid.Row="5" Text="الرصيد الحالي:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock x:Name="CurrentBalanceText" Grid.Row="5" Text="-" HorizontalAlignment="Left" Margin="100,5,0,5"/>
                            
                            <TextBlock Grid.Row="6" Text="الحالة:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock x:Name="IsActiveText" Grid.Row="6" Text="-" HorizontalAlignment="Left" Margin="100,5,0,5"/>
                            
                            <TextBlock Grid.Row="7" Text="الوصف:" FontWeight="Bold" Margin="0,10,0,5"/>
                            <TextBlock x:Name="DescriptionText" Grid.Row="7" Text="-" TextWrapping="Wrap" Margin="0,5"/>
                        </Grid>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
    </Grid>
</Window>
