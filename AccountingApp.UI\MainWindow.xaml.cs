﻿using System.Windows;
using System.Windows.Threading;
using AccountingApp.Services;
using AccountingApp.UI.Views;
using Microsoft.Extensions.DependencyInjection;

namespace AccountingApp.UI;

/// <summary>
/// الواجهة الرئيسية - Main Window
/// </summary>
public partial class MainWindow : Window
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IAuthenticationService _authService;
    private readonly DispatcherTimer _timer;

    public MainWindow(IServiceProvider serviceProvider)
    {
        InitializeComponent();

        _serviceProvider = serviceProvider;
        _authService = _serviceProvider.GetRequiredService<IAuthenticationService>();

        // إعداد المؤقت لعرض التاريخ والوقت
        _timer = new DispatcherTimer();
        _timer.Interval = TimeSpan.FromSeconds(1);
        _timer.Tick += Timer_Tick;
        _timer.Start();

        // تحديث معلومات المستخدم
        UpdateUserInfo();

        // إعداد الحالة الأولية
        StatusTextBlock.Text = "جاهز";
    }

    private async void UpdateUserInfo()
    {
        var currentUser = await _authService.GetCurrentUserAsync();
        if (currentUser != null)
        {
            UserNameTextBlock.Text = $"مرحباً، {currentUser.FullName}";
        }
    }

    private void Timer_Tick(object? sender, EventArgs e)
    {
        DateTimeTextBlock.Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm:ss");
    }

    private void DashboardButton_Click(object sender, RoutedEventArgs e)
    {
        // عرض لوحة التحكم
        WelcomeGrid.Visibility = Visibility.Visible;
        MainFrame.Content = null;
        StatusTextBlock.Text = "لوحة التحكم";
    }

    private void ChartOfAccountsButton_Click(object sender, RoutedEventArgs e)
    {
        // عرض دليل الحسابات
        WelcomeGrid.Visibility = Visibility.Collapsed;
        // TODO: إنشاء صفحة دليل الحسابات
        StatusTextBlock.Text = "دليل الحسابات";
    }

    private async void LogoutButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show(
            "هل أنت متأكد من تسجيل الخروج؟",
            "تأكيد تسجيل الخروج",
            MessageBoxButton.YesNo,
            MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            _authService.Logout();

            var loginWindow = new LoginWindow();
            loginWindow.Show();

            this.Close();
        }
    }

    private void SettingsButton_Click(object sender, RoutedEventArgs e)
    {
        // عرض شاشة الإعدادات
        StatusTextBlock.Text = "الإعدادات";
        MessageBox.Show("شاشة الإعدادات قيد التطوير", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    protected override void OnClosed(EventArgs e)
    {
        _timer?.Stop();
        base.OnClosed(e);
    }
}