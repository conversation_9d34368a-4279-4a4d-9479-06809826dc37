<Window x:Class="SimpleAccountingApp.Windows.ProductsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الأصناف - نظام المحاسبة المالية" 
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="50"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#9C27B0" Padding="15">
            <DockPanel>
                <Button DockPanel.Dock="Left" 
                       Content="✖ إغلاق" 
                       Background="#7B1FA2" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,5" 
                       Click="CloseButton_Click"/>
                
                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                    <TextBlock Text="📦" FontSize="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="إدارة الأصناف" 
                              FontSize="20" 
                              FontWeight="Bold" 
                              Foreground="White" 
                              VerticalAlignment="Center"/>
                </StackPanel>
            </DockPanel>
        </Border>
        
        <!-- Toolbar -->
        <Border Grid.Row="1" Background="#F5F5F5" Padding="10" BorderBrush="#DDD" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <Button x:Name="AddProductButton" 
                       Content="➕ إضافة صنف جديد" 
                       Background="#4CAF50" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="5,0"
                       Click="AddProductButton_Click"/>
                
                <Button x:Name="EditProductButton" 
                       Content="✏️ تعديل" 
                       Background="#FF9800" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="5,0"
                       IsEnabled="False"
                       Click="EditProductButton_Click"/>
                
                <Button x:Name="DeleteProductButton" 
                       Content="🗑️ حذف" 
                       Background="#F44336" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="5,0"
                       IsEnabled="False"
                       Click="DeleteProductButton_Click"/>
                
                <Separator Margin="10,0"/>
                
                <Button x:Name="StockReportButton" 
                       Content="📊 تقرير المخزون" 
                       Background="#2196F3" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="5,0"
                       Click="StockReportButton_Click"/>
                
                <Separator Margin="10,0"/>
                
                <TextBlock Text="البحث:" VerticalAlignment="Center" Margin="10,0,5,0"/>
                <TextBox x:Name="SearchTextBox" 
                        Width="200" 
                        Height="30"
                        Padding="5"
                        TextChanged="SearchTextBox_TextChanged"/>
            </StackPanel>
        </Border>
        
        <!-- Products DataGrid -->
        <Grid Grid.Row="2" Margin="10">
            <DataGrid x:Name="ProductsDataGrid"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     AlternatingRowBackground="#F9F9F9"
                     SelectionChanged="ProductsDataGrid_SelectionChanged">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم الصنف" 
                                       Binding="{Binding ProductCode}" 
                                       Width="100"/>
                    
                    <DataGridTextColumn Header="اسم الصنف" 
                                       Binding="{Binding ProductName}" 
                                       Width="200"/>
                    
                    <DataGridTextColumn Header="الباركود" 
                                       Binding="{Binding Barcode}" 
                                       Width="120"/>
                    
                    <DataGridTextColumn Header="الفئة" 
                                       Binding="{Binding Category}" 
                                       Width="120"/>
                    
                    <DataGridTextColumn Header="الوحدة" 
                                       Binding="{Binding Unit}" 
                                       Width="80"/>
                    
                    <DataGridTextColumn Header="سعر الشراء" 
                                       Binding="{Binding PurchasePrice, StringFormat=N2}" 
                                       Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="سعر البيع" 
                                       Binding="{Binding SalePrice, StringFormat=N2}" 
                                       Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="الكمية المتاحة"
                                       Binding="{Binding CurrentStock, StringFormat=N2}"
                                       Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="الحد الأدنى" 
                                       Binding="{Binding MinimumStock, StringFormat=N2}" 
                                       Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="الحالة" 
                                       Binding="{Binding IsActiveText}" 
                                       Width="80">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsActive}" Value="True">
                                        <Setter Property="Foreground" Value="Green"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding IsActive}" Value="False">
                                        <Setter Property="Foreground" Value="Red"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>
    </Grid>
</Window>
