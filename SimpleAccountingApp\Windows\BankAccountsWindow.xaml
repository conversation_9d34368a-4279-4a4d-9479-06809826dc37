<Window x:Class="SimpleAccountingApp.Windows.BankAccountsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة حسابات البنوك" Height="600" Width="1000"
        WindowStartupLocation="CenterScreen" FlowDirection="RightToLeft">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="إدارة حسابات البنوك" 
                   FontSize="24" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="0,0,0,20">
            <Button Name="AddBankAccountButton" Content="إضافة حساب بنكي" 
                    Width="150" Click="AddBankAccountButton_Click"/>
            <Button Name="EditBankAccountButton" Content="تعديل" 
                    Width="100" Margin="10,0,0,0" Click="EditBankAccountButton_Click"/>
            <Button Name="DeleteBankAccountButton" Content="حذف" 
                    Width="100" Margin="10,0,0,0" Click="DeleteBankAccountButton_Click"/>
            <Button Name="ViewTransactionsButton" Content="عرض العمليات" 
                    Width="120" Margin="10,0,0,0" Click="ViewTransactionsButton_Click"/>
            <Button Name="RefreshButton" Content="تحديث" 
                    Width="100" Margin="10,0,0,0" Click="RefreshButton_Click"/>
        </StackPanel>

        <!-- جدول البيانات -->
        <DataGrid Grid.Row="2" Name="BankAccountsDataGrid" 
                  AutoGenerateColumns="False" 
                  CanUserAddRows="False" 
                  CanUserDeleteRows="False"
                  IsReadOnly="True"
                  GridLinesVisibility="All"
                  HeadersVisibility="Column"
                  SelectionMode="Single">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="رقم الحساب" Binding="{Binding AccountNumber}" Width="120"/>
                <DataGridTextColumn Header="اسم الحساب" Binding="{Binding AccountName}" Width="200"/>
                <DataGridTextColumn Header="البنك" Binding="{Binding BankName}" Width="150"/>
                <DataGridTextColumn Header="الفرع" Binding="{Binding BranchName}" Width="120"/>
                <DataGridTextColumn Header="نوع الحساب" Binding="{Binding AccountTypeText}" Width="100"/>
                <DataGridTextColumn Header="الرصيد" Binding="{Binding Balance, StringFormat='{}{0:N2} ريال'}" Width="120"/>
                <DataGridTextColumn Header="حد الائتمان" Binding="{Binding CreditLimit, StringFormat='{}{0:N2} ريال'}" Width="120"/>
                <DataGridTextColumn Header="الرصيد المتاح" Binding="{Binding AvailableBalance, StringFormat='{}{0:N2} ريال'}" Width="130"/>
                <DataGridTextColumn Header="العملة" Binding="{Binding Currency}" Width="80"/>
                <DataGridTextColumn Header="الحالة" Binding="{Binding IsActiveText}" Width="80"/>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</Window>
