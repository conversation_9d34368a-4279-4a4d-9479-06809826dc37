<Window x:Class="SimpleAccountingApp.Windows.AddBankAccountWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة حساب بنكي" Height="600" Width="500"
        WindowStartupLocation="CenterScreen" FlowDirection="RightToLeft">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Name="TitleTextBlock" Text="إضافة حساب بنكي جديد" 
                   FontSize="18" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- نموذج البيانات -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- رقم الحساب -->
                <Label Content="رقم الحساب:" FontWeight="Bold"/>
                <TextBox Name="AccountNumberTextBox" Margin="0,0,0,10"/>

                <!-- اسم الحساب -->
                <Label Content="اسم الحساب:" FontWeight="Bold"/>
                <TextBox Name="AccountNameTextBox" Margin="0,0,0,10"/>

                <!-- اسم البنك -->
                <Label Content="اسم البنك:" FontWeight="Bold"/>
                <TextBox Name="BankNameTextBox" Margin="0,0,0,10"/>

                <!-- اسم الفرع -->
                <Label Content="اسم الفرع:" FontWeight="Bold"/>
                <TextBox Name="BranchNameTextBox" Margin="0,0,0,10"/>

                <!-- رقم الآيبان -->
                <Label Content="رقم الآيبان (IBAN):" FontWeight="Bold"/>
                <TextBox Name="IBANTextBox" Margin="0,0,0,10"/>

                <!-- رمز السويفت -->
                <Label Content="رمز السويفت (SWIFT):" FontWeight="Bold"/>
                <TextBox Name="SwiftCodeTextBox" Margin="0,0,0,10"/>

                <!-- نوع الحساب -->
                <Label Content="نوع الحساب:" FontWeight="Bold"/>
                <ComboBox Name="AccountTypeComboBox" Margin="0,0,0,10">
                    <ComboBoxItem Content="جاري" Tag="Current"/>
                    <ComboBoxItem Content="توفير" Tag="Savings"/>
                    <ComboBoxItem Content="وديعة ثابتة" Tag="FixedDeposit"/>
                    <ComboBoxItem Content="استثماري" Tag="Investment"/>
                </ComboBox>

                <!-- الرصيد الابتدائي -->
                <Label Content="الرصيد الابتدائي:" FontWeight="Bold"/>
                <TextBox Name="BalanceTextBox" Margin="0,0,0,10"/>

                <!-- حد الائتمان -->
                <Label Content="حد الائتمان:" FontWeight="Bold"/>
                <TextBox Name="CreditLimitTextBox" Margin="0,0,0,10"/>

                <!-- العملة -->
                <Label Content="العملة:" FontWeight="Bold"/>
                <ComboBox Name="CurrencyComboBox" Margin="0,0,0,10">
                    <ComboBoxItem Content="ريال سعودي (SAR)" Tag="SAR" IsSelected="True"/>
                    <ComboBoxItem Content="دولار أمريكي (USD)" Tag="USD"/>
                    <ComboBoxItem Content="يورو (EUR)" Tag="EUR"/>
                </ComboBox>

                <!-- نشط -->
                <CheckBox Name="IsActiveCheckBox" Content="نشط" 
                          IsChecked="True" Margin="0,10,0,10"/>

                <!-- ملاحظات -->
                <Label Content="ملاحظات:" FontWeight="Bold"/>
                <TextBox Name="NotesTextBox" Height="80" 
                         TextWrapping="Wrap" AcceptsReturn="True" 
                         VerticalScrollBarVisibility="Auto" Margin="0,0,0,10"/>
            </StackPanel>
        </ScrollViewer>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Name="SaveButton" Content="حفظ" 
                    Width="100" Margin="0,0,10,0" Click="SaveButton_Click"/>
            <Button Name="CancelButton" Content="إلغاء" 
                    Width="100" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
