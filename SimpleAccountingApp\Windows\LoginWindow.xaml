<Window x:Class="SimpleAccountingApp.Windows.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تسجيل الدخول - نظام المحاسبة المالية" 
        Height="500" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14"
        Background="White">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="120"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#2196F3" Offset="0"/>
                    <GradientStop Color="#1976D2" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <TextBlock Text="🔐" FontSize="36" HorizontalAlignment="Center" Foreground="White"/>
                <TextBlock Text="نظام المحاسبة المالية" 
                          FontSize="18" 
                          FontWeight="Bold" 
                          Foreground="White" 
                          HorizontalAlignment="Center"
                          Margin="0,5,0,0"/>
                <TextBlock Text="تسجيل الدخول" 
                          FontSize="14" 
                          Foreground="White" 
                          HorizontalAlignment="Center"
                          Margin="0,5,0,0"/>
            </StackPanel>
        </Border>
        
        <!-- Login Form -->
        <StackPanel Grid.Row="1" Margin="40,30" VerticalAlignment="Center">
            <!-- Username -->
            <TextBlock Text="اسم المستخدم" FontWeight="Bold" Margin="0,0,0,8" FontSize="15"/>
            <Border BorderBrush="#DDD" BorderThickness="2" CornerRadius="5" Margin="0,0,0,20">
                <TextBox x:Name="UsernameTextBox" 
                        Height="40" 
                        Padding="15,10"
                        FontSize="14"
                        BorderThickness="0"
                        Background="Transparent"/>
            </Border>
            
            <!-- Password -->
            <TextBlock Text="كلمة المرور" FontWeight="Bold" Margin="0,0,0,8" FontSize="15"/>
            <Border BorderBrush="#DDD" BorderThickness="2" CornerRadius="5" Margin="0,0,0,20">
                <PasswordBox x:Name="PasswordBox" 
                            Height="40" 
                            Padding="15,10"
                            FontSize="14"
                            BorderThickness="0"
                            Background="Transparent"/>
            </Border>
            
            <!-- Remember Me -->
            <CheckBox x:Name="RememberMeCheckBox" 
                     Content="تذكرني" 
                     Margin="0,0,0,25"
                     FontSize="14"/>
            
            <!-- Login Button -->
            <Button x:Name="LoginButton" 
                   Content="🔓 تسجيل الدخول" 
                   Height="45"
                   Background="#4CAF50" 
                   Foreground="White" 
                   BorderThickness="0"
                   FontSize="16"
                   FontWeight="Bold"
                   Cursor="Hand"
                   Click="LoginButton_Click">
                <Button.Effect>
                    <DropShadowEffect Color="Black" Direction="315" ShadowDepth="2" Opacity="0.3"/>
                </Button.Effect>
            </Button>
            
            <!-- Forgot Password -->
            <TextBlock Text="نسيت كلمة المرور؟" 
                      HorizontalAlignment="Center" 
                      Margin="0,15,0,0"
                      Foreground="#2196F3"
                      Cursor="Hand"
                      TextDecorations="Underline"
                      MouseLeftButtonUp="ForgotPassword_Click"/>
        </StackPanel>
        
        <!-- Footer -->
        <Border Grid.Row="2" Background="#F5F5F5" Padding="15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="CreateAccountButton" 
                       Content="👤 إنشاء حساب جديد" 
                       Background="Transparent" 
                       Foreground="#2196F3" 
                       BorderThickness="1"
                       BorderBrush="#2196F3"
                       Padding="15,8" 
                       FontSize="14"
                       FontWeight="Bold"
                       Cursor="Hand"
                       Click="CreateAccountButton_Click"/>

                <Button x:Name="CancelButton"
                       Content="❌ إلغاء"
                       Background="Transparent"
                       Foreground="#F44336"
                       BorderThickness="1"
                       BorderBrush="#F44336"
                       Padding="15,8"
                       Margin="15,0,0,0"
                       FontSize="14"
                       FontWeight="Bold"
                       Cursor="Hand"
                       Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
