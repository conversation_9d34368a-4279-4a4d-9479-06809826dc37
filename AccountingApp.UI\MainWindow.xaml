﻿<Window x:Class="AccountingApp.UI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="نظام المحاسبة المالية - الواجهة الرئيسية"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Style="{StaticResource MaterialWindow}">

    <materialDesign:DialogHost Identifier="MainDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <materialDesign:ColorZone Grid.Row="0"
                                    Mode="PrimaryMid"
                                    Padding="16"
                                    materialDesign:ElevationAssist.Elevation="Dp4">
                <DockPanel>
                    <!-- Logo and Title -->
                    <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="AccountBalance"
                                               Width="32" Height="32"
                                               Foreground="White"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                        <TextBlock Text="نظام المحاسبة المالية"
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Foreground="White"
                                 VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- User Info and Actions -->
                    <StackPanel DockPanel.Dock="Left" Orientation="Horizontal">
                        <Button x:Name="LogoutButton"
                              Style="{StaticResource MaterialDesignToolButton}"
                              ToolTip="تسجيل الخروج"
                              Click="LogoutButton_Click">
                            <materialDesign:PackIcon Kind="Logout"
                                                   Width="24" Height="24"
                                                   Foreground="White"/>
                        </Button>

                        <Button x:Name="SettingsButton"
                              Style="{StaticResource MaterialDesignToolButton}"
                              ToolTip="الإعدادات"
                              Click="SettingsButton_Click">
                            <materialDesign:PackIcon Kind="Settings"
                                                   Width="24" Height="24"
                                                   Foreground="White"/>
                        </Button>

                        <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}"
                                 Margin="8,0"/>

                        <TextBlock x:Name="UserNameTextBlock"
                                 Text="مرحباً، مدير النظام"
                                 Style="{StaticResource MaterialDesignBody1TextBlock}"
                                 Foreground="White"
                                 VerticalAlignment="Center"
                                 Margin="8,0"/>
                    </StackPanel>
                </DockPanel>
            </materialDesign:ColorZone>

            <!-- Main Content -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="280"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Navigation Menu -->
                <materialDesign:Card Grid.Column="0"
                                   Margin="8"
                                   materialDesign:ElevationAssist.Elevation="Dp2">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="0,16">
                            <!-- Dashboard -->
                            <Button x:Name="DashboardButton"
                                  Style="{StaticResource MaterialDesignFlatButton}"
                                  HorizontalAlignment="Stretch"
                                  HorizontalContentAlignment="Right"
                                  Padding="16,12"
                                  Click="DashboardButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="لوحة التحكم"
                                             VerticalAlignment="Center"
                                             Margin="0,0,8,0"/>
                                    <materialDesign:PackIcon Kind="ViewDashboard"
                                                           Width="20" Height="20"/>
                                </StackPanel>
                            </Button>

                            <!-- Accounts Management -->
                            <Expander Header="إدارة الحسابات"
                                    Style="{StaticResource MaterialDesignExpander}">
                                <StackPanel>
                                    <Button Content="دليل الحسابات"
                                          Style="{StaticResource MaterialDesignFlatButton}"
                                          HorizontalAlignment="Stretch"
                                          HorizontalContentAlignment="Right"
                                          Padding="32,8"
                                          Click="ChartOfAccountsButton_Click"/>
                                    <Button Content="إضافة حساب جديد"
                                          Style="{StaticResource MaterialDesignFlatButton}"
                                          HorizontalAlignment="Stretch"
                                          HorizontalContentAlignment="Right"
                                          Padding="32,8"/>
                                </StackPanel>
                            </Expander>

                            <!-- Customers and Suppliers -->
                            <Expander Header="العملاء والموردين"
                                    Style="{StaticResource MaterialDesignExpander}">
                                <StackPanel>
                                    <Button Content="إدارة العملاء"
                                          Style="{StaticResource MaterialDesignFlatButton}"
                                          HorizontalAlignment="Stretch"
                                          HorizontalContentAlignment="Right"
                                          Padding="32,8"/>
                                    <Button Content="إدارة الموردين"
                                          Style="{StaticResource MaterialDesignFlatButton}"
                                          HorizontalAlignment="Stretch"
                                          HorizontalContentAlignment="Right"
                                          Padding="32,8"/>
                                </StackPanel>
                            </Expander>

                            <!-- Products and Inventory -->
                            <Expander Header="الأصناف والمخازن"
                                    Style="{StaticResource MaterialDesignExpander}">
                                <StackPanel>
                                    <Button Content="إدارة الأصناف"
                                          Style="{StaticResource MaterialDesignFlatButton}"
                                          HorizontalAlignment="Stretch"
                                          HorizontalContentAlignment="Right"
                                          Padding="32,8"/>
                                    <Button Content="إدارة المخازن"
                                          Style="{StaticResource MaterialDesignFlatButton}"
                                          HorizontalAlignment="Stretch"
                                          HorizontalContentAlignment="Right"
                                          Padding="32,8"/>
                                    <Button Content="تقارير المخزون"
                                          Style="{StaticResource MaterialDesignFlatButton}"
                                          HorizontalAlignment="Stretch"
                                          HorizontalContentAlignment="Right"
                                          Padding="32,8"/>
                                </StackPanel>
                            </Expander>
                        </StackPanel>
                    </ScrollViewer>
                </materialDesign:Card>

                <!-- Content Area -->
                <materialDesign:Card Grid.Column="1"
                                   Margin="0,8,8,8"
                                   materialDesign:ElevationAssist.Elevation="Dp2">
                    <Grid>
                        <Frame x:Name="MainFrame"
                             NavigationUIVisibility="Hidden"
                             Background="Transparent"/>

                        <!-- Welcome Screen -->
                        <Grid x:Name="WelcomeGrid">
                            <StackPanel HorizontalAlignment="Center"
                                      VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="AccountBalance"
                                                       Width="120" Height="120"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       HorizontalAlignment="Center"
                                                       Margin="0,0,0,24"/>

                                <TextBlock Text="مرحباً بك في نظام المحاسبة المالية"
                                         Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                         HorizontalAlignment="Center"
                                         Margin="0,0,0,16"/>

                                <TextBlock Text="اختر من القائمة الجانبية للبدء"
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         HorizontalAlignment="Center"
                                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </materialDesign:Card>
            </Grid>

            <!-- Status Bar -->
            <StatusBar Grid.Row="2"
                     Background="{DynamicResource MaterialDesignDivider}">
                <StatusBarItem>
                    <TextBlock x:Name="StatusTextBlock"
                             Text="جاهز"/>
                </StatusBarItem>
                <Separator/>
                <StatusBarItem>
                    <TextBlock x:Name="DateTimeTextBlock"/>
                </StatusBarItem>
            </StatusBar>
        </Grid>
    </materialDesign:DialogHost>
</Window>
