<Window x:Class="AccountingApp.UI.SimpleMainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نظام المحاسبة المالية - الواجهة الرئيسية" 
        Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="30"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" Padding="15">
            <DockPanel>
                <!-- Logo and Title -->
                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                    <TextBlock Text="💼" FontSize="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="نظام المحاسبة المالية"
                             FontSize="20"
                             FontWeight="Bold"
                             Foreground="White"
                             VerticalAlignment="Center"/>
                </StackPanel>
                
                <!-- User Info -->
                <StackPanel DockPanel.Dock="Left" Orientation="Horizontal">
                    <Button x:Name="LogoutButton"
                          Content="تسجيل الخروج"
                          Background="#1976D2"
                          Foreground="White"
                          BorderThickness="0"
                          Padding="15,5"
                          Margin="5,0"
                          Click="LogoutButton_Click"/>
                    
                    <TextBlock Text="مرحباً، مدير النظام"
                             Foreground="White"
                             VerticalAlignment="Center"
                             Margin="15,0"/>
                </StackPanel>
            </DockPanel>
        </Border>
        
        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Navigation Menu -->
            <Border Grid.Column="0" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,0,1,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <!-- Dashboard -->
                        <Button Content="🏠 لوحة التحكم"
                              Style="{x:Null}"
                              Background="Transparent"
                              BorderThickness="0"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Right"
                              Padding="15,10"
                              FontSize="14"
                              Margin="0,5"
                              Click="DashboardButton_Click"/>
                        
                        <!-- Separator -->
                        <Separator Margin="0,10"/>
                        
                        <!-- Accounts -->
                        <TextBlock Text="إدارة الحسابات" FontWeight="Bold" Margin="5,10,5,5" Foreground="#666"/>
                        <Button Content="📊 دليل الحسابات"
                              Style="{x:Null}"
                              Background="Transparent"
                              BorderThickness="0"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Right"
                              Padding="20,8"
                              FontSize="13"
                              Margin="0,2"/>
                        <Button Content="➕ إضافة حساب جديد"
                              Style="{x:Null}"
                              Background="Transparent"
                              BorderThickness="0"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Right"
                              Padding="20,8"
                              FontSize="13"
                              Margin="0,2"/>
                        
                        <!-- Customers and Suppliers -->
                        <TextBlock Text="العملاء والموردين" FontWeight="Bold" Margin="5,15,5,5" Foreground="#666"/>
                        <Button Content="👥 إدارة العملاء"
                              Style="{x:Null}"
                              Background="Transparent"
                              BorderThickness="0"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Right"
                              Padding="20,8"
                              FontSize="13"
                              Margin="0,2"/>
                        <Button Content="🏢 إدارة الموردين"
                              Style="{x:Null}"
                              Background="Transparent"
                              BorderThickness="0"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Right"
                              Padding="20,8"
                              FontSize="13"
                              Margin="0,2"/>
                        
                        <!-- Products and Inventory -->
                        <TextBlock Text="الأصناف والمخازن" FontWeight="Bold" Margin="5,15,5,5" Foreground="#666"/>
                        <Button Content="📦 إدارة الأصناف"
                              Style="{x:Null}"
                              Background="Transparent"
                              BorderThickness="0"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Right"
                              Padding="20,8"
                              FontSize="13"
                              Margin="0,2"/>
                        <Button Content="🏪 إدارة المخازن"
                              Style="{x:Null}"
                              Background="Transparent"
                              BorderThickness="0"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Right"
                              Padding="20,8"
                              FontSize="13"
                              Margin="0,2"/>
                        
                        <!-- Invoices -->
                        <TextBlock Text="الفواتير" FontWeight="Bold" Margin="5,15,5,5" Foreground="#666"/>
                        <Button Content="🧾 فواتير المبيعات"
                              Style="{x:Null}"
                              Background="Transparent"
                              BorderThickness="0"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Right"
                              Padding="20,8"
                              FontSize="13"
                              Margin="0,2"/>
                        <Button Content="📋 فواتير المشتريات"
                              Style="{x:Null}"
                              Background="Transparent"
                              BorderThickness="0"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Right"
                              Padding="20,8"
                              FontSize="13"
                              Margin="0,2"/>
                        
                        <!-- Reports -->
                        <TextBlock Text="التقارير" FontWeight="Bold" Margin="5,15,5,5" Foreground="#666"/>
                        <Button Content="📈 التقارير المالية"
                              Style="{x:Null}"
                              Background="Transparent"
                              BorderThickness="0"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Right"
                              Padding="20,8"
                              FontSize="13"
                              Margin="0,2"/>
                    </StackPanel>
                </ScrollViewer>
            </Border>
            
            <!-- Content Area -->
            <Border Grid.Column="1" Background="White" Padding="20">
                <Grid x:Name="ContentGrid">
                    <!-- Welcome Screen -->
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <TextBlock Text="💼" FontSize="80" HorizontalAlignment="Center" Margin="0,0,0,20"/>
                        <TextBlock Text="مرحباً بك في نظام المحاسبة المالية"
                                 FontSize="28"
                                 FontWeight="Bold"
                                 HorizontalAlignment="Center"
                                 Foreground="#2196F3"
                                 Margin="0,0,0,15"/>
                        <TextBlock Text="نظام محاسبة شامل ومتكامل للشركات"
                                 FontSize="16"
                                 HorizontalAlignment="Center"
                                 Foreground="#666"
                                 Margin="0,0,0,30"/>
                        
                        <!-- Quick Actions -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button Content="إنشاء فاتورة مبيعات"
                                  Background="#4CAF50"
                                  Foreground="White"
                                  BorderThickness="0"
                                  Padding="20,10"
                                  Margin="10"
                                  FontSize="14"/>
                            <Button Content="إنشاء فاتورة مشتريات"
                                  Background="#FF9800"
                                  Foreground="White"
                                  BorderThickness="0"
                                  Padding="20,10"
                                  Margin="10"
                                  FontSize="14"/>
                            <Button Content="عرض التقارير"
                                  Background="#9C27B0"
                                  Foreground="White"
                                  BorderThickness="0"
                                  Padding="20,10"
                                  Margin="10"
                                  FontSize="14"/>
                        </StackPanel>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
        
        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#F0F0F0" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <DockPanel Margin="10,5">
                <TextBlock Text="جاهز" DockPanel.Dock="Right"/>
                <TextBlock x:Name="DateTimeTextBlock" DockPanel.Dock="Left" HorizontalAlignment="Left"/>
            </DockPanel>
        </Border>
    </Grid>
</Window>
