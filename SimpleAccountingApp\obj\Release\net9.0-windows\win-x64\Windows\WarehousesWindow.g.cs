﻿#pragma checksum "..\..\..\..\..\Windows\WarehousesWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "188E4A1BC4CC48EE1AC8855CD2C404EAB44D2140"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleAccountingApp.Windows {
    
    
    /// <summary>
    /// WarehousesWindow
    /// </summary>
    public partial class WarehousesWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 43 "..\..\..\..\..\Windows\WarehousesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddWarehouseButton;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\..\Windows\WarehousesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditWarehouseButton;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\..\Windows\WarehousesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteWarehouseButton;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\..\Windows\WarehousesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StockMovementButton;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\..\Windows\WarehousesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\..\Windows\WarehousesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid WarehousesDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleAccountingApp;component/windows/warehouseswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\WarehousesWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 27 "..\..\..\..\..\Windows\WarehousesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.AddWarehouseButton = ((System.Windows.Controls.Button)(target));
            
            #line 50 "..\..\..\..\..\Windows\WarehousesWindow.xaml"
            this.AddWarehouseButton.Click += new System.Windows.RoutedEventHandler(this.AddWarehouseButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.EditWarehouseButton = ((System.Windows.Controls.Button)(target));
            
            #line 60 "..\..\..\..\..\Windows\WarehousesWindow.xaml"
            this.EditWarehouseButton.Click += new System.Windows.RoutedEventHandler(this.EditWarehouseButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.DeleteWarehouseButton = ((System.Windows.Controls.Button)(target));
            
            #line 70 "..\..\..\..\..\Windows\WarehousesWindow.xaml"
            this.DeleteWarehouseButton.Click += new System.Windows.RoutedEventHandler(this.DeleteWarehouseButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.StockMovementButton = ((System.Windows.Controls.Button)(target));
            
            #line 81 "..\..\..\..\..\Windows\WarehousesWindow.xaml"
            this.StockMovementButton.Click += new System.Windows.RoutedEventHandler(this.StockMovementButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 90 "..\..\..\..\..\Windows\WarehousesWindow.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.WarehousesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 105 "..\..\..\..\..\Windows\WarehousesWindow.xaml"
            this.WarehousesDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.WarehousesDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

