using System.Collections.ObjectModel;
using System.Windows;

namespace SimpleAccountingApp.Windows
{
    public partial class SalesSummaryWindow : Window
    {
        private ObservableCollection<SalesInvoice> salesData;

        public SalesSummaryWindow()
        {
            InitializeComponent();
            InitializeDates();
            LoadSalesData();
        }

        private void InitializeDates()
        {
            FromDatePicker.SelectedDate = DateTime.Now.AddDays(-30);
            ToDatePicker.SelectedDate = DateTime.Now;
        }

        private void LoadSalesData()
        {
            salesData = new ObservableCollection<SalesInvoice>
            {
                new SalesInvoice
                {
                    InvoiceNumber = "S001",
                    InvoiceDate = DateTime.Now.AddDays(-5),
                    CustomerName = "شركة الأمل للتجارة",
                    SubTotal = 5000,
                    DiscountAmount = 250,
                    TaxAmount = 712.50m,
                    TotalAmount = 5462.50m,
                    PaidAmount = 5462.50m,
                    RemainingAmount = 0
                },
                new SalesInvoice
                {
                    InvoiceNumber = "S002",
                    InvoiceDate = DateTime.Now.AddDays(-3),
                    CustomerName = "أحمد محمد العلي",
                    SubTotal = 3200,
                    DiscountAmount = 0,
                    TaxAmount = 480,
                    TotalAmount = 3680,
                    PaidAmount = 2000,
                    RemainingAmount = 1680
                },
                new SalesInvoice
                {
                    InvoiceNumber = "S003",
                    InvoiceDate = DateTime.Now.AddDays(-1),
                    CustomerName = "مؤسسة النور للمقاولات",
                    SubTotal = 8500,
                    DiscountAmount = 500,
                    TaxAmount = 1200,
                    TotalAmount = 9200,
                    PaidAmount = 0,
                    RemainingAmount = 9200
                },
                new SalesInvoice
                {
                    InvoiceNumber = "S004",
                    InvoiceDate = DateTime.Now,
                    CustomerName = "فاطمة سالم القحطاني",
                    SubTotal = 1500,
                    DiscountAmount = 75,
                    TaxAmount = 213.75m,
                    TotalAmount = 1638.75m,
                    PaidAmount = 1638.75m,
                    RemainingAmount = 0
                }
            };

            SalesDataGrid.ItemsSource = salesData;
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            // في التطبيق الحقيقي، سيتم فلترة البيانات حسب التواريخ المحددة
            MessageBox.Show("تم تحديث التقرير بنجاح!", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
