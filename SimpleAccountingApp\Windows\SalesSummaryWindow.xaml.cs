using System.Collections.ObjectModel;
using System.Windows;
using SimpleAccountingApp.Helpers;
using SimpleAccountingApp.Data;
using SimpleAccountingApp.Models;
using Microsoft.EntityFrameworkCore;

namespace SimpleAccountingApp.Windows
{
    public partial class SalesSummaryWindow : Window
    {
        private ObservableCollection<SalesInvoice> salesData;
        private AccountingDbContext _context;

        public SalesSummaryWindow()
        {
            InitializeComponent();
            _context = new AccountingDbContext();
            InitializeDates();
            LoadSalesData();
        }

        private void InitializeDates()
        {
            FromDatePicker.SelectedDate = DateTime.Now.AddDays(-30);
            ToDatePicker.SelectedDate = DateTime.Now;
        }

        private async void LoadSalesData()
        {
            try
            {
                await _context.InitializeDatabaseAsync();

                var fromDate = FromDatePicker.SelectedDate ?? DateTime.Now.AddDays(-30);
                var toDate = ToDatePicker.SelectedDate ?? DateTime.Now;

                var salesInvoices = await _context.SalesInvoices
                    .Where(s => s.InvoiceDate >= fromDate && s.InvoiceDate <= toDate)
                    .OrderByDescending(s => s.InvoiceDate)
                    .ToListAsync();

                salesData = new ObservableCollection<SalesInvoice>();

                // إذا لم توجد فواتير، إنشاء فواتير تجريبية
                if (!salesInvoices.Any())
                {
                    await CreateSampleSalesInvoices();
                    salesInvoices = await _context.SalesInvoices
                        .Where(s => s.InvoiceDate >= fromDate && s.InvoiceDate <= toDate)
                        .OrderByDescending(s => s.InvoiceDate)
                        .ToListAsync();
                }

                foreach (var invoice in salesInvoices)
                {
                    salesData.Add(invoice);
                }

                SalesDataGrid.ItemsSource = salesData;
                CalculateTotals();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المبيعات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task CreateSampleSalesInvoices()
        {
            var sampleInvoices = new[]
            {
                new SalesInvoice
                {
                    InvoiceNumber = "S001",
                    InvoiceDate = DateTime.Now.AddDays(-5),
                    CustomerName = "شركة الأمل للتجارة",
                    SubTotal = 5000,
                    DiscountAmount = 250,
                    TaxAmount = 712.50m,
                    TotalAmount = 5462.50m,
                    PaidAmount = 5462.50m,
                    RemainingAmount = 0,
                    Notes = "فاتورة تجريبية"
                },
                new SalesInvoice
                {
                    InvoiceNumber = "S002",
                    InvoiceDate = DateTime.Now.AddDays(-3),
                    CustomerName = "أحمد محمد العلي",
                    SubTotal = 3200,
                    DiscountAmount = 0,
                    TaxAmount = 480,
                    TotalAmount = 3680,
                    PaidAmount = 2000,
                    RemainingAmount = 1680,
                    Notes = "فاتورة تجريبية"
                },
                new SalesInvoice
                {
                    InvoiceNumber = "S003",
                    InvoiceDate = DateTime.Now.AddDays(-1),
                    CustomerName = "مؤسسة النور للمقاولات",
                    SubTotal = 8500,
                    DiscountAmount = 500,
                    TaxAmount = 1200,
                    TotalAmount = 9200,
                    PaidAmount = 0,
                    RemainingAmount = 9200,
                    Notes = "فاتورة تجريبية"
                },
                new SalesInvoice
                {
                    InvoiceNumber = "S004",
                    InvoiceDate = DateTime.Now,
                    CustomerName = "فاطمة سالم القحطاني",
                    SubTotal = 1500,
                    DiscountAmount = 75,
                    TaxAmount = 213.75m,
                    TotalAmount = 1638.75m,
                    PaidAmount = 1638.75m,
                    RemainingAmount = 0,
                    Notes = "فاتورة تجريبية"
                }
            };

            _context.SalesInvoices.AddRange(sampleInvoices);
            await _context.SaveChangesAsync();
        }

        private void CalculateTotals()
        {
            if (salesData?.Any() == true)
            {
                var totalInvoices = salesData.Count;
                var totalAmount = salesData.Sum(s => s.TotalAmount);
                var totalPaid = salesData.Sum(s => s.PaidAmount);
                var totalRemaining = salesData.Sum(s => s.RemainingAmount);

                // يمكن إضافة عرض هذه المجاميع في واجهة المستخدم لاحقاً
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadSalesData();
            MessageBox.Show("تم تحديث التقرير بنجاح!", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            ReportHelper.PrintDataGrid(SalesDataGrid, "ملخص المبيعات");
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            ReportHelper.ExportDataGridToExcel(SalesDataGrid, "ملخص المبيعات");
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
