using System.Windows;
using System.Windows.Controls;
using SimpleAccountingApp.Models;
using SimpleAccountingApp.Data;
using Microsoft.EntityFrameworkCore;

namespace SimpleAccountingApp.Windows
{
    public partial class AddExpenseWindow : Window
    {
        public Expense? NewExpense { get; private set; }
        private bool _isEditMode = false;
        private Expense? _editingExpense;
        private AccountingDbContext _context;

        public AddExpenseWindow()
        {
            InitializeComponent();
            _context = new AccountingDbContext();
            _isEditMode = false;
            InitializeWindow();
        }

        public AddExpenseWindow(Expense expense)
        {
            InitializeComponent();
            _context = new AccountingDbContext();
            _isEditMode = true;
            _editingExpense = expense;
            TitleTextBlock.Text = "تعديل مصروف";
            InitializeWindow();
            LoadExpenseData();
        }

        private async void InitializeWindow()
        {
            // إنشاء رقم مصروف تلقائي
            if (!_isEditMode)
            {
                ExpenseNumberTextBox.Text = GenerateExpenseNumber();
            }

            // تحديد التاريخ الحالي
            ExpenseDatePicker.SelectedDate = DateTime.Now;

            // تحميل حسابات البنوك
            await LoadBankAccounts();
        }

        private async Task LoadBankAccounts()
        {
            try
            {
                await _context.InitializeDatabaseAsync();
                var bankAccounts = await _context.BankAccounts
                    .Where(b => b.IsActive)
                    .OrderBy(b => b.BankName)
                    .ToListAsync();

                BankAccountComboBox.Items.Clear();
                foreach (var account in bankAccounts)
                {
                    var item = new ComboBoxItem
                    {
                        Content = $"{account.BankName} - {account.AccountName}",
                        Tag = account.BankAccountId
                    };
                    BankAccountComboBox.Items.Add(item);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل حسابات البنوك: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GenerateExpenseNumber()
        {
            return $"EXP{DateTime.Now:yyyyMMddHHmmss}";
        }

        private void LoadExpenseData()
        {
            if (_editingExpense != null)
            {
                ExpenseNumberTextBox.Text = _editingExpense.ExpenseNumber;
                ExpenseDatePicker.SelectedDate = _editingExpense.ExpenseDate;
                ExpenseNameTextBox.Text = _editingExpense.ExpenseName;
                AmountTextBox.Text = _editingExpense.Amount.ToString();
                VendorTextBox.Text = _editingExpense.Vendor;
                InvoiceNumberTextBox.Text = _editingExpense.InvoiceNumber;
                DescriptionTextBox.Text = _editingExpense.Description;
                NotesTextBox.Text = _editingExpense.Notes;
                IsApprovedCheckBox.IsChecked = _editingExpense.IsApproved;
                ApprovedByTextBox.Text = _editingExpense.ApprovedBy;

                // تحديد فئة المصروف
                foreach (ComboBoxItem item in CategoryComboBox.Items)
                {
                    if (item.Tag.ToString() == _editingExpense.Category.ToString())
                    {
                        CategoryComboBox.SelectedItem = item;
                        break;
                    }
                }

                // تحديد نوع الدفع
                foreach (ComboBoxItem item in PaymentTypeComboBox.Items)
                {
                    if (item.Tag.ToString() == _editingExpense.PaymentType.ToString())
                    {
                        PaymentTypeComboBox.SelectedItem = item;
                        break;
                    }
                }

                // تحديد الحساب البنكي
                if (_editingExpense.BankAccountId.HasValue)
                {
                    foreach (ComboBoxItem item in BankAccountComboBox.Items)
                    {
                        if ((int)item.Tag == _editingExpense.BankAccountId.Value)
                        {
                            BankAccountComboBox.SelectedItem = item;
                            break;
                        }
                    }
                }
            }
        }

        private void PaymentTypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (PaymentTypeComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                bool showBankAccount = selectedItem.Tag.ToString() == "Bank";
                
                BankAccountLabel.Visibility = showBankAccount ? Visibility.Visible : Visibility.Collapsed;
                BankAccountComboBox.Visibility = showBankAccount ? Visibility.Visible : Visibility.Collapsed;
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(ExpenseNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المصروف.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (CategoryComboBox.SelectedItem == null)
                {
                    MessageBox.Show("يرجى اختيار فئة المصروف.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (PaymentTypeComboBox.SelectedItem == null)
                {
                    MessageBox.Show("يرجى اختيار نوع الدفع.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (!decimal.TryParse(AmountTextBox.Text, out decimal amount) || amount <= 0)
                {
                    MessageBox.Show("يرجى إدخال مبلغ صحيح.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (ExpenseDatePicker.SelectedDate == null)
                {
                    MessageBox.Show("يرجى اختيار تاريخ المصروف.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // التحقق من الحساب البنكي إذا كان نوع الدفع بنكي
                var selectedPaymentType = (ComboBoxItem)PaymentTypeComboBox.SelectedItem;
                int? bankAccountId = null;
                if (selectedPaymentType.Tag.ToString() == "Bank")
                {
                    if (BankAccountComboBox.SelectedItem == null)
                    {
                        MessageBox.Show("يرجى اختيار الحساب البنكي.", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }
                    bankAccountId = (int)((ComboBoxItem)BankAccountComboBox.SelectedItem).Tag;
                }

                var selectedCategory = (ComboBoxItem)CategoryComboBox.SelectedItem;

                if (_isEditMode && _editingExpense != null)
                {
                    // تحديث البيانات الموجودة
                    _editingExpense.ExpenseNumber = ExpenseNumberTextBox.Text.Trim();
                    _editingExpense.ExpenseDate = ExpenseDatePicker.SelectedDate.Value;
                    _editingExpense.ExpenseName = ExpenseNameTextBox.Text.Trim();
                    _editingExpense.Category = Enum.Parse<ExpenseCategory>(selectedCategory.Tag.ToString()!);
                    _editingExpense.PaymentType = Enum.Parse<ExpenseType>(selectedPaymentType.Tag.ToString()!);
                    _editingExpense.Amount = amount;
                    _editingExpense.Vendor = VendorTextBox.Text.Trim();
                    _editingExpense.InvoiceNumber = InvoiceNumberTextBox.Text.Trim();
                    _editingExpense.BankAccountId = bankAccountId;
                    _editingExpense.Description = DescriptionTextBox.Text.Trim();
                    _editingExpense.Notes = NotesTextBox.Text.Trim();
                    _editingExpense.IsApproved = IsApprovedCheckBox.IsChecked ?? false;
                    _editingExpense.ApprovedBy = ApprovedByTextBox.Text.Trim();
                    if (_editingExpense.IsApproved && _editingExpense.ApprovedDate == null)
                    {
                        _editingExpense.ApprovedDate = DateTime.Now;
                    }

                    NewExpense = _editingExpense;
                }
                else
                {
                    // إنشاء مصروف جديد
                    NewExpense = new Expense
                    {
                        ExpenseNumber = ExpenseNumberTextBox.Text.Trim(),
                        ExpenseDate = ExpenseDatePicker.SelectedDate.Value,
                        ExpenseName = ExpenseNameTextBox.Text.Trim(),
                        Category = Enum.Parse<ExpenseCategory>(selectedCategory.Tag.ToString()!),
                        PaymentType = Enum.Parse<ExpenseType>(selectedPaymentType.Tag.ToString()!),
                        Amount = amount,
                        Vendor = VendorTextBox.Text.Trim(),
                        InvoiceNumber = InvoiceNumberTextBox.Text.Trim(),
                        BankAccountId = bankAccountId,
                        Description = DescriptionTextBox.Text.Trim(),
                        Notes = NotesTextBox.Text.Trim(),
                        IsApproved = IsApprovedCheckBox.IsChecked ?? false,
                        ApprovedBy = ApprovedByTextBox.Text.Trim(),
                        ApprovedDate = (IsApprovedCheckBox.IsChecked ?? false) ? DateTime.Now : null
                    };
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
