using System.Collections.ObjectModel;
using System.Windows;
using SimpleAccountingApp.Models;
using SimpleAccountingApp.Data;
using Microsoft.EntityFrameworkCore;

namespace SimpleAccountingApp.Windows
{
    public partial class BankTransactionsWindow : Window
    {
        private BankAccount _bankAccount;
        private ObservableCollection<BankTransaction> transactions = new();
        private AccountingDbContext _context;

        public BankTransactionsWindow(BankAccount bankAccount)
        {
            InitializeComponent();
            _bankAccount = bankAccount;
            _context = new AccountingDbContext();
            InitializeWindow();
            LoadTransactions();
        }

        private void InitializeWindow()
        {
            TitleTextBlock.Text = $"العمليات المصرفية - {_bankAccount.AccountName}";
            AccountInfoTextBlock.Text = $"البنك: {_bankAccount.BankName} | رقم الحساب: {_bankAccount.AccountNumber} | الرصيد: {_bankAccount.Balance:N2} ريال";
        }

        private async void LoadTransactions()
        {
            try
            {
                await _context.InitializeDatabaseAsync();
                var transactionsList = await _context.BankTransactions
                    .Where(t => t.BankAccountId == _bankAccount.BankAccountId)
                    .OrderByDescending(t => t.TransactionDate)
                    .ToListAsync();

                transactions.Clear();
                foreach (var transaction in transactionsList)
                {
                    transactions.Add(transaction);
                }

                TransactionsDataGrid.ItemsSource = transactions;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العمليات المصرفية: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void AddDepositButton_Click(object sender, RoutedEventArgs e)
        {
            var transactionWindow = new AddBankTransactionWindow(_bankAccount, BankTransactionType.Deposit);
            if (transactionWindow.ShowDialog() == true)
            {
                var newTransaction = transactionWindow.NewTransaction;
                if (newTransaction != null)
                {
                    try
                    {
                        // إضافة العملية
                        _context.BankTransactions.Add(newTransaction);

                        // تحديث رصيد الحساب
                        _bankAccount.Balance += newTransaction.Amount;
                        _context.BankAccounts.Update(_bankAccount);

                        await _context.SaveChangesAsync();

                        transactions.Add(newTransaction);
                        RefreshTransactionsGrid();
                        UpdateAccountInfo();

                        MessageBox.Show("تم إضافة عملية الإيداع بنجاح!", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حفظ العملية: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private async void AddWithdrawalButton_Click(object sender, RoutedEventArgs e)
        {
            var transactionWindow = new AddBankTransactionWindow(_bankAccount, BankTransactionType.Withdrawal);
            if (transactionWindow.ShowDialog() == true)
            {
                var newTransaction = transactionWindow.NewTransaction;
                if (newTransaction != null)
                {
                    try
                    {
                        // إضافة العملية
                        _context.BankTransactions.Add(newTransaction);

                        // تحديث رصيد الحساب
                        _bankAccount.Balance -= newTransaction.Amount;
                        _context.BankAccounts.Update(_bankAccount);

                        await _context.SaveChangesAsync();

                        transactions.Add(newTransaction);
                        RefreshTransactionsGrid();
                        UpdateAccountInfo();

                        MessageBox.Show("تم إضافة عملية السحب بنجاح!", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حفظ العملية: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private async void AddTransferButton_Click(object sender, RoutedEventArgs e)
        {
            var transactionWindow = new AddBankTransactionWindow(_bankAccount, BankTransactionType.Transfer);
            if (transactionWindow.ShowDialog() == true)
            {
                var newTransaction = transactionWindow.NewTransaction;
                if (newTransaction != null)
                {
                    try
                    {
                        // إضافة العملية
                        _context.BankTransactions.Add(newTransaction);

                        // تحديث رصيد الحساب المرسل
                        _bankAccount.Balance -= newTransaction.Amount;
                        _context.BankAccounts.Update(_bankAccount);

                        // تحديث رصيد الحساب المستقبل إذا كان محدداً
                        if (newTransaction.ToBankAccountId.HasValue)
                        {
                            var toAccount = await _context.BankAccounts
                                .FindAsync(newTransaction.ToBankAccountId.Value);
                            if (toAccount != null)
                            {
                                toAccount.Balance += newTransaction.Amount;
                                _context.BankAccounts.Update(toAccount);
                            }
                        }

                        await _context.SaveChangesAsync();

                        transactions.Add(newTransaction);
                        RefreshTransactionsGrid();
                        UpdateAccountInfo();

                        MessageBox.Show("تم إضافة عملية التحويل بنجاح!", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حفظ العملية: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadTransactions();
        }

        private void RefreshTransactionsGrid()
        {
            TransactionsDataGrid.Items.Refresh();
        }

        private void UpdateAccountInfo()
        {
            AccountInfoTextBlock.Text = $"البنك: {_bankAccount.BankName} | رقم الحساب: {_bankAccount.AccountNumber} | الرصيد: {_bankAccount.Balance:N2} ريال";
        }
    }
}
