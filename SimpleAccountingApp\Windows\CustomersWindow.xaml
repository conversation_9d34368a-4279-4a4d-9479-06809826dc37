<Window x:Class="SimpleAccountingApp.Windows.CustomersWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة العملاء - نظام المحاسبة المالية" 
        Height="700" Width="1100"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="50"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" Padding="15">
            <DockPanel>
                <Button DockPanel.Dock="Left" 
                       Content="✖ إغلاق" 
                       Background="#1976D2" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,5" 
                       Click="CloseButton_Click"/>
                
                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                    <TextBlock Text="👥" FontSize="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="إدارة العملاء" 
                              FontSize="20" 
                              FontWeight="Bold" 
                              Foreground="White" 
                              VerticalAlignment="Center"/>
                </StackPanel>
            </DockPanel>
        </Border>
        
        <!-- Toolbar -->
        <Border Grid.Row="1" Background="#F5F5F5" Padding="10" BorderBrush="#DDD" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <Button x:Name="AddCustomerButton" 
                       Content="➕ إضافة عميل جديد" 
                       Background="#4CAF50" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="5,0"
                       Click="AddCustomerButton_Click"/>
                
                <Button x:Name="EditCustomerButton" 
                       Content="✏️ تعديل" 
                       Background="#FF9800" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="5,0"
                       IsEnabled="False"
                       Click="EditCustomerButton_Click"/>
                
                <Button x:Name="DeleteCustomerButton" 
                       Content="🗑️ حذف" 
                       Background="#F44336" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="5,0"
                       IsEnabled="False"
                       Click="DeleteCustomerButton_Click"/>
                
                <Separator Margin="10,0"/>
                
                <TextBlock Text="البحث:" VerticalAlignment="Center" Margin="10,0,5,0"/>
                <TextBox x:Name="SearchTextBox" 
                        Width="200" 
                        Height="30"
                        Padding="5"
                        TextChanged="SearchTextBox_TextChanged"/>
            </StackPanel>
        </Border>
        
        <!-- Customers DataGrid -->
        <Grid Grid.Row="2" Margin="10">
            <DataGrid x:Name="CustomersDataGrid"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     AlternatingRowBackground="#F9F9F9"
                     SelectionChanged="CustomersDataGrid_SelectionChanged">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم العميل" 
                                       Binding="{Binding CustomerCode}" 
                                       Width="100"/>
                    
                    <DataGridTextColumn Header="اسم العميل" 
                                       Binding="{Binding CustomerName}" 
                                       Width="200"/>
                    
                    <DataGridTextColumn Header="نوع العميل" 
                                       Binding="{Binding CustomerType}" 
                                       Width="100"/>
                    
                    <DataGridTextColumn Header="الهاتف" 
                                       Binding="{Binding Phone}" 
                                       Width="120"/>
                    
                    <DataGridTextColumn Header="الجوال" 
                                       Binding="{Binding Mobile}" 
                                       Width="120"/>
                    
                    <DataGridTextColumn Header="البريد الإلكتروني" 
                                       Binding="{Binding Email}" 
                                       Width="180"/>
                    
                    <DataGridTextColumn Header="المدينة" 
                                       Binding="{Binding City}" 
                                       Width="100"/>
                    
                    <DataGridTextColumn Header="الرصيد الحالي"
                                       Binding="{Binding CurrentBalance, StringFormat=N2}"
                                       Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="الحالة" 
                                       Binding="{Binding IsActiveText}" 
                                       Width="80">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsActive}" Value="True">
                                        <Setter Property="Foreground" Value="Green"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding IsActive}" Value="False">
                                        <Setter Property="Foreground" Value="Red"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>
    </Grid>
</Window>
