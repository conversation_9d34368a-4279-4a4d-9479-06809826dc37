﻿#pragma checksum "..\..\..\..\..\Windows\AddBankAccountWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "3EBE0F4EC53C5771357A8522047DD2179C8B7F4E"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleAccountingApp.Windows {
    
    
    /// <summary>
    /// AddBankAccountWindow
    /// </summary>
    public partial class AddBankAccountWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 15 "..\..\..\..\..\Windows\AddBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleTextBlock;
        
        #line default
        #line hidden
        
        
        #line 24 "..\..\..\..\..\Windows\AddBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AccountNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 28 "..\..\..\..\..\Windows\AddBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AccountNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\..\..\Windows\AddBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BankNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\..\..\Windows\AddBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BranchNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\..\..\Windows\AddBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IBANTextBox;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\..\..\Windows\AddBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SwiftCodeTextBox;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\..\Windows\AddBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AccountTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\..\Windows\AddBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BalanceTextBox;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\..\Windows\AddBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CreditLimitTextBox;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\..\Windows\AddBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CurrencyComboBox;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\..\Windows\AddBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsActiveCheckBox;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\..\Windows\AddBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\..\Windows\AddBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\..\Windows\AddBankAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleAccountingApp;component/windows/addbankaccountwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\AddBankAccountWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TitleTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.AccountNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.AccountNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.BankNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.BranchNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.IBANTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.SwiftCodeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.AccountTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.BalanceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.CreditLimitTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.CurrencyComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 12:
            this.IsActiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 13:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 87 "..\..\..\..\..\Windows\AddBankAccountWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 89 "..\..\..\..\..\Windows\AddBankAccountWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

