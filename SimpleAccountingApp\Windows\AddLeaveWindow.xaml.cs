using System.Windows;
using System.Windows.Controls;
using SimpleAccountingApp.Models;

namespace SimpleAccountingApp.Windows
{
    public partial class AddLeaveWindow : Window
    {
        public Leave? NewLeave { get; private set; }
        private bool _isEditMode = false;
        private Leave? _editingLeave;
        private Employee _employee;

        public AddLeaveWindow(Employee employee)
        {
            InitializeComponent();
            _employee = employee;
            _isEditMode = false;
            InitializeWindow();
        }

        public AddLeaveWindow(Employee employee, Leave leave)
        {
            InitializeComponent();
            _employee = employee;
            _isEditMode = true;
            _editingLeave = leave;
            TitleTextBlock.Text = "تعديل طلب إجازة";
            InitializeWindow();
            LoadLeaveData();
        }

        private void InitializeWindow()
        {
            // إنشاء رقم إجازة تلقائي
            if (!_isEditMode)
            {
                LeaveNumberTextBox.Text = GenerateLeaveNumber();
            }

            // تحديد التاريخ الحالي
            StartDatePicker.SelectedDate = DateTime.Now;
            EndDatePicker.SelectedDate = DateTime.Now.AddDays(1);
            
            CalculateDays();
        }

        private string GenerateLeaveNumber()
        {
            return $"LV{DateTime.Now:yyyyMMddHHmmss}";
        }

        private void LoadLeaveData()
        {
            if (_editingLeave != null)
            {
                LeaveNumberTextBox.Text = _editingLeave.LeaveNumber;
                StartDatePicker.SelectedDate = _editingLeave.StartDate;
                EndDatePicker.SelectedDate = _editingLeave.EndDate;
                DaysTextBox.Text = _editingLeave.Days.ToString();
                ReasonTextBox.Text = _editingLeave.Reason;
                NotesTextBox.Text = _editingLeave.Notes;

                // تحديد نوع الإجازة
                foreach (ComboBoxItem item in LeaveTypeComboBox.Items)
                {
                    if (item.Tag.ToString() == _editingLeave.LeaveType.ToString())
                    {
                        LeaveTypeComboBox.SelectedItem = item;
                        break;
                    }
                }
            }
        }

        private void DatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            CalculateDays();
        }

        private void CalculateDays()
        {
            if (StartDatePicker.SelectedDate.HasValue && EndDatePicker.SelectedDate.HasValue)
            {
                var startDate = StartDatePicker.SelectedDate.Value;
                var endDate = EndDatePicker.SelectedDate.Value;
                
                if (endDate >= startDate)
                {
                    var days = (endDate - startDate).Days + 1;
                    DaysTextBox.Text = days.ToString();
                }
                else
                {
                    DaysTextBox.Text = "0";
                }
            }
            else
            {
                DaysTextBox.Text = "0";
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (LeaveTypeComboBox.SelectedItem == null)
                {
                    MessageBox.Show("يرجى اختيار نوع الإجازة.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (StartDatePicker.SelectedDate == null)
                {
                    MessageBox.Show("يرجى اختيار تاريخ البداية.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (EndDatePicker.SelectedDate == null)
                {
                    MessageBox.Show("يرجى اختيار تاريخ النهاية.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (EndDatePicker.SelectedDate < StartDatePicker.SelectedDate)
                {
                    MessageBox.Show("تاريخ النهاية يجب أن يكون بعد تاريخ البداية.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (string.IsNullOrWhiteSpace(ReasonTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال سبب الإجازة.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (!int.TryParse(DaysTextBox.Text, out int days) || days <= 0)
                {
                    MessageBox.Show("عدد الأيام غير صحيح.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var selectedLeaveType = (ComboBoxItem)LeaveTypeComboBox.SelectedItem;

                if (_isEditMode && _editingLeave != null)
                {
                    // تحديث البيانات الموجودة
                    _editingLeave.LeaveNumber = LeaveNumberTextBox.Text.Trim();
                    _editingLeave.LeaveType = Enum.Parse<LeaveType>(selectedLeaveType.Tag.ToString()!);
                    _editingLeave.StartDate = StartDatePicker.SelectedDate.Value;
                    _editingLeave.EndDate = EndDatePicker.SelectedDate.Value;
                    _editingLeave.Days = days;
                    _editingLeave.Reason = ReasonTextBox.Text.Trim();
                    _editingLeave.Notes = NotesTextBox.Text.Trim();

                    NewLeave = _editingLeave;
                }
                else
                {
                    // إنشاء إجازة جديدة
                    NewLeave = new Leave
                    {
                        LeaveNumber = LeaveNumberTextBox.Text.Trim(),
                        EmployeeId = _employee.EmployeeId,
                        LeaveType = Enum.Parse<LeaveType>(selectedLeaveType.Tag.ToString()!),
                        StartDate = StartDatePicker.SelectedDate.Value,
                        EndDate = EndDatePicker.SelectedDate.Value,
                        Days = days,
                        Reason = ReasonTextBox.Text.Trim(),
                        Status = LeaveStatus.Pending,
                        Notes = NotesTextBox.Text.Trim()
                    };
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
