<Window x:Class="SimpleAccountingApp.Windows.VATReturnsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="الإقرارات الضريبية" 
        Height="400" Width="500"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" Padding="15">
            <TextBlock Text="📊 الإقرارات الضريبية" 
                      FontSize="18" 
                      FontWeight="Bold" 
                      Foreground="White" 
                      VerticalAlignment="Center"/>
        </Border>
        
        <!-- Content -->
        <StackPanel Grid.Row="1" Margin="30" VerticalAlignment="Center">
            <TextBlock Text="تقديم الإقرارات الضريبية" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,20"/>
            
            <TextBlock TextWrapping="Wrap" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,0,0,20">
                يتم تقديم الإقرارات الضريبية من خلال البوابة الإلكترونية
                <LineBreak/>
                لهيئة الزكاة والضرائب والجمارك
            </TextBlock>
            
            <TextBlock Text="🌐 https://zatca.gov.sa" 
                      FontSize="14" 
                      FontWeight="Bold" 
                      Foreground="Blue" 
                      HorizontalAlignment="Center"/>
        </StackPanel>
        
        <!-- Buttons -->
        <Border Grid.Row="2" Background="#F5F5F5" Padding="15">
            <Button Content="✖ إغلاق" 
                   Background="#F44336" 
                   Foreground="White" 
                   BorderThickness="0"
                   Padding="20,10" 
                   HorizontalAlignment="Center"
                   FontWeight="Bold"
                   Click="CloseButton_Click"/>
        </Border>
    </Grid>
</Window>
