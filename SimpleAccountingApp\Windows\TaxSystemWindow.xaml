<Window x:Class="SimpleAccountingApp.Windows.TaxSystemWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نظام الزكاة والضرائب والجمارك - المملكة العربية السعودية" 
        Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="#1B5E20" Offset="0"/>
                    <GradientStop Color="#2E7D32" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <DockPanel Margin="20">
                <Button DockPanel.Dock="Left" 
                       Content="✖ إغلاق" 
                       Background="#1B5E20" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       FontWeight="Bold"
                       Click="CloseButton_Click"/>
                
                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="🇸🇦" FontSize="32" Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="🏛️ هيئة الزكاة والضرائب والجمارك" 
                                  FontSize="18" 
                                  FontWeight="Bold" 
                                  Foreground="White"/>
                        <TextBlock Text="المملكة العربية السعودية" 
                                  FontSize="14" 
                                  Foreground="White" 
                                  Margin="0,2,0,0"/>
                    </StackPanel>
                </StackPanel>
            </DockPanel>
        </Border>
        
        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- VAT Section -->
                <Border Grid.Row="0" Background="#E8F5E8" Padding="20" Margin="0,0,0,20" CornerRadius="10">
                    <StackPanel>
                        <TextBlock Text="🧾 ضريبة القيمة المضافة (VAT)" FontSize="20" FontWeight="Bold" Margin="0,0,0,15" Foreground="#1B5E20"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- VAT Registration -->
                            <Border Grid.Column="0" Background="White" Padding="20" Margin="5" CornerRadius="8" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="📋" FontSize="32" HorizontalAlignment="Center" Foreground="#4CAF50"/>
                                    <TextBlock Text="التسجيل في ضريبة القيمة المضافة" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5" TextWrapping="Wrap"/>
                                    <TextBlock Text="الرقم الضريبي: 300123456789003" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15" Foreground="Blue"/>
                                    <Button x:Name="VATRegistrationButton" 
                                           Content="إدارة التسجيل" 
                                           Background="#4CAF50" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           FontWeight="Bold"
                                           Click="VATRegistrationButton_Click"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- VAT Returns -->
                            <Border Grid.Column="1" Background="White" Padding="20" Margin="5" CornerRadius="8" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="📊" FontSize="32" HorizontalAlignment="Center" Foreground="#2196F3"/>
                                    <TextBlock Text="الإقرارات الضريبية" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                    <TextBlock Text="الإقرار الشهري والربع سنوي" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="VATReturnsButton" 
                                           Content="تقديم الإقرار" 
                                           Background="#2196F3" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           FontWeight="Bold"
                                           Click="VATReturnsButton_Click"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- VAT Calculation -->
                            <Border Grid.Column="2" Background="White" Padding="20" Margin="5" CornerRadius="8" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="🧮" FontSize="32" HorizontalAlignment="Center" Foreground="#FF9800"/>
                                    <TextBlock Text="حاسبة ضريبة القيمة المضافة" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5" TextWrapping="Wrap"/>
                                    <TextBlock Text="معدل الضريبة: 15%" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15" Foreground="Red"/>
                                    <Button x:Name="VATCalculatorButton" 
                                           Content="حاسبة الضريبة" 
                                           Background="#FF9800" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           FontWeight="Bold"
                                           Click="VATCalculatorButton_Click"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </StackPanel>
                </Border>
                
                <!-- Zakat Section -->
                <Border Grid.Row="1" Background="#FFF3E0" Padding="20" Margin="0,0,0,20" CornerRadius="10">
                    <StackPanel>
                        <TextBlock Text="🕌 الزكاة" FontSize="20" FontWeight="Bold" Margin="0,0,0,15" Foreground="#E65100"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Zakat Calculation -->
                            <Border Grid.Column="0" Background="White" Padding="20" Margin="5" CornerRadius="8" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="📊" FontSize="32" HorizontalAlignment="Center" Foreground="#FF9800"/>
                                    <TextBlock Text="حساب الزكاة" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                    <TextBlock Text="زكاة الشركات والأفراد" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="ZakatCalculationButton" 
                                           Content="حساب الزكاة" 
                                           Background="#FF9800" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           FontWeight="Bold"
                                           Click="ZakatCalculationButton_Click"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- Zakat Returns -->
                            <Border Grid.Column="1" Background="White" Padding="20" Margin="5" CornerRadius="8" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="📋" FontSize="32" HorizontalAlignment="Center" Foreground="#4CAF50"/>
                                    <TextBlock Text="إقرار الزكاة" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                    <TextBlock Text="الإقرار السنوي للزكاة" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="ZakatReturnsButton" 
                                           Content="تقديم الإقرار" 
                                           Background="#4CAF50" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           FontWeight="Bold"
                                           Click="ZakatReturnsButton_Click"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- Zakat Payment -->
                            <Border Grid.Column="2" Background="White" Padding="20" Margin="5" CornerRadius="8" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="💳" FontSize="32" HorizontalAlignment="Center" Foreground="#2196F3"/>
                                    <TextBlock Text="دفع الزكاة" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                    <TextBlock Text="الدفع الإلكتروني" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="ZakatPaymentButton" 
                                           Content="دفع الزكاة" 
                                           Background="#2196F3" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           FontWeight="Bold"
                                           Click="ZakatPaymentButton_Click"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </StackPanel>
                </Border>
                
                <!-- E-Invoicing Section -->
                <Border Grid.Row="2" Background="#E3F2FD" Padding="20" Margin="0,0,0,20" CornerRadius="10">
                    <StackPanel>
                        <TextBlock Text="📱 الفوترة الإلكترونية" FontSize="20" FontWeight="Bold" Margin="0,0,0,15" Foreground="#1565C0"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- E-Invoice Integration -->
                            <Border Grid.Column="0" Background="White" Padding="20" Margin="5" CornerRadius="8" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="🔗" FontSize="32" HorizontalAlignment="Center" Foreground="#2196F3"/>
                                    <TextBlock Text="ربط النظام بالفوترة الإلكترونية" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5" TextWrapping="Wrap"/>
                                    <TextBlock Text="API Integration" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="EInvoiceIntegrationButton" 
                                           Content="إعداد الربط" 
                                           Background="#2196F3" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           FontWeight="Bold"
                                           Click="EInvoiceIntegrationButton_Click"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- QR Code Generation -->
                            <Border Grid.Column="1" Background="White" Padding="20" Margin="5" CornerRadius="8" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="📱" FontSize="32" HorizontalAlignment="Center" Foreground="#4CAF50"/>
                                    <TextBlock Text="إنشاء رمز QR للفواتير" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5" TextWrapping="Wrap"/>
                                    <TextBlock Text="متوافق مع المعايير السعودية" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="QRCodeButton" 
                                           Content="إنشاء رمز QR" 
                                           Background="#4CAF50" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           FontWeight="Bold"
                                           Click="QRCodeButton_Click"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- E-Invoice Validation -->
                            <Border Grid.Column="2" Background="White" Padding="20" Margin="5" CornerRadius="8" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="✅" FontSize="32" HorizontalAlignment="Center" Foreground="#FF9800"/>
                                    <TextBlock Text="التحقق من صحة الفواتير" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5" TextWrapping="Wrap"/>
                                    <TextBlock Text="التحقق من المعايير" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="InvoiceValidationButton" 
                                           Content="التحقق من الفواتير" 
                                           Background="#FF9800" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           FontWeight="Bold"
                                           Click="InvoiceValidationButton_Click"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </StackPanel>
                </Border>
                
                <!-- Reports Section -->
                <Border Grid.Row="3" Background="#F3E5F5" Padding="20" CornerRadius="10">
                    <StackPanel>
                        <TextBlock Text="📊 التقارير الضريبية" FontSize="20" FontWeight="Bold" Margin="0,0,0,15" Foreground="#7B1FA2"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Tax Reports -->
                            <Border Grid.Column="0" Background="White" Padding="20" Margin="5" CornerRadius="8" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="📈" FontSize="32" HorizontalAlignment="Center" Foreground="#9C27B0"/>
                                    <TextBlock Text="تقارير ضريبة القيمة المضافة" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5" TextWrapping="Wrap"/>
                                    <TextBlock Text="تقارير شهرية وربع سنوية" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="TaxReportsButton" 
                                           Content="عرض التقارير" 
                                           Background="#9C27B0" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           FontWeight="Bold"
                                           Click="TaxReportsButton_Click"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- Compliance Check -->
                            <Border Grid.Column="1" Background="White" Padding="20" Margin="5" CornerRadius="8" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="🔍" FontSize="32" HorizontalAlignment="Center" Foreground="#F44336"/>
                                    <TextBlock Text="فحص الامتثال" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                    <TextBlock Text="التحقق من الامتثال للقوانين" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="ComplianceCheckButton" 
                                           Content="فحص الامتثال" 
                                           Background="#F44336" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           FontWeight="Bold"
                                           Click="ComplianceCheckButton_Click"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- Help & Support -->
                            <Border Grid.Column="2" Background="White" Padding="20" Margin="5" CornerRadius="8" BorderBrush="#DDD" BorderThickness="1">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="❓" FontSize="32" HorizontalAlignment="Center" Foreground="#607D8B"/>
                                    <TextBlock Text="المساعدة والدعم" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                    <TextBlock Text="أدلة وإرشادات" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                    <Button x:Name="HelpSupportButton" 
                                           Content="المساعدة" 
                                           Background="#607D8B" 
                                           Foreground="White" 
                                           BorderThickness="0"
                                           Padding="15,8"
                                           FontWeight="Bold"
                                           Click="HelpSupportButton_Click"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </StackPanel>
                </Border>
            </Grid>
        </ScrollViewer>
    </Grid>
</Window>
