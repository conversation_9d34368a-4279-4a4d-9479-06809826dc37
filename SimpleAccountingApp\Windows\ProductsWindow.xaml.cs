using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace SimpleAccountingApp.Windows
{
    public partial class ProductsWindow : Window
    {
        private ObservableCollection<Product> products;

        public ProductsWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            products = new ObservableCollection<Product>
            {
                new Product
                {
                    ProductCode = "P001",
                    ProductName = "لابتوب ديل إنسبايرون",
                    Barcode = "*************",
                    Category = "أجهزة كمبيوتر",
                    Unit = "قطعة",
                    PurchasePrice = 2500,
                    SalePrice = 3200,
                    CurrentStock = 15,
                    MinimumStock = 5,
                    MaximumStock = 50,
                    IsActive = true,
                    Description = "لابتوب ديل إنسبايرون 15 بوصة"
                },
                new Product
                {
                    ProductCode = "P002",
                    ProductName = "طابعة HP ليزر",
                    Barcode = "2345678901234",
                    Category = "طابعات",
                    Unit = "قطعة",
                    PurchasePrice = 800,
                    SalePrice = 1100,
                    CurrentStock = 8,
                    MinimumStock = 3,
                    MaximumStock = 20,
                    IsActive = true,
                    Description = "طابعة ليزر أبيض وأسود"
                },
                new Product
                {
                    ProductCode = "P003",
                    ProductName = "ورق A4",
                    Barcode = "3456789012345",
                    Category = "مستلزمات مكتبية",
                    Unit = "علبة",
                    PurchasePrice = 25,
                    SalePrice = 35,
                    CurrentStock = 2,
                    MinimumStock = 10,
                    MaximumStock = 100,
                    IsActive = true,
                    Description = "ورق A4 أبيض 500 ورقة"
                },
                new Product
                {
                    ProductCode = "P004",
                    ProductName = "ماوس لاسلكي",
                    Barcode = "4567890123456",
                    Category = "ملحقات كمبيوتر",
                    Unit = "قطعة",
                    PurchasePrice = 45,
                    SalePrice = 65,
                    CurrentStock = 25,
                    MinimumStock = 10,
                    MaximumStock = 50,
                    IsActive = true,
                    Description = "ماوس لاسلكي بتقنية البلوتوث"
                },
                new Product
                {
                    ProductCode = "P005",
                    ProductName = "كيبورد ميكانيكي",
                    Barcode = "5678901234567",
                    Category = "ملحقات كمبيوتر",
                    Unit = "قطعة",
                    PurchasePrice = 120,
                    SalePrice = 180,
                    CurrentStock = 0,
                    MinimumStock = 5,
                    MaximumStock = 30,
                    IsActive = false,
                    Description = "كيبورد ميكانيكي للألعاب - متوقف"
                }
            };

            ProductsDataGrid.ItemsSource = products;
        }

        private void ProductsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            bool hasSelection = ProductsDataGrid.SelectedItem != null;
            EditProductButton.IsEnabled = hasSelection;
            DeleteProductButton.IsEnabled = hasSelection;
        }

        private void AddProductButton_Click(object sender, RoutedEventArgs e)
        {
            var addProductWindow = new AddProductWindow();
            if (addProductWindow.ShowDialog() == true)
            {
                MessageBox.Show("تم إضافة الصنف بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: إعادة تحميل البيانات
            }
        }

        private void EditProductButton_Click(object sender, RoutedEventArgs e)
        {
            if (ProductsDataGrid.SelectedItem is Product selectedProduct)
            {
                var editProductWindow = new AddProductWindow(selectedProduct);
                if (editProductWindow.ShowDialog() == true)
                {
                    MessageBox.Show("تم تعديل الصنف بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    // TODO: تحديث البيانات
                }
            }
        }

        private void DeleteProductButton_Click(object sender, RoutedEventArgs e)
        {
            if (ProductsDataGrid.SelectedItem is Product selectedProduct)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الصنف '{selectedProduct.ProductName}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    products.Remove(selectedProduct);
                    MessageBox.Show("تم حذف الصنف بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void StockReportButton_Click(object sender, RoutedEventArgs e)
        {
            var stockReportWindow = new StockReportWindow(products.ToList());
            stockReportWindow.ShowDialog();
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            string searchText = SearchTextBox.Text.ToLower();
            if (string.IsNullOrWhiteSpace(searchText))
            {
                ProductsDataGrid.ItemsSource = products;
            }
            else
            {
                var filteredProducts = products.Where(p => 
                    p.ProductName.ToLower().Contains(searchText) ||
                    p.ProductCode.ToLower().Contains(searchText) ||
                    p.Barcode.Contains(searchText) ||
                    p.Category.ToLower().Contains(searchText)
                ).ToList();
                
                ProductsDataGrid.ItemsSource = filteredProducts;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    // نموذج بيانات الصنف
    public class Product
    {
        public string ProductCode { get; set; } = "";
        public string ProductName { get; set; } = "";
        public string Barcode { get; set; } = "";
        public string Category { get; set; } = "";
        public string Unit { get; set; } = "";
        public decimal PurchasePrice { get; set; }
        public decimal SalePrice { get; set; }
        public decimal CurrentStock { get; set; }
        public decimal MinimumStock { get; set; }
        public decimal MaximumStock { get; set; }
        public bool IsActive { get; set; } = true;
        public string Description { get; set; } = "";
        
        public string IsActiveText => IsActive ? "نشط" : "غير نشط";
        public bool IsLowStock => CurrentStock <= MinimumStock;
        public decimal ProfitMargin => SalePrice - PurchasePrice;
        public decimal ProfitPercentage => PurchasePrice > 0 ? (ProfitMargin / PurchasePrice) * 100 : 0;
    }
}
