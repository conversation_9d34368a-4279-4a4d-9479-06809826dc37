using System.Windows;
using System.Windows.Input;
using AccountingApp.Services;
using AccountingApp.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace AccountingApp.UI.Views
{
    /// <summary>
    /// شاشة تسجيل الدخول - Login Window
    /// </summary>
    public partial class LoginWindow : Window
    {
        private readonly IAuthenticationService _authService;
        private readonly IServiceProvider _serviceProvider;

        public LoginWindow()
        {
            InitializeComponent();
            
            // إعداد الخدمات
            var services = new ServiceCollection();
            ConfigureServices(services);
            _serviceProvider = services.BuildServiceProvider();
            
            _authService = _serviceProvider.GetRequiredService<IAuthenticationService>();
            
            // إعداد الأحداث
            PasswordBox.KeyDown += PasswordBox_KeyDown;
            UsernameTextBox.KeyDown += UsernameTextBox_KeyDown;
            
            // تركيز على حقل اسم المستخدم
            Loaded += (s, e) => UsernameTextBox.Focus();
        }

        private void ConfigureServices(ServiceCollection services)
        {
            // إعداد قاعدة البيانات
            services.AddDbContext<AccountingDbContext>(options =>
                options.UseSqlServer(GetConnectionString()));
            
            // إعداد الخدمات
            services.AddScoped<IAuthenticationService, AuthenticationService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IAccountService, AccountService>();
        }

        private string GetConnectionString()
        {
            // استخدام LocalDB للتطوير
            return "Server=(localdb)\\mssqllocaldb;Database=AccountingAppDb;Trusted_Connection=true;MultipleActiveResultSets=true";
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            await PerformLogin();
        }

        private void UsernameTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                PasswordBox.Focus();
            }
        }

        private async void PasswordBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                await PerformLogin();
            }
        }

        private async Task PerformLogin()
        {
            try
            {
                // إخفاء رسالة الخطأ
                ErrorMessageTextBlock.Visibility = Visibility.Collapsed;
                
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(UsernameTextBox.Text))
                {
                    ShowError("يرجى إدخال اسم المستخدم");
                    UsernameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(PasswordBox.Password))
                {
                    ShowError("يرجى إدخال كلمة المرور");
                    PasswordBox.Focus();
                    return;
                }

                // تعطيل زر تسجيل الدخول أثناء المعالجة
                LoginButton.IsEnabled = false;
                LoginButton.Content = "جاري التحقق...";

                // محاولة تسجيل الدخول
                var user = await _authService.LoginAsync(UsernameTextBox.Text.Trim(), PasswordBox.Password);

                if (user != null)
                {
                    // تسجيل الدخول نجح
                    var mainWindow = new MainWindow(_serviceProvider);
                    mainWindow.Show();
                    this.Close();
                }
                else
                {
                    ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
                    PasswordBox.Clear();
                    PasswordBox.Focus();
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء تسجيل الدخول: {ex.Message}");
            }
            finally
            {
                // إعادة تفعيل زر تسجيل الدخول
                LoginButton.IsEnabled = true;
                LoginButton.Content = "تسجيل الدخول";
            }
        }

        private void ShowError(string message)
        {
            ErrorMessageTextBlock.Text = message;
            ErrorMessageTextBlock.Visibility = Visibility.Visible;
        }

        protected override void OnClosed(EventArgs e)
        {
            if (_serviceProvider is IDisposable disposable)
            {
                disposable.Dispose();
            }
            base.OnClosed(e);
        }
    }
}
