# برومبت إنشاء تطبيق محاسبة مالية احترافي متكامل

## المطلوب الأساسي
أريد منك إنشاء تطبيق محاسبة مالية احترافي ومتكامل للشركات باستخدام C# مع التقنيات الحديثة. التطبيق يجب أن يكون:

### المتطلبات التقنية الأساسية:
- **اللغة الأساسية**: C# (.NET Framework 4.7.2 أو أحدث للتوافق مع Windows 7+)
- **واجهة المستخدم**: WPF مع Material Design أو DevExpress للحصول على واجهة عصرية وجذابة
- **قاعدة البيانات**: SQL Server LocalDB أو SQLite للسهولة في التوزيع
- **الهيكل المعماري**: MVVM Pattern مع Dependency Injection
- **إدارة البيانات**: Entity Framework Core
- **التقارير**: Crystal Reports أو DevExpress Reports
- **الأمان**: تشفير البيانات الحساسة وحماية قاعدة البيانات

### المتطلبات الوظيفية الشاملة:

#### 1. نظام تسجيل الدخول والأمان
- واجهة تسجيل دخول احترافية وأنيقة
- إدارة المستخدمين والصلاحيات (Admin, Accountant, User)
- تشفير كلمات المرور
- تسجيل عمليات المستخدمين (Audit Trail)
- إمكانية تغيير كلمة المرور
- قفل الحساب بعد محاولات فاشلة

#### 2. إعدادات الشركة
- بيانات الشركة الأساسية (الاسم، العنوان، الهاتف، الإيميل)
- شعار الشركة
- العملة الافتراضية
- السنة المالية
- إعدادات الضرائب
- أرقام التسجيل التجاري والضريبي

#### 3. إدارة الحسابات والدليل المحاسبي
- دليل محاسبي شامل ومرن (Chart of Accounts)
- حسابات رئيسية وفرعية
- أنواع الحسابات (أصول، خصوم، حقوق ملكية، إيرادات، مصروفات)
- ترقيم تلقائي للحسابات
- إمكانية إضافة وتعديل وحذف الحسابات

#### 4. إدارة العملاء والموردين
- قاعدة بيانات شاملة للعملاء والموردين
- معلومات الاتصال والعناوين
- حدود الائتمان للعملاء
- تاريخ التعامل والملاحظات
- تقارير أعمار الديون
- كشوف حسابات العملاء والموردين

#### 5. إدارة الأصناف والمخازن
- قاعدة بيانات شاملة للأصناف
- تصنيف الأصناف (مجموعات وفئات)
- وحدات القياس المختلفة
- أسعار البيع والشراء
- مستويات المخزون (الحد الأدنى والأقصى)
- إدارة متعددة المخازن
- تتبع حركة المخزون (FIFO, LIFO, Average)
- تقارير المخزون والجرد

#### 6. فواتير المشتريات
- إنشاء فواتير مشتريات احترافية
- ربط بالموردين والأصناف
- حساب الضرائب تلقائياً
- خصومات على مستوى الصنف والفاتورة
- طباعة وحفظ الفواتير
- تحويل عروض الأسعار إلى فواتير

#### 7. مردودات المشتريات
- إنشاء مردودات مشتريات
- ربط بالفواتير الأصلية
- تحديث المخزون تلقائياً
- معالجة محاسبية صحيحة

#### 8. فواتير المبيعات
- إنشاء فواتير مبيعات احترافية
- عروض أسعار وفواتير مبدئية
- ربط بالعملاء والأصناف
- حساب العمولات
- طرق دفع متعددة
- طباعة فواتير بتصاميم مختلفة

#### 9. مردودات المبيعات
- إنشاء مردودات مبيعات
- ربط بالفواتير الأصلية
- إشعارات دائنة
- تحديث المخزون والحسابات

#### 10. الخزينة والبنوك
- إدارة الخزائن النقدية
- حسابات البنوك المتعددة
- سندات القبض والصرف
- تحويلات بين الخزائن والبنوك
- مطابقة كشوف البنوك

#### 11. المصروفات والإيرادات
- تسجيل المصروفات المختلفة
- تصنيف المصروفات
- الإيرادات الأخرى
- المصروفات المستحقة والمقدمة
- الإيرادات المستحقة والمقدمة

#### 12. القيود المحاسبية
- إنشاء قيود يدوية
- قيود تلقائية من الفواتير
- مراجعة وتعديل القيود
- ترحيل القيود
- دفتر الأستاذ العام

#### 13. التقارير المالية الشاملة
- **قائمة الدخل** (شهرية، ربع سنوية، سنوية)
- **الميزانية العمومية**
- **قائمة التدفقات النقدية**
- **قائمة التغيرات في حقوق الملكية**
- **ميزان المراجعة**
- **تقارير المبيعات والمشتريات**
- **تقارير المخزون**
- **تقارير العملاء والموردين**
- **تقارير الربحية**

#### 14. حسابات الشركاء
- إدارة حسابات الشركاء
- توزيع الأرباح والخسائر
- حسابات جارية للشركاء
- تقارير حسابات الشركاء

#### 15. الميزات الإضافية المطلوبة
- **النسخ الاحتياطي**: نسخ احتياطي تلقائي ويدوي
- **الاستيراد والتصدير**: Excel, CSV
- **البحث المتقدم**: في جميع الشاشات
- **التنبيهات**: للمخزون المنخفض، الفواتير المستحقة
- **التقويم المالي**: تذكير بالمهام المالية
- **الآلة الحاسبة المدمجة**
- **دعم اللغة العربية**: RTL Support
- **طباعة متقدمة**: معاينة قبل الطباعة، تخصيص التقارير

### المتطلبات التقنية المتقدمة:

#### الأداء والاستقرار:
- تحسين الاستعلامات لقواعد البيانات الكبيرة
- Lazy Loading للبيانات
- Caching للبيانات المستخدمة بكثرة
- معالجة الأخطاء بشكل احترافي
- Logging شامل للعمليات

#### واجهة المستخدم:
- تصميم Material Design أو Fluent Design
- ألوان متناسقة ومريحة للعين
- أيقونات واضحة ومعبرة
- انتقالات سلسة بين الشاشات
- دعم الشاشات عالية الدقة (4K)
- إمكانية تخصيص الواجهة

#### الأمان والحماية:
- تشفير قاعدة البيانات
- حماية من SQL Injection
- تسجيل جميع العمليات
- نسخ احتياطي مشفر
- انتهاء صلاحية الجلسة

### هيكل المشروع المطلوب:
```
AccountingApp/
├── AccountingApp.UI/ (WPF Application)
├── AccountingApp.Core/ (Business Logic)
├── AccountingApp.Data/ (Data Access Layer)
├── AccountingApp.Models/ (Data Models)
├── AccountingApp.Services/ (Business Services)
├── AccountingApp.Reports/ (Reporting)
└── AccountingApp.Tests/ (Unit Tests)
```

### ملاحظات مهمة:
1. التطبيق يجب أن يعمل على Windows 7 SP1 فما أعلى
2. واجهة باللغة العربية مع دعم RTL
3. إمكانية العمل بدون إنترنت (Offline)
4. سهولة التثبيت والاستخدام
5. دليل مستخدم شامل
6. كود نظيف ومنظم مع تعليقات باللغة العربية
7. إمكانية التوسع والتطوير المستقبلي

**المطلوب**: تطبيق شامل ومتكامل يغطي جميع احتياجات المحاسبة المالية للشركات الصغيرة والمتوسطة، مع واجهة مستخدم عصرية وجذابة، وأداء عالي، وأمان قوي.
