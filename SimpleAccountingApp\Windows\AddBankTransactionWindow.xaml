<Window x:Class="SimpleAccountingApp.Windows.AddBankTransactionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة عملية مصرفية" Height="500" Width="450"
        WindowStartupLocation="CenterScreen" FlowDirection="RightToLeft">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Name="TitleTextBlock" Text="إضافة عملية مصرفية" 
                   FontSize="18" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- نموذج البيانات -->
        <StackPanel Grid.Row="1">
            <!-- رقم العملية -->
            <Label Content="رقم العملية:" FontWeight="Bold"/>
            <TextBox Name="TransactionNumberTextBox" Margin="0,0,0,10" IsReadOnly="True"/>

            <!-- تاريخ العملية -->
            <Label Content="تاريخ العملية:" FontWeight="Bold"/>
            <DatePicker Name="TransactionDatePicker" Margin="0,0,0,10"/>

            <!-- نوع العملية -->
            <Label Content="نوع العملية:" FontWeight="Bold"/>
            <TextBox Name="TransactionTypeTextBox" Margin="0,0,0,10" IsReadOnly="True"/>

            <!-- المبلغ -->
            <Label Content="المبلغ:" FontWeight="Bold"/>
            <TextBox Name="AmountTextBox" Margin="0,0,0,10"/>

            <!-- الحساب المستقبل (للتحويل فقط) -->
            <Label Name="ToAccountLabel" Content="الحساب المستقبل:" FontWeight="Bold" Visibility="Collapsed"/>
            <ComboBox Name="ToAccountComboBox" Margin="0,0,0,10" Visibility="Collapsed"/>

            <!-- الوصف -->
            <Label Content="الوصف:" FontWeight="Bold"/>
            <TextBox Name="DescriptionTextBox" Margin="0,0,0,10"/>

            <!-- المرجع -->
            <Label Content="المرجع:" FontWeight="Bold"/>
            <TextBox Name="ReferenceTextBox" Margin="0,0,0,10"/>

            <!-- ملاحظات -->
            <Label Content="ملاحظات:" FontWeight="Bold"/>
            <TextBox Name="NotesTextBox" Height="60" 
                     TextWrapping="Wrap" AcceptsReturn="True" 
                     VerticalScrollBarVisibility="Auto" Margin="0,0,0,10"/>
        </StackPanel>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Name="SaveButton" Content="حفظ" 
                    Width="100" Margin="0,0,10,0" Click="SaveButton_Click"/>
            <Button Name="CancelButton" Content="إلغاء" 
                    Width="100" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
