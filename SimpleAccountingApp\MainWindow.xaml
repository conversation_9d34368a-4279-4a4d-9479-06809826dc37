﻿<Window x:Class="SimpleAccountingApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نظام المحاسبة المالية - تطبيق بسيط"
        Height="600" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">

    <Window.Resources>
        <!-- Button Style for Better Text Clarity -->
        <Style x:Key="ClearButtonStyle" TargetType="Button">
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="BorderBrush" Value="White"/>
            <Setter Property="Padding" Value="20,15"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="8"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            TextBlock.Foreground="White"
                                            TextBlock.FontWeight="Bold"
                                            TextBlock.FontSize="18"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.9"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Menu Button Style -->
        <Style x:Key="MenuButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
            <Setter Property="Padding" Value="15,12"/>
            <Setter Property="Margin" Value="0,3"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="5"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"
                                            TextBlock.Foreground="{TemplateBinding Foreground}"
                                            TextBlock.FontWeight="Bold"
                                            TextBlock.FontSize="18"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E3F2FD"/>
                                <Setter Property="BorderBrush" Value="#2196F3"/>
                                <Setter Property="Foreground" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#BBDEFB"/>
                                <Setter Property="BorderBrush" Value="#1976D2"/>
                                <Setter Property="Foreground" Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="30"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" Padding="20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="💼" FontSize="32" Foreground="White" VerticalAlignment="Center" Margin="0,0,15,0"/>
                <TextBlock Text="نظام المحاسبة المالية"
                          FontSize="24"
                          FontWeight="Bold"
                          Foreground="White"
                          VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Menu -->
            <Border Grid.Column="0" Background="#F5F5F5" Padding="10" Margin="0,0,10,0">
                <StackPanel>
                    <TextBlock Text="📋 القوائم الرئيسية"
                              FontWeight="Bold"
                              FontSize="20"
                              Margin="0,0,0,15"
                              Foreground="Black"
                              HorizontalAlignment="Center"
                              Background="#E3F2FD"
                              Padding="10,8"
                              TextAlignment="Center"/>

                    <Button Content="📊 دليل الحسابات" Style="{StaticResource MenuButtonStyle}" Click="AccountsButton_Click"/>
                    <Button Content="👥 العملاء" Style="{StaticResource MenuButtonStyle}" Click="CustomersButton_Click"/>
                    <Button Content="🏢 الموردين" Style="{StaticResource MenuButtonStyle}" Click="SuppliersButton_Click"/>
                    <Button Content="📦 الأصناف" Style="{StaticResource MenuButtonStyle}" Click="ProductsButton_Click"/>
                    <Button Content="🏪 المخازن" Style="{StaticResource MenuButtonStyle}" Click="WarehousesButton_Click"/>
                    <Button Content="🏦 حسابات البنوك" Style="{StaticResource MenuButtonStyle}" Click="BankAccountsButton_Click"/>
                    <Button Content="💰 المصروفات" Style="{StaticResource MenuButtonStyle}" Click="ExpensesButton_Click"/>
                    <Button Content="👨‍💼 شؤون الموظفين" Style="{StaticResource MenuButtonStyle}" Click="EmployeesButton_Click"/>
                    <Button Content="💵 إدارة الرواتب" Style="{StaticResource MenuButtonStyle}" Click="PayrollManagementButton_Click"/>
                    <Button Content="🧾 فواتير المبيعات" Style="{StaticResource MenuButtonStyle}" Click="SalesButton_Click"/>
                    <Button Content="📋 فواتير المشتريات" Style="{StaticResource MenuButtonStyle}" Click="PurchasesButton_Click"/>
                    <Button Content="📈 التقارير المالية" Style="{StaticResource MenuButtonStyle}" Click="ReportsButton_Click"/>
                    <Button Content="👤 إدارة المستخدمين" Style="{StaticResource MenuButtonStyle}" Click="UsersButton_Click"/>
                    <Button Content="🏛️ الزكاة والضرائب" Style="{StaticResource MenuButtonStyle}" Click="TaxButton_Click"/>
                </StackPanel>
            </Border>

            <!-- Content Area -->
            <Border Grid.Column="1" Background="White" Padding="20" BorderBrush="#DDD" BorderThickness="1">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBlock Text="🎉" FontSize="60" HorizontalAlignment="Center" Margin="0,0,0,20"/>
                    <TextBlock Text="مرحباً بك في نظام المحاسبة المالية!"
                              FontSize="24"
                              FontWeight="Bold"
                              HorizontalAlignment="Center"
                              Foreground="#2196F3"
                              Margin="0,0,0,10"/>
                    <TextBlock Text="اختر من القائمة الجانبية للبدء"
                              FontSize="18"
                              FontWeight="Bold"
                              HorizontalAlignment="Center"
                              Foreground="Black"
                              Margin="0,0,0,30"/>

                    <StackPanel Orientation="Horizontal">
                        <Button Content="➕ إنشاء فاتورة مبيعات"
                               Background="#4CAF50"
                               Style="{StaticResource ClearButtonStyle}"
                               Click="NewSaleButton_Click"/>
                        <Button Content="➕ إنشاء فاتورة مشتريات"
                               Background="#FF9800"
                               Style="{StaticResource ClearButtonStyle}"
                               Click="NewPurchaseButton_Click"/>
                        <Button Content="📊 عرض التقارير"
                               Background="#2196F3"
                               Style="{StaticResource ClearButtonStyle}"
                               Click="ViewReportsButton_Click"/>
                    </StackPanel>
                </StackPanel>
            </Border>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#F0F0F0" Padding="10,5">
            <DockPanel>
                <TextBlock Text="جاهز - نظام المحاسبة المالية"
                          DockPanel.Dock="Right"
                          FontSize="16"
                          FontWeight="Bold"
                          Foreground="Black"/>
                <TextBlock x:Name="DateTimeText"
                          DockPanel.Dock="Left"
                          FontSize="16"
                          FontWeight="Bold"
                          Foreground="Black"/>
            </DockPanel>
        </Border>
    </Grid>
</Window>
