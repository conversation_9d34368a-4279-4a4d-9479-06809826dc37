﻿<Window x:Class="SimpleAccountingApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نظام المحاسبة المالية - تطبيق بسيط"
        Height="600" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="30"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" Padding="20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="💼" FontSize="32" Foreground="White" VerticalAlignment="Center" Margin="0,0,15,0"/>
                <TextBlock Text="نظام المحاسبة المالية"
                          FontSize="24"
                          FontWeight="Bold"
                          Foreground="White"
                          VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Menu -->
            <Border Grid.Column="0" Background="#F5F5F5" Padding="10" Margin="0,0,10,0">
                <StackPanel>
                    <TextBlock Text="القوائم الرئيسية" FontWeight="Bold" FontSize="16" Margin="0,0,0,15"/>

                    <Button Content="📊 دليل الحسابات"
                           Background="Transparent"
                           BorderThickness="0"
                           HorizontalAlignment="Stretch"
                           HorizontalContentAlignment="Right"
                           Padding="10,8"
                           Margin="0,2"
                           Click="AccountsButton_Click"/>

                    <Button Content="👥 العملاء"
                           Background="Transparent"
                           BorderThickness="0"
                           HorizontalAlignment="Stretch"
                           HorizontalContentAlignment="Right"
                           Padding="10,8"
                           Margin="0,2"
                           Click="CustomersButton_Click"/>

                    <Button Content="🏢 الموردين"
                           Background="Transparent"
                           BorderThickness="0"
                           HorizontalAlignment="Stretch"
                           HorizontalContentAlignment="Right"
                           Padding="10,8"
                           Margin="0,2"
                           Click="SuppliersButton_Click"/>

                    <Button Content="📦 الأصناف"
                           Background="Transparent"
                           BorderThickness="0"
                           HorizontalAlignment="Stretch"
                           HorizontalContentAlignment="Right"
                           Padding="10,8"
                           Margin="0,2"
                           Click="ProductsButton_Click"/>

                    <Button Content="🧾 فواتير المبيعات"
                           Background="Transparent"
                           BorderThickness="0"
                           HorizontalAlignment="Stretch"
                           HorizontalContentAlignment="Right"
                           Padding="10,8"
                           Margin="0,2"
                           Click="SalesButton_Click"/>

                    <Button Content="📋 فواتير المشتريات"
                           Background="Transparent"
                           BorderThickness="0"
                           HorizontalAlignment="Stretch"
                           HorizontalContentAlignment="Right"
                           Padding="10,8"
                           Margin="0,2"
                           Click="PurchasesButton_Click"/>

                    <Button Content="📈 التقارير المالية"
                           Background="Transparent"
                           BorderThickness="0"
                           HorizontalAlignment="Stretch"
                           HorizontalContentAlignment="Right"
                           Padding="10,8"
                           Margin="0,2"
                           Click="ReportsButton_Click"/>
                </StackPanel>
            </Border>

            <!-- Content Area -->
            <Border Grid.Column="1" Background="White" Padding="20" BorderBrush="#DDD" BorderThickness="1">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBlock Text="🎉" FontSize="60" HorizontalAlignment="Center" Margin="0,0,0,20"/>
                    <TextBlock Text="مرحباً بك في نظام المحاسبة المالية!"
                              FontSize="20"
                              FontWeight="Bold"
                              HorizontalAlignment="Center"
                              Foreground="#2196F3"
                              Margin="0,0,0,10"/>
                    <TextBlock Text="اختر من القائمة الجانبية للبدء"
                              FontSize="14"
                              HorizontalAlignment="Center"
                              Foreground="#666"
                              Margin="0,0,0,30"/>

                    <StackPanel Orientation="Horizontal">
                        <Button Content="إنشاء فاتورة مبيعات"
                               Background="#4CAF50"
                               Foreground="White"
                               BorderThickness="0"
                               Padding="15,8"
                               Margin="5"
                               Click="NewSaleButton_Click"/>
                        <Button Content="إنشاء فاتورة مشتريات"
                               Background="#FF9800"
                               Foreground="White"
                               BorderThickness="0"
                               Padding="15,8"
                               Margin="5"
                               Click="NewPurchaseButton_Click"/>
                        <Button Content="عرض التقارير"
                               Background="#9C27B0"
                               Foreground="White"
                               BorderThickness="0"
                               Padding="15,8"
                               Margin="5"
                               Click="ViewReportsButton_Click"/>
                    </StackPanel>
                </StackPanel>
            </Border>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#F0F0F0" Padding="10,5">
            <DockPanel>
                <TextBlock Text="جاهز - نظام المحاسبة المالية" DockPanel.Dock="Right"/>
                <TextBlock x:Name="DateTimeText" DockPanel.Dock="Left"/>
            </DockPanel>
        </Border>
    </Grid>
</Window>
