<Window x:Class="SimpleAccountingApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نظام المحاسبة المالية - تطبيق بسيط"
        Height="600" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">

    <Window.Resources>
        <!-- Button Style for Better Text Clarity -->
        <Style x:Key="ClearButtonStyle" TargetType="Button">
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Direction="315" ShadowDepth="2" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Opacity" Value="0.9"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Opacity" Value="0.8"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Menu Button Style -->
        <Style x:Key="MenuButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
            <Setter Property="Padding" Value="15,12"/>
            <Setter Property="Margin" Value="0,2"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E3F2FD"/>
                    <Setter Property="Foreground" Value="#1976D2"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#BBDEFB"/>
                    <Setter Property="Foreground" Value="#0D47A1"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="30"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" Padding="20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="💼" FontSize="32" Foreground="White" VerticalAlignment="Center" Margin="0,0,15,0"/>
                <TextBlock Text="نظام المحاسبة المالية"
                          FontSize="24"
                          FontWeight="Bold"
                          Foreground="White"
                          VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Menu -->
            <Border Grid.Column="0" Background="#F5F5F5" Padding="10" Margin="0,0,10,0">
                <StackPanel>
                    <TextBlock Text="📋 القوائم الرئيسية" FontWeight="Bold" FontSize="18" Margin="0,0,0,15" Foreground="#1976D2" HorizontalAlignment="Center"/>

                    <Button Content="📊 دليل الحسابات" Style="{StaticResource MenuButtonStyle}" Click="AccountsButton_Click"/>
                    <Button Content="👥 العملاء" Style="{StaticResource MenuButtonStyle}" Click="CustomersButton_Click"/>
                    <Button Content="🏢 الموردين" Style="{StaticResource MenuButtonStyle}" Click="SuppliersButton_Click"/>
                    <Button Content="📦 الأصناف" Style="{StaticResource MenuButtonStyle}" Click="ProductsButton_Click"/>
                    <Button Content="🏪 المخازن" Style="{StaticResource MenuButtonStyle}" Click="WarehousesButton_Click"/>
                    <Button Content="🧾 فواتير المبيعات" Style="{StaticResource MenuButtonStyle}" Click="SalesButton_Click"/>
                    <Button Content="📋 فواتير المشتريات" Style="{StaticResource MenuButtonStyle}" Click="PurchasesButton_Click"/>
                    <Button Content="📈 التقارير المالية" Style="{StaticResource MenuButtonStyle}" Click="ReportsButton_Click"/>
                    <Button Content="👤 إدارة المستخدمين" Style="{StaticResource MenuButtonStyle}" Click="UsersButton_Click"/>
                    <Button Content="🏛️ الزكاة والضرائب" Style="{StaticResource MenuButtonStyle}" Click="TaxButton_Click"/>
                </StackPanel>
            </Border>

            <!-- Content Area -->
            <Border Grid.Column="1" Background="White" Padding="20" BorderBrush="#DDD" BorderThickness="1">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBlock Text="🎉" FontSize="60" HorizontalAlignment="Center" Margin="0,0,0,20"/>
                    <TextBlock Text="مرحباً بك في نظام المحاسبة المالية!"
                              FontSize="20"
                              FontWeight="Bold"
                              HorizontalAlignment="Center"
                              Foreground="#2196F3"
                              Margin="0,0,0,10"/>
                    <TextBlock Text="اختر من القائمة الجانبية للبدء"
                              FontSize="14"
                              HorizontalAlignment="Center"
                              Foreground="#666"
                              Margin="0,0,0,30"/>

                    <StackPanel Orientation="Horizontal">
                        <Button Content="➕ إنشاء فاتورة مبيعات"
                               Background="#4CAF50"
                               Style="{StaticResource ClearButtonStyle}"
                               Click="NewSaleButton_Click"/>
                        <Button Content="➕ إنشاء فاتورة مشتريات"
                               Background="#FF9800"
                               Style="{StaticResource ClearButtonStyle}"
                               Click="NewPurchaseButton_Click"/>
                        <Button Content="📊 عرض التقارير"
                               Background="#2196F3"
                               Style="{StaticResource ClearButtonStyle}"
                               Click="ViewReportsButton_Click"/>
                        <Button Content="🔐 تسجيل الدخول"
                               Background="#9C27B0"
                               Style="{StaticResource ClearButtonStyle}"
                               Click="LoginButton_Click"/>
                    </StackPanel>
                </StackPanel>
            </Border>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#F0F0F0" Padding="10,5">
            <DockPanel>
                <TextBlock Text="جاهز - نظام المحاسبة المالية" DockPanel.Dock="Right"/>
                <TextBlock x:Name="DateTimeText" DockPanel.Dock="Left"/>
            </DockPanel>
        </Border>
    </Grid>
</Window>
