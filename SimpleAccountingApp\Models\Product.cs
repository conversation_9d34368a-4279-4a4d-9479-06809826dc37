using System.ComponentModel.DataAnnotations;

namespace SimpleAccountingApp.Models
{
    public class Product
    {
        [Key]
        public int ProductId { get; set; }
        
        [Required]
        [MaxLength(200)]
        public string ProductName { get; set; } = "";
        
        [MaxLength(500)]
        public string Description { get; set; } = "";
        
        public decimal UnitPrice { get; set; }
        public int StockQuantity { get; set; }
        
        [MaxLength(100)]
        public string Category { get; set; } = "";
        
        [MaxLength(50)]
        public string Unit { get; set; } = "";
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public bool IsActive { get; set; } = true;
    }
}
