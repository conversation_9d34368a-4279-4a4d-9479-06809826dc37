using System.ComponentModel.DataAnnotations;

namespace SimpleAccountingApp.Models
{
    public class Product
    {
        [Key]
        public int ProductId { get; set; }

        [MaxLength(50)]
        public string ProductCode { get; set; } = "";

        [Required]
        [MaxLength(200)]
        public string ProductName { get; set; } = "";

        [MaxLength(50)]
        public string Barcode { get; set; } = "";

        [MaxLength(100)]
        public string Category { get; set; } = "";

        [MaxLength(50)]
        public string Unit { get; set; } = "";

        public decimal PurchasePrice { get; set; }
        public decimal SalePrice { get; set; }
        public decimal CurrentStock { get; set; }
        public decimal MinimumStock { get; set; }
        public decimal MaximumStock { get; set; }
        public bool IsActive { get; set; } = true;

        [MaxLength(500)]
        public string Description { get; set; } = "";

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // خصائص محسوبة
        public string IsActiveText => IsActive ? "نشط" : "غير نشط";
        public bool IsLowStock => CurrentStock <= MinimumStock;
        public decimal ProfitMargin => SalePrice - PurchasePrice;
        public decimal ProfitPercentage => PurchasePrice > 0 ? (ProfitMargin / PurchasePrice) * 100 : 0;

        // للتوافق مع الكود الحالي
        public decimal UnitPrice
        {
            get => SalePrice;
            set => SalePrice = value;
        }

        public int StockQuantity
        {
            get => (int)CurrentStock;
            set => CurrentStock = value;
        }
    }
}
