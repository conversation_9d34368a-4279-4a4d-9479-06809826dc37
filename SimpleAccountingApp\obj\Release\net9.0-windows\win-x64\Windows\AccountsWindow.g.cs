﻿#pragma checksum "..\..\..\..\..\Windows\AccountsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7EF955C7BAD1C17A447A2EAEE3B53E76D591F79B"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleAccountingApp.Windows {
    
    
    /// <summary>
    /// AccountsWindow
    /// </summary>
    public partial class AccountsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 43 "..\..\..\..\..\Windows\AccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddAccountButton;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\..\Windows\AccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditAccountButton;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\..\Windows\AccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteAccountButton;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\..\Windows\AccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\..\Windows\AccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TreeView AccountsTreeView;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\..\Windows\AccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AccountDetailsPanel;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\..\Windows\AccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AccountCodeText;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\..\Windows\AccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AccountNameText;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\..\Windows\AccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AccountTypeText;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\..\Windows\AccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ParentAccountText;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\..\..\Windows\AccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OpeningBalanceText;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\..\Windows\AccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentBalanceText;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\..\Windows\AccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock IsActiveText;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\..\Windows\AccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DescriptionText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleAccountingApp;component/windows/accountswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\AccountsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 27 "..\..\..\..\..\Windows\AccountsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.AddAccountButton = ((System.Windows.Controls.Button)(target));
            
            #line 50 "..\..\..\..\..\Windows\AccountsWindow.xaml"
            this.AddAccountButton.Click += new System.Windows.RoutedEventHandler(this.AddAccountButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.EditAccountButton = ((System.Windows.Controls.Button)(target));
            
            #line 60 "..\..\..\..\..\Windows\AccountsWindow.xaml"
            this.EditAccountButton.Click += new System.Windows.RoutedEventHandler(this.EditAccountButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.DeleteAccountButton = ((System.Windows.Controls.Button)(target));
            
            #line 70 "..\..\..\..\..\Windows\AccountsWindow.xaml"
            this.DeleteAccountButton.Click += new System.Windows.RoutedEventHandler(this.DeleteAccountButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 79 "..\..\..\..\..\Windows\AccountsWindow.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.AccountsTreeView = ((System.Windows.Controls.TreeView)(target));
            
            #line 95 "..\..\..\..\..\Windows\AccountsWindow.xaml"
            this.AccountsTreeView.SelectedItemChanged += new System.Windows.RoutedPropertyChangedEventHandler<object>(this.AccountsTreeView_SelectedItemChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.AccountDetailsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 8:
            this.AccountCodeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.AccountNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.AccountTypeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.ParentAccountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.OpeningBalanceText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.CurrentBalanceText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.IsActiveText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.DescriptionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

