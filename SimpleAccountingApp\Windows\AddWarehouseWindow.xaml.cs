using System.Windows;
using System.Windows.Controls;

namespace SimpleAccountingApp.Windows
{
    public partial class AddWarehouseWindow : Window
    {
        private Warehouse? _editingWarehouse;
        private bool _isEditMode;

        public AddWarehouseWindow()
        {
            InitializeComponent();
            _isEditMode = false;
            GenerateWarehouseCode();
            InitializeDefaults();
        }

        public AddWarehouseWindow(Warehouse warehouseToEdit)
        {
            InitializeComponent();
            _editingWarehouse = warehouseToEdit;
            _isEditMode = true;
            
            WindowTitle.Text = "تعديل المخزن";
            this.Title = "تعديل المخزن - نظام المحاسبة المالية";
            
            InitializeDefaults();
            LoadWarehouseData();
        }

        private void InitializeDefaults()
        {
            TemperatureControlComboBox.SelectedIndex = 0;
            SecurityLevelComboBox.SelectedIndex = 0;
        }

        private void GenerateWarehouseCode()
        {
            Random random = new Random();
            WarehouseCodeTextBox.Text = "WH" + (random.Next(100, 999)).ToString();
        }

        private void LoadWarehouseData()
        {
            if (_editingWarehouse != null)
            {
                WarehouseCodeTextBox.Text = _editingWarehouse.WarehouseCode;
                WarehouseNameTextBox.Text = _editingWarehouse.WarehouseName;
                LocationTextBox.Text = _editingWarehouse.Location;
                ManagerTextBox.Text = _editingWarehouse.Manager;
                PhoneTextBox.Text = _editingWarehouse.Phone;
                AreaTextBox.Text = _editingWarehouse.Area.ToString();
                DescriptionTextBox.Text = _editingWarehouse.Description;
                IsActiveCheckBox.IsChecked = _editingWarehouse.IsActive;
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                if (_isEditMode)
                {
                    UpdateWarehouse();
                }
                else
                {
                    CreateNewWarehouse();
                }
                
                this.DialogResult = true;
                this.Close();
            }
        }

        private bool ValidateInput()
        {
            // التحقق من رقم المخزن
            if (string.IsNullOrWhiteSpace(WarehouseCodeTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم المخزن", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                WarehouseCodeTextBox.Focus();
                return false;
            }

            // التحقق من اسم المخزن
            if (string.IsNullOrWhiteSpace(WarehouseNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المخزن", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                WarehouseNameTextBox.Focus();
                return false;
            }

            // التحقق من الموقع
            if (string.IsNullOrWhiteSpace(LocationTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال موقع المخزن", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                LocationTextBox.Focus();
                return false;
            }

            // التحقق من المساحة
            if (!decimal.TryParse(AreaTextBox.Text, out decimal area) || area < 0)
            {
                MessageBox.Show("يرجى إدخال مساحة صحيحة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                AreaTextBox.Focus();
                return false;
            }

            return true;
        }

        private void CreateNewWarehouse()
        {
            var newWarehouse = new Warehouse
            {
                WarehouseCode = WarehouseCodeTextBox.Text.Trim(),
                WarehouseName = WarehouseNameTextBox.Text.Trim(),
                Location = LocationTextBox.Text.Trim(),
                Manager = ManagerTextBox.Text.Trim(),
                Phone = PhoneTextBox.Text.Trim(),
                Area = decimal.Parse(AreaTextBox.Text),
                Description = DescriptionTextBox.Text.Trim(),
                IsActive = IsActiveCheckBox.IsChecked ?? true,
                ProductCount = 0,
                TotalValue = 0
            };

            // في التطبيق الحقيقي، سيتم حفظ المخزن في قاعدة البيانات
            // TODO: حفظ في قاعدة البيانات
        }

        private void UpdateWarehouse()
        {
            if (_editingWarehouse != null)
            {
                _editingWarehouse.WarehouseCode = WarehouseCodeTextBox.Text.Trim();
                _editingWarehouse.WarehouseName = WarehouseNameTextBox.Text.Trim();
                _editingWarehouse.Location = LocationTextBox.Text.Trim();
                _editingWarehouse.Manager = ManagerTextBox.Text.Trim();
                _editingWarehouse.Phone = PhoneTextBox.Text.Trim();
                _editingWarehouse.Area = decimal.Parse(AreaTextBox.Text);
                _editingWarehouse.Description = DescriptionTextBox.Text.Trim();
                _editingWarehouse.IsActive = IsActiveCheckBox.IsChecked ?? true;

                // في التطبيق الحقيقي، سيتم تحديث المخزن في قاعدة البيانات
                // TODO: تحديث في قاعدة البيانات
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            this.DialogResult = false;
            this.Close();
        }
    }
}
