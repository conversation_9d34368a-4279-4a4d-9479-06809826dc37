using System.Windows;
using System.Windows.Controls;

namespace SimpleAccountingApp.Windows
{
    public partial class CreateUserWindow : Window
    {
        public CreateUserWindow()
        {
            InitializeComponent();
            SetDefaultValues();
        }

        private void SetDefaultValues()
        {
            UserRoleComboBox.SelectedIndex = 2; // مستخدم عادي
            DepartmentComboBox.SelectedIndex = 0; // المحاسبة
            
            // صلاحيات افتراضية
            CanViewReportsCheckBox.IsChecked = true;
            CanCreateInvoicesCheckBox.IsChecked = true;
        }

        private void CreateButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                CreateUser();
                this.DialogResult = true;
                this.Close();
            }
        }

        private bool ValidateInput()
        {
            // التحقق من الاسم الكامل
            if (string.IsNullOrWhiteSpace(FullNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم الكامل", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                FullNameTextBox.Focus();
                return false;
            }

            // التحقق من اسم المستخدم
            if (string.IsNullOrWhiteSpace(UsernameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                UsernameTextBox.Focus();
                return false;
            }

            // التحقق من كلمة المرور
            if (string.IsNullOrWhiteSpace(PasswordBox.Password))
            {
                MessageBox.Show("يرجى إدخال كلمة المرور", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                PasswordBox.Focus();
                return false;
            }

            // التحقق من تطابق كلمة المرور
            if (PasswordBox.Password != ConfirmPasswordBox.Password)
            {
                MessageBox.Show("كلمة المرور وتأكيد كلمة المرور غير متطابقتين", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                ConfirmPasswordBox.Focus();
                return false;
            }

            // التحقق من قوة كلمة المرور
            if (PasswordBox.Password.Length < 6)
            {
                MessageBox.Show("كلمة المرور يجب أن تكون 6 أحرف على الأقل", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                PasswordBox.Focus();
                return false;
            }

            // التحقق من دور المستخدم
            if (UserRoleComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار دور المستخدم", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                UserRoleComboBox.Focus();
                return false;
            }

            return true;
        }

        private void CreateUser()
        {
            var newUser = new User
            {
                FullName = FullNameTextBox.Text.Trim(),
                Username = UsernameTextBox.Text.Trim(),
                Email = EmailTextBox.Text.Trim(),
                Password = PasswordBox.Password, // في التطبيق الحقيقي، يجب تشفير كلمة المرور
                Role = ((ComboBoxItem)UserRoleComboBox.SelectedItem).Content.ToString()!,
                Department = DepartmentComboBox.SelectedItem != null ? 
                    ((ComboBoxItem)DepartmentComboBox.SelectedItem).Content.ToString()! : "",
                Phone = PhoneTextBox.Text.Trim(),
                CreatedDate = DateTime.Now,
                IsActive = true,
                Permissions = new UserPermissions
                {
                    CanViewReports = CanViewReportsCheckBox.IsChecked ?? false,
                    CanCreateInvoices = CanCreateInvoicesCheckBox.IsChecked ?? false,
                    CanManageUsers = CanManageUsersCheckBox.IsChecked ?? false,
                    CanManageProducts = CanManageProductsCheckBox.IsChecked ?? false,
                    CanAccessTaxSystem = CanAccessTaxSystemCheckBox.IsChecked ?? false
                }
            };

            // في التطبيق الحقيقي، سيتم حفظ المستخدم في قاعدة البيانات
            // TODO: حفظ المستخدم في قاعدة البيانات
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            this.DialogResult = false;
            this.Close();
        }
    }

    // نماذج البيانات للمستخدمين
    public class User
    {
        public int UserId { get; set; }
        public string FullName { get; set; } = "";
        public string Username { get; set; } = "";
        public string Email { get; set; } = "";
        public string Password { get; set; } = "";
        public string Role { get; set; } = "";
        public string Department { get; set; } = "";
        public string Phone { get; set; } = "";
        public DateTime CreatedDate { get; set; }
        public DateTime LastLoginDate { get; set; }
        public bool IsActive { get; set; } = true;
        public string IsActiveText { get; set; } = "";
        public UserPermissions Permissions { get; set; } = new UserPermissions();
    }

    public class UserPermissions
    {
        public bool CanViewReports { get; set; }
        public bool CanCreateInvoices { get; set; }
        public bool CanManageUsers { get; set; }
        public bool CanManageProducts { get; set; }
        public bool CanAccessTaxSystem { get; set; }
    }
}
