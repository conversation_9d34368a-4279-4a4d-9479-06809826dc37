using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace SimpleAccountingApp.Windows
{
    public partial class StockMovementWindow : Window
    {
        private ObservableCollection<StockMovement> stockMovements;
        private List<Warehouse> warehouses;

        public StockMovementWindow()
        {
            InitializeComponent();
            LoadData();
        }

        private void LoadData()
        {
            // تحميل المخازن
            warehouses = new List<Warehouse>
            {
                new Warehouse { WarehouseCode = "ALL", WarehouseName = "جميع المخازن" },
                new Warehouse { WarehouseCode = "WH001", WarehouseName = "المخزن الرئيسي" },
                new Warehouse { WarehouseCode = "WH002", WarehouseName = "مخزن الفرع الشرقي" },
                new Warehouse { WarehouseCode = "WH003", WarehouseName = "مخزن الفرع الغربي" }
            };
            WarehouseComboBox.ItemsSource = warehouses;
            WarehouseComboBox.SelectedIndex = 0;

            // تحديد التواريخ الافتراضية
            FromDatePicker.SelectedDate = DateTime.Now.AddDays(-30);
            ToDatePicker.SelectedDate = DateTime.Now;
            MovementTypeComboBox.SelectedIndex = 0;

            LoadStockMovements();
        }

        private void LoadStockMovements()
        {
            stockMovements = new ObservableCollection<StockMovement>
            {
                new StockMovement
                {
                    MovementDate = DateTime.Now.AddDays(-5),
                    ReferenceNumber = "IN001",
                    MovementType = "إدخال",
                    WarehouseName = "المخزن الرئيسي",
                    ProductCode = "P001",
                    ProductName = "لابتوب ديل إنسبايرون",
                    Quantity = 10,
                    UnitPrice = 2500,
                    TotalValue = 25000,
                    UserName = "أحمد محمد",
                    Notes = "شراء جديد من المورد"
                },
                new StockMovement
                {
                    MovementDate = DateTime.Now.AddDays(-4),
                    ReferenceNumber = "OUT001",
                    MovementType = "إخراج",
                    WarehouseName = "المخزن الرئيسي",
                    ProductCode = "P001",
                    ProductName = "لابتوب ديل إنسبايرون",
                    Quantity = -3,
                    UnitPrice = 2500,
                    TotalValue = -7500,
                    UserName = "فاطمة علي",
                    Notes = "بيع للعميل"
                },
                new StockMovement
                {
                    MovementDate = DateTime.Now.AddDays(-3),
                    ReferenceNumber = "TR001",
                    MovementType = "تحويل",
                    WarehouseName = "مخزن الفرع الشرقي",
                    ProductCode = "P002",
                    ProductName = "طابعة HP ليزر",
                    Quantity = 5,
                    UnitPrice = 800,
                    TotalValue = 4000,
                    UserName = "محمد سالم",
                    Notes = "تحويل من المخزن الرئيسي"
                },
                new StockMovement
                {
                    MovementDate = DateTime.Now.AddDays(-2),
                    ReferenceNumber = "ADJ001",
                    MovementType = "تسوية",
                    WarehouseName = "المخزن الرئيسي",
                    ProductCode = "P003",
                    ProductName = "ورق A4",
                    Quantity = -2,
                    UnitPrice = 25,
                    TotalValue = -50,
                    UserName = "سارة أحمد",
                    Notes = "تسوية جرد - نقص في المخزون"
                },
                new StockMovement
                {
                    MovementDate = DateTime.Now.AddDays(-1),
                    ReferenceNumber = "IN002",
                    MovementType = "إدخال",
                    WarehouseName = "مخزن الفرع الغربي",
                    ProductCode = "P004",
                    ProductName = "ماوس لاسلكي",
                    Quantity = 20,
                    UnitPrice = 45,
                    TotalValue = 900,
                    UserName = "خالد عبدالله",
                    Notes = "شراء محلي"
                },
                new StockMovement
                {
                    MovementDate = DateTime.Now,
                    ReferenceNumber = "OUT002",
                    MovementType = "إخراج",
                    WarehouseName = "مخزن الفرع الشرقي",
                    ProductCode = "P005",
                    ProductName = "كيبورد ميكانيكي",
                    Quantity = -1,
                    UnitPrice = 120,
                    TotalValue = -120,
                    UserName = "نورا سعد",
                    Notes = "بيع مباشر"
                }
            };

            StockMovementDataGrid.ItemsSource = stockMovements;
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            FilterMovements();
        }

        private void FilterMovements()
        {
            var filteredMovements = stockMovements.AsEnumerable();

            // فلترة بالتاريخ
            if (FromDatePicker.SelectedDate.HasValue)
            {
                filteredMovements = filteredMovements.Where(m => m.MovementDate.Date >= FromDatePicker.SelectedDate.Value.Date);
            }

            if (ToDatePicker.SelectedDate.HasValue)
            {
                filteredMovements = filteredMovements.Where(m => m.MovementDate.Date <= ToDatePicker.SelectedDate.Value.Date);
            }

            // فلترة بنوع الحركة
            if (MovementTypeComboBox.SelectedItem is ComboBoxItem selectedType && selectedType.Content.ToString() != "الكل")
            {
                filteredMovements = filteredMovements.Where(m => m.MovementType == selectedType.Content.ToString());
            }

            // فلترة بالمخزن
            if (WarehouseComboBox.SelectedItem is Warehouse selectedWarehouse && selectedWarehouse.WarehouseCode != "ALL")
            {
                filteredMovements = filteredMovements.Where(m => m.WarehouseName == selectedWarehouse.WarehouseName);
            }

            StockMovementDataGrid.ItemsSource = filteredMovements.ToList();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    // نموذج بيانات حركة المخزون
    public class StockMovement
    {
        public DateTime MovementDate { get; set; }
        public string ReferenceNumber { get; set; } = "";
        public string MovementType { get; set; } = "";
        public string WarehouseName { get; set; } = "";
        public string ProductCode { get; set; } = "";
        public string ProductName { get; set; } = "";
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalValue { get; set; }
        public string UserName { get; set; } = "";
        public string Notes { get; set; } = "";
    }
}
