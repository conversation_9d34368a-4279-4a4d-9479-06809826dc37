using System.Windows;

namespace SimpleAccountingApp.Windows
{
    public partial class IncomeStatementWindow : Window
    {
        public IncomeStatementWindow()
        {
            InitializeComponent();
            UpdatePeriodText();
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            UpdatePeriodText();
            MessageBox.Show("تم تحديث التقرير بنجاح!", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void UpdatePeriodText()
        {
            if (FromDatePicker.SelectedDate.HasValue && ToDatePicker.SelectedDate.HasValue)
            {
                PeriodText.Text = $"للفترة من {FromDatePicker.SelectedDate.Value:yyyy/MM/dd} إلى {ToDatePicker.SelectedDate.Value:yyyy/MM/dd}";
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("وظيفة الطباعة قيد التطوير", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("وظيفة التصدير قيد التطوير", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
