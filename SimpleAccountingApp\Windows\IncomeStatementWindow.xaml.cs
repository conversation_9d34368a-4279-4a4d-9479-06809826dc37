using System.Windows;
using SimpleAccountingApp.Helpers;

namespace SimpleAccountingApp.Windows
{
    public partial class IncomeStatementWindow : Window
    {
        public IncomeStatementWindow()
        {
            InitializeComponent();
            UpdatePeriodText();
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            UpdatePeriodText();
            MessageBox.Show("تم تحديث التقرير بنجاح!", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void UpdatePeriodText()
        {
            if (FromDatePicker.SelectedDate.HasValue && ToDatePicker.SelectedDate.HasValue)
            {
                PeriodText.Text = $"للفترة من {FromDatePicker.SelectedDate.Value:yyyy/MM/dd} إلى {ToDatePicker.SelectedDate.Value:yyyy/MM/dd}";
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            var reportContent = @"
قائمة الدخل
==========

الإيرادات:
---------
إيرادات المبيعات: 125,000.00 ريال
إيرادات أخرى: 5,000.00 ريال
إجمالي الإيرادات: 130,000.00 ريال

المصروفات:
----------
تكلفة البضاعة المباعة: 75,000.00 ريال
مصروفات التشغيل: 25,000.00 ريال
مصروفات إدارية: 15,000.00 ريال
إجمالي المصروفات: 115,000.00 ريال

صافي الربح: 15,000.00 ريال
";
            ReportHelper.PrintText(reportContent, "قائمة الدخل");
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير تصدير قائمة الدخل قريباً", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
