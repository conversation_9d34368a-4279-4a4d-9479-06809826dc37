using System.Windows;
using SimpleAccountingApp.Helpers;

namespace SimpleAccountingApp.Windows
{
    public partial class IncomeStatementWindow : Window
    {
        public IncomeStatementWindow()
        {
            InitializeComponent();
            UpdatePeriodText();
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            UpdatePeriodText();
            MessageBox.Show("تم تحديث التقرير بنجاح!", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void UpdatePeriodText()
        {
            if (FromDatePicker.SelectedDate.HasValue && ToDatePicker.SelectedDate.HasValue)
            {
                PeriodText.Text = $"للفترة من {FromDatePicker.SelectedDate.Value:yyyy/MM/dd} إلى {ToDatePicker.SelectedDate.Value:yyyy/MM/dd}";
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            ReportHelper.PrintDataGrid(IncomeStatementDataGrid, "قائمة الدخل");
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            ReportHelper.ExportDataGridToExcel(IncomeStatementDataGrid, "قائمة الدخل");
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
