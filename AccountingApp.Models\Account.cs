using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingApp.Models
{
    /// <summary>
    /// نموذج الحساب - Account Model
    /// </summary>
    public class Account
    {
        [Key]
        public int AccountId { get; set; }

        [Required]
        [StringLength(20)]
        public string AccountCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string AccountName { get; set; } = string.Empty;

        [StringLength(100)]
        public string AccountNameEnglish { get; set; } = string.Empty;

        public AccountType AccountType { get; set; }

        public int? ParentAccountId { get; set; }

        [ForeignKey("ParentAccountId")]
        public virtual Account? ParentAccount { get; set; }

        public virtual ICollection<Account> SubAccounts { get; set; } = new List<Account>();

        public int Level { get; set; } = 1;

        public bool IsActive { get; set; } = true;

        public bool IsMainAccount { get; set; } = false;

        [Column(TypeName = "decimal(18,2)")]
        public decimal OpeningBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentBalance { get; set; } = 0;

        public BalanceType BalanceType { get; set; } = BalanceType.Debit;

        [StringLength(500)]
        public string? Description { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? ModifiedDate { get; set; }

        public string? CreatedBy { get; set; }

        public string? ModifiedBy { get; set; }

        // Navigation Properties
        public virtual ICollection<JournalEntryDetail> JournalEntryDetails { get; set; } = new List<JournalEntryDetail>();
    }

    /// <summary>
    /// أنواع الحسابات - Account Types
    /// </summary>
    public enum AccountType
    {
        Assets = 1,         // الأصول
        Liabilities = 2,    // الخصوم
        Equity = 3,         // حقوق الملكية
        Revenue = 4,        // الإيرادات
        Expenses = 5,       // المصروفات
        Cost = 6           // تكلفة البضاعة المباعة
    }

    /// <summary>
    /// نوع الرصيد - Balance Type
    /// </summary>
    public enum BalanceType
    {
        Debit = 1,  // مدين
        Credit = 2  // دائن
    }
}
