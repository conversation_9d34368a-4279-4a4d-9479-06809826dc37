<Window x:Class="SimpleAccountingApp.Windows.SalesSummaryWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="ملخص المبيعات - نظام المحاسبة المالية" 
        Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#4CAF50" Padding="15">
            <DockPanel>
                <Button DockPanel.Dock="Left" 
                       Content="✖ إغلاق" 
                       Background="#388E3C" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,5" 
                       Click="CloseButton_Click"/>
                
                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                    <TextBlock Text="📊" FontSize="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="ملخص المبيعات" 
                              FontSize="20" 
                              FontWeight="Bold" 
                              Foreground="White" 
                              VerticalAlignment="Center"/>
                </StackPanel>
            </DockPanel>
        </Border>
        
        <!-- Period Selection -->
        <Border Grid.Row="1" Background="#F5F5F5" Padding="15" BorderBrush="#DDD" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="من تاريخ:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <DatePicker x:Name="FromDatePicker" Grid.Column="1" Height="35"/>
                
                <TextBlock Grid.Column="2" Text="إلى تاريخ:" VerticalAlignment="Center" Margin="15,0,10,0"/>
                <DatePicker x:Name="ToDatePicker" Grid.Column="3" Height="35"/>
                
                <Button x:Name="RefreshButton" 
                       Grid.Column="4" 
                       Content="🔄 تحديث" 
                       Background="#2196F3" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="15,0,0,0"
                       Click="RefreshButton_Click"/>
            </Grid>
        </Border>
        
        <!-- Summary Cards -->
        <Grid Grid.Row="2" Margin="15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Total Sales -->
            <Border Grid.Column="0" Background="#E8F5E8" Padding="15" Margin="5" CornerRadius="5">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="💰" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="20,381.25" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Green"/>
                    <TextBlock Text="إجمالي المبيعات" FontSize="12" HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>
            
            <!-- Number of Invoices -->
            <Border Grid.Column="1" Background="#E3F2FD" Padding="15" Margin="5" CornerRadius="5">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="🧾" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="4" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Blue"/>
                    <TextBlock Text="عدد الفواتير" FontSize="12" HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>
            
            <!-- Average Invoice -->
            <Border Grid.Column="2" Background="#FFF3E0" Padding="15" Margin="5" CornerRadius="5">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="📈" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="5,095.31" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Orange"/>
                    <TextBlock Text="متوسط الفاتورة" FontSize="12" HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>
            
            <!-- Outstanding Amount -->
            <Border Grid.Column="3" Background="#FFEBEE" Padding="15" Margin="5" CornerRadius="5">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="⏰" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="10,880.00" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Red"/>
                    <TextBlock Text="المبالغ المستحقة" FontSize="12" HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>
        </Grid>
        
        <!-- Sales Details DataGrid -->
        <Grid Grid.Row="3" Margin="15,0,15,15">
            <DataGrid x:Name="SalesDataGrid"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     AlternatingRowBackground="#F9F9F9">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم الفاتورة" 
                                       Binding="{Binding InvoiceNumber}" 
                                       Width="120"/>
                    
                    <DataGridTextColumn Header="التاريخ" 
                                       Binding="{Binding InvoiceDate, StringFormat=yyyy/MM/dd}" 
                                       Width="100"/>
                    
                    <DataGridTextColumn Header="العميل" 
                                       Binding="{Binding CustomerName}" 
                                       Width="200"/>
                    
                    <DataGridTextColumn Header="المبلغ الفرعي" 
                                       Binding="{Binding SubTotal, StringFormat=N2}" 
                                       Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="الخصم" 
                                       Binding="{Binding DiscountAmount, StringFormat=N2}" 
                                       Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="الضريبة" 
                                       Binding="{Binding TaxAmount, StringFormat=N2}" 
                                       Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="الإجمالي" 
                                       Binding="{Binding TotalAmount, StringFormat=N2}" 
                                       Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="Foreground" Value="Green"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="المدفوع" 
                                       Binding="{Binding PaidAmount, StringFormat=N2}" 
                                       Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="المتبقي" 
                                       Binding="{Binding RemainingAmount, StringFormat=N2}" 
                                       Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding RemainingAmount}" Value="0">
                                        <Setter Property="Foreground" Value="Green"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>
    </Grid>
</Window>
