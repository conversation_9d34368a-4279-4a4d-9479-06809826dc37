using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using SimpleAccountingApp.Data;
using SimpleAccountingApp.Models;
using Microsoft.EntityFrameworkCore;

namespace SimpleAccountingApp.Windows
{
    public partial class SuppliersWindow : Window
    {
        private ObservableCollection<Supplier> suppliers = new();
        private AccountingDbContext _context;

        public SuppliersWindow()
        {
            InitializeComponent();
            _context = new AccountingDbContext();
            LoadSuppliers();
        }

        private async void LoadSuppliers()
        {
            try
            {
                await _context.InitializeDatabaseAsync();
                var suppliersList = await _context.Suppliers
                    .Where(s => s.IsActive)
                    .OrderBy(s => s.SupplierName)
                    .ToListAsync();

                suppliers.Clear();
                foreach (var supplier in suppliersList)
                {
                    suppliers.Add(supplier);
                }

                SuppliersDataGrid.ItemsSource = suppliers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الموردين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }


        private void SuppliersDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            bool hasSelection = SuppliersDataGrid.SelectedItem != null;
            EditSupplierButton.IsEnabled = hasSelection;
            DeleteSupplierButton.IsEnabled = hasSelection;
        }

        private async void AddSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            var addSupplierWindow = new AddSupplierWindow();
            if (addSupplierWindow.ShowDialog() == true)
            {
                // الحصول على المورد الجديد من النافذة
                var newSupplier = addSupplierWindow.NewSupplier;
                if (newSupplier != null)
                {
                    try
                    {
                        // حفظ المورد في قاعدة البيانات
                        _context.Suppliers.Add(newSupplier);
                        await _context.SaveChangesAsync();

                        // إضافة المورد الجديد إلى القائمة
                        suppliers.Add(newSupplier);

                        // تحديث عرض البيانات
                        RefreshSuppliersGrid();

                        MessageBox.Show("تم إضافة المورد بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حفظ المورد: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void RefreshSuppliersGrid()
        {
            SuppliersDataGrid.ItemsSource = null;
            SuppliersDataGrid.ItemsSource = suppliers;
        }

        private async void EditSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            if (SuppliersDataGrid.SelectedItem is Supplier selectedSupplier)
            {
                var editSupplierWindow = new AddSupplierWindow(selectedSupplier);
                if (editSupplierWindow.ShowDialog() == true)
                {
                    try
                    {
                        // تحديث المورد في قاعدة البيانات
                        _context.Suppliers.Update(selectedSupplier);
                        await _context.SaveChangesAsync();

                        // تحديث عرض البيانات
                        RefreshSuppliersGrid();

                        MessageBox.Show("تم تعديل المورد بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في تحديث المورد: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private async void DeleteSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            if (SuppliersDataGrid.SelectedItem is Supplier selectedSupplier)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف المورد '{selectedSupplier.SupplierName}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // حذف المورد من قاعدة البيانات
                        _context.Suppliers.Remove(selectedSupplier);
                        await _context.SaveChangesAsync();

                        // حذف المورد من القائمة
                        suppliers.Remove(selectedSupplier);

                        // تحديث عرض البيانات
                        RefreshSuppliersGrid();

                        MessageBox.Show("تم حذف المورد بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف المورد: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            string searchText = SearchTextBox.Text.ToLower();
            if (string.IsNullOrWhiteSpace(searchText))
            {
                SuppliersDataGrid.ItemsSource = suppliers;
            }
            else
            {
                var filteredSuppliers = suppliers.Where(s => 
                    s.SupplierName.ToLower().Contains(searchText) ||
                    s.SupplierCode.ToLower().Contains(searchText) ||
                    s.Phone.Contains(searchText) ||
                    s.Mobile.Contains(searchText) ||
                    s.Email.ToLower().Contains(searchText)
                ).ToList();
                
                SuppliersDataGrid.ItemsSource = filteredSuppliers;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

}
