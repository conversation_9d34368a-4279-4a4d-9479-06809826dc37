using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using SimpleAccountingApp.Data;
using SimpleAccountingApp.Models;
using Microsoft.EntityFrameworkCore;

namespace SimpleAccountingApp.Windows
{
    public partial class SuppliersWindow : Window
    {
        private ObservableCollection<Supplier> suppliers = new();
        private AccountingDbContext _context;

        public SuppliersWindow()
        {
            InitializeComponent();
            _context = new AccountingDbContext();
            LoadSuppliers();
        }

        private async void LoadSuppliers()
        {
            try
            {
                await _context.InitializeDatabaseAsync();
                var suppliersList = await _context.Suppliers
                    .Where(s => s.IsActive)
                    .OrderBy(s => s.SupplierName)
                    .ToListAsync();

                suppliers.Clear();
                foreach (var supplier in suppliersList)
                {
                    suppliers.Add(supplier);
                }

                SuppliersDataGrid.ItemsSource = suppliers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الموردين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
                new Supplier
                {
                    SupplierCode = "S002",
                    SupplierName = "مؤسسة الخليج للمواد الغذائية",
                    SupplierType = "مؤسسة",
                    Phone = "012-3456789",
                    Mobile = "0503456789",
                    Email = "<EMAIL>",
                    Address = "جدة، حي الروضة",
                    City = "جدة",
                    Country = "السعودية",
                    TaxNumber = "*********",
                    CurrentBalance = 15000,
                    PaymentTerms = 15,
                    IsActive = true,
                    Notes = "مورد مواد غذائية"
                },
                new Supplier
                {
                    SupplierCode = "S003",
                    SupplierName = "محمد أحمد للتجارة",
                    SupplierType = "فرد",
                    Phone = "013-4567890",
                    Mobile = "0504567890",
                    Email = "<EMAIL>",
                    Address = "الدمام، حي الفيصلية",
                    City = "الدمام",
                    Country = "السعودية",
                    TaxNumber = "",
                    CurrentBalance = 8000,
                    PaymentTerms = 7,
                    IsActive = true,
                    Notes = "مورد قطع غيار"
                },
                new Supplier
                {
                    SupplierCode = "S004",
                    SupplierName = "شركة البناء الحديث",
                    SupplierType = "شركة",
                    Phone = "014-5678901",
                    Mobile = "0505678901",
                    Email = "<EMAIL>",
                    Address = "الطائف، حي الشهداء",
                    City = "الطائف",
                    Country = "السعودية",
                    TaxNumber = "*********",
                    CurrentBalance = 50000,
                    PaymentTerms = 45,
                    IsActive = false,
                    Notes = "مورد مواد بناء - متوقف"
                }
            };

            SuppliersDataGrid.ItemsSource = suppliers;
        }

        private void SuppliersDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            bool hasSelection = SuppliersDataGrid.SelectedItem != null;
            EditSupplierButton.IsEnabled = hasSelection;
            DeleteSupplierButton.IsEnabled = hasSelection;
        }

        private void AddSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            var addSupplierWindow = new AddSupplierWindow();
            if (addSupplierWindow.ShowDialog() == true)
            {
                // الحصول على المورد الجديد من النافذة
                var newSupplier = addSupplierWindow.NewSupplier;
                if (newSupplier != null)
                {
                    // إضافة المورد الجديد إلى القائمة
                    suppliers.Add(newSupplier);

                    // تحديث عرض البيانات
                    RefreshSuppliersGrid();

                    MessageBox.Show("تم إضافة المورد بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void RefreshSuppliersGrid()
        {
            SuppliersDataGrid.ItemsSource = null;
            SuppliersDataGrid.ItemsSource = suppliers;
        }

        private void EditSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            if (SuppliersDataGrid.SelectedItem is Supplier selectedSupplier)
            {
                var editSupplierWindow = new AddSupplierWindow(selectedSupplier);
                if (editSupplierWindow.ShowDialog() == true)
                {
                    // تحديث عرض البيانات
                    RefreshSuppliersGrid();

                    MessageBox.Show("تم تعديل المورد بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void DeleteSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            if (SuppliersDataGrid.SelectedItem is Supplier selectedSupplier)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف المورد '{selectedSupplier.SupplierName}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    suppliers.Remove(selectedSupplier);

                    // تحديث عرض البيانات
                    RefreshSuppliersGrid();

                    MessageBox.Show("تم حذف المورد بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            string searchText = SearchTextBox.Text.ToLower();
            if (string.IsNullOrWhiteSpace(searchText))
            {
                SuppliersDataGrid.ItemsSource = suppliers;
            }
            else
            {
                var filteredSuppliers = suppliers.Where(s => 
                    s.SupplierName.ToLower().Contains(searchText) ||
                    s.SupplierCode.ToLower().Contains(searchText) ||
                    s.Phone.Contains(searchText) ||
                    s.Mobile.Contains(searchText) ||
                    s.Email.ToLower().Contains(searchText)
                ).ToList();
                
                SuppliersDataGrid.ItemsSource = filteredSuppliers;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    // نموذج بيانات المورد
    public class Supplier
    {
        public string SupplierCode { get; set; } = "";
        public string SupplierName { get; set; } = "";
        public string SupplierType { get; set; } = "";
        public string Address { get; set; } = "";
        public string City { get; set; } = "";
        public string Country { get; set; } = "السعودية";
        public string Phone { get; set; } = "";
        public string Mobile { get; set; } = "";
        public string Email { get; set; } = "";
        public string TaxNumber { get; set; } = "";
        public decimal CurrentBalance { get; set; }
        public int PaymentTerms { get; set; } = 30;
        public bool IsActive { get; set; } = true;
        public string Notes { get; set; } = "";
        
        public string IsActiveText => IsActive ? "نشط" : "غير نشط";
    }
}
