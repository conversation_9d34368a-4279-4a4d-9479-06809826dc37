<Window x:Class="SimpleAccountingApp.Windows.VATRegistrationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="التسجيل في ضريبة القيمة المضافة" 
        Height="500" Width="600"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#1B5E20" Padding="15">
            <TextBlock Text="📋 التسجيل في ضريبة القيمة المضافة" 
                      FontSize="18" 
                      FontWeight="Bold" 
                      Foreground="White" 
                      VerticalAlignment="Center"/>
        </Border>
        
        <!-- Content -->
        <ScrollViewer Grid.Row="1" Padding="30">
            <StackPanel>
                <TextBlock Text="معلومات التسجيل الحالية" FontSize="16" FontWeight="Bold" Margin="0,0,0,20"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Grid.Column="0" Text="الرقم الضريبي:" FontWeight="Bold" Margin="0,10"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" Text="300123456789003" Margin="0,10" Foreground="Blue"/>
                    
                    <TextBlock Grid.Row="1" Grid.Column="0" Text="تاريخ التسجيل:" FontWeight="Bold" Margin="0,10"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" Text="2020/01/01" Margin="0,10"/>
                    
                    <TextBlock Grid.Row="2" Grid.Column="0" Text="حالة التسجيل:" FontWeight="Bold" Margin="0,10"/>
                    <TextBlock Grid.Row="2" Grid.Column="1" Text="نشط" Margin="0,10" Foreground="Green" FontWeight="Bold"/>
                    
                    <TextBlock Grid.Row="3" Grid.Column="0" Text="نوع التسجيل:" FontWeight="Bold" Margin="0,10"/>
                    <TextBlock Grid.Row="3" Grid.Column="1" Text="إجباري" Margin="0,10"/>
                    
                    <TextBlock Grid.Row="4" Grid.Column="0" Text="فترة الإقرار:" FontWeight="Bold" Margin="0,10"/>
                    <TextBlock Grid.Row="4" Grid.Column="1" Text="شهرية" Margin="0,10"/>
                </Grid>
                
                <Border Background="#E8F5E8" Padding="20" Margin="0,30,0,0" CornerRadius="8">
                    <StackPanel>
                        <TextBlock Text="ℹ️ معلومات مهمة" FontSize="14" FontWeight="Bold" Margin="0,0,0,10" Foreground="#1B5E20"/>
                        <TextBlock TextWrapping="Wrap" FontSize="12">
                            • التسجيل في ضريبة القيمة المضافة إجباري للمنشآت التي تتجاوز إيراداتها 375,000 ريال سنوياً
                            <LineBreak/>
                            • التسجيل اختياري للمنشآت التي تتراوح إيراداتها بين 40,000 - 375,000 ريال سنوياً
                            <LineBreak/>
                            • يجب تقديم الإقرارات الضريبية في المواعيد المحددة
                            <LineBreak/>
                            • للمزيد من المعلومات: https://zatca.gov.sa
                        </TextBlock>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
        
        <!-- Buttons -->
        <Border Grid.Row="2" Background="#F5F5F5" Padding="15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="🌐 موقع هيئة الزكاة والضرائب" 
                       Background="#4CAF50" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,10" 
                       Margin="10,0"
                       FontWeight="Bold"
                       Click="WebsiteButton_Click"/>
                
                <Button Content="✖ إغلاق" 
                       Background="#F44336" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,10" 
                       Margin="10,0"
                       FontWeight="Bold"
                       Click="CloseButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
