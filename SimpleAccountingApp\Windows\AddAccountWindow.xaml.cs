using System.Windows;
using System.Windows.Controls;

namespace SimpleAccountingApp.Windows
{
    public partial class AddAccountWindow : Window
    {
        private Account? _editingAccount;
        private bool _isEditMode;

        public Account? NewAccount { get; private set; }

        public AddAccountWindow()
        {
            InitializeComponent();
            _isEditMode = false;
            LoadParentAccounts();
        }

        public AddAccountWindow(Account accountToEdit)
        {
            InitializeComponent();
            _editingAccount = accountToEdit;
            _isEditMode = true;
            
            WindowTitle.Text = "تعديل الحساب";
            this.Title = "تعديل الحساب - نظام المحاسبة المالية";
            
            LoadParentAccounts();
            LoadAccountData();
        }

        private void LoadParentAccounts()
        {
            // إضافة الحسابات الأب المتاحة
            // في التطبيق الحقيقي، سيتم جلبها من قاعدة البيانات
            ParentAccountComboBox.Items.Clear();
            ParentAccountComboBox.Items.Add(new ComboBoxItem { Content = "لا يوجد (حساب رئيسي)", Tag = null });
            ParentAccountComboBox.Items.Add(new ComboBoxItem { Content = "1000 - الأصول", Tag = "1000" });
            ParentAccountComboBox.Items.Add(new ComboBoxItem { Content = "1100 - الأصول المتداولة", Tag = "1100" });
            ParentAccountComboBox.Items.Add(new ComboBoxItem { Content = "1200 - الأصول الثابتة", Tag = "1200" });
            ParentAccountComboBox.Items.Add(new ComboBoxItem { Content = "2000 - الخصوم", Tag = "2000" });
            ParentAccountComboBox.Items.Add(new ComboBoxItem { Content = "2100 - الخصوم المتداولة", Tag = "2100" });
            ParentAccountComboBox.Items.Add(new ComboBoxItem { Content = "3000 - حقوق الملكية", Tag = "3000" });
            ParentAccountComboBox.Items.Add(new ComboBoxItem { Content = "4000 - الإيرادات", Tag = "4000" });
            ParentAccountComboBox.Items.Add(new ComboBoxItem { Content = "5000 - المصروفات", Tag = "5000" });
            
            ParentAccountComboBox.SelectedIndex = 0;
        }

        private void LoadAccountData()
        {
            if (_editingAccount != null)
            {
                AccountCodeTextBox.Text = _editingAccount.AccountCode;
                AccountNameTextBox.Text = _editingAccount.AccountName;
                
                // تحديد نوع الحساب
                foreach (ComboBoxItem item in AccountTypeComboBox.Items)
                {
                    if (item.Content.ToString() == _editingAccount.AccountType)
                    {
                        AccountTypeComboBox.SelectedItem = item;
                        break;
                    }
                }
                
                // تحديد الحساب الأب
                if (!string.IsNullOrEmpty(_editingAccount.ParentAccount))
                {
                    foreach (ComboBoxItem item in ParentAccountComboBox.Items)
                    {
                        if (item.Tag?.ToString() == _editingAccount.ParentAccount)
                        {
                            ParentAccountComboBox.SelectedItem = item;
                            break;
                        }
                    }
                }
                
                OpeningBalanceTextBox.Text = _editingAccount.OpeningBalance.ToString();
                IsActiveCheckBox.IsChecked = _editingAccount.IsActive;
                DescriptionTextBox.Text = _editingAccount.Description ?? "";
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                if (_isEditMode)
                {
                    UpdateAccount();
                }
                else
                {
                    CreateNewAccount();
                }
                
                this.DialogResult = true;
                this.Close();
            }
        }

        private bool ValidateInput()
        {
            // التحقق من اسم الحساب
            if (string.IsNullOrWhiteSpace(AccountNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الحساب", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                AccountNameTextBox.Focus();
                return false;
            }

            // التحقق من نوع الحساب
            if (AccountTypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع الحساب", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                AccountTypeComboBox.Focus();
                return false;
            }

            // التحقق من الرصيد الافتتاحي
            if (!decimal.TryParse(OpeningBalanceTextBox.Text, out _))
            {
                MessageBox.Show("يرجى إدخال رصيد افتتاحي صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                OpeningBalanceTextBox.Focus();
                return false;
            }

            return true;
        }

        private void CreateNewAccount()
        {
            NewAccount = new Account
            {
                AccountCode = string.IsNullOrWhiteSpace(AccountCodeTextBox.Text) ? GenerateAccountCode() : AccountCodeTextBox.Text,
                AccountName = AccountNameTextBox.Text.Trim(),
                AccountType = ((ComboBoxItem)AccountTypeComboBox.SelectedItem).Content.ToString()!,
                ParentAccount = GetSelectedParentAccount(),
                OpeningBalance = decimal.Parse(OpeningBalanceTextBox.Text),
                CurrentBalance = decimal.Parse(OpeningBalanceTextBox.Text),
                IsActive = IsActiveCheckBox.IsChecked ?? true,
                Description = DescriptionTextBox.Text.Trim()
            };

            // في التطبيق الحقيقي، سيتم حفظ الحساب في قاعدة البيانات
            // TODO: حفظ في قاعدة البيانات
        }

        private void UpdateAccount()
        {
            if (_editingAccount != null)
            {
                _editingAccount.AccountCode = AccountCodeTextBox.Text.Trim();
                _editingAccount.AccountName = AccountNameTextBox.Text.Trim();
                _editingAccount.AccountType = ((ComboBoxItem)AccountTypeComboBox.SelectedItem).Content.ToString()!;
                _editingAccount.ParentAccount = GetSelectedParentAccount();
                _editingAccount.OpeningBalance = decimal.Parse(OpeningBalanceTextBox.Text);
                _editingAccount.IsActive = IsActiveCheckBox.IsChecked ?? true;
                _editingAccount.Description = DescriptionTextBox.Text.Trim();

                // في التطبيق الحقيقي، سيتم تحديث الحساب في قاعدة البيانات
                // TODO: تحديث في قاعدة البيانات
            }
        }

        private string? GetSelectedParentAccount()
        {
            if (ParentAccountComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                return selectedItem.Tag?.ToString();
            }
            return null;
        }

        private string GenerateAccountCode()
        {
            // إنشاء رقم حساب تلقائي فريد
            // في التطبيق الحقيقي، سيتم إنشاؤه بناءً على آخر رقم في قاعدة البيانات
            var timestamp = DateTime.Now.ToString("HHmmss");
            var random = new Random().Next(100, 999);
            return $"{timestamp}{random}";
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            this.DialogResult = false;
            this.Close();
        }
    }
}
