<Window x:Class="SimpleAccountingApp.Windows.AddExpenseWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة مصروف" Height="650" Width="500"
        WindowStartupLocation="CenterScreen" FlowDirection="RightToLeft">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Name="TitleTextBlock" Text="إضافة مصروف جديد" 
                   FontSize="18" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- نموذج البيانات -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- رقم المصروف -->
                <Label Content="رقم المصروف:" FontWeight="Bold"/>
                <TextBox Name="ExpenseNumberTextBox" Margin="0,0,0,10" IsReadOnly="True"/>

                <!-- تاريخ المصروف -->
                <Label Content="تاريخ المصروف:" FontWeight="Bold"/>
                <DatePicker Name="ExpenseDatePicker" Margin="0,0,0,10"/>

                <!-- اسم المصروف -->
                <Label Content="اسم المصروف:" FontWeight="Bold"/>
                <TextBox Name="ExpenseNameTextBox" Margin="0,0,0,10"/>

                <!-- فئة المصروف -->
                <Label Content="فئة المصروف:" FontWeight="Bold"/>
                <ComboBox Name="CategoryComboBox" Margin="0,0,0,10">
                    <ComboBoxItem Content="مستلزمات مكتبية" Tag="OfficeSupplies"/>
                    <ComboBoxItem Content="مرافق" Tag="Utilities"/>
                    <ComboBoxItem Content="إيجار" Tag="Rent"/>
                    <ComboBoxItem Content="رواتب" Tag="Salaries"/>
                    <ComboBoxItem Content="تسويق" Tag="Marketing"/>
                    <ComboBoxItem Content="سفر" Tag="Travel"/>
                    <ComboBoxItem Content="صيانة" Tag="Maintenance"/>
                    <ComboBoxItem Content="تأمين" Tag="Insurance"/>
                    <ComboBoxItem Content="قانونية" Tag="Legal"/>
                    <ComboBoxItem Content="أخرى" Tag="Other"/>
                </ComboBox>

                <!-- نوع الدفع -->
                <Label Content="نوع الدفع:" FontWeight="Bold"/>
                <ComboBox Name="PaymentTypeComboBox" Margin="0,0,0,10" SelectionChanged="PaymentTypeComboBox_SelectionChanged">
                    <ComboBoxItem Content="نقدي" Tag="Cash"/>
                    <ComboBoxItem Content="بنكي" Tag="Bank"/>
                    <ComboBoxItem Content="آجل" Tag="Credit"/>
                    <ComboBoxItem Content="شيك" Tag="Check"/>
                </ComboBox>

                <!-- الحساب البنكي -->
                <Label Name="BankAccountLabel" Content="الحساب البنكي:" FontWeight="Bold" Visibility="Collapsed"/>
                <ComboBox Name="BankAccountComboBox" Margin="0,0,0,10" Visibility="Collapsed"/>

                <!-- المبلغ -->
                <Label Content="المبلغ:" FontWeight="Bold"/>
                <TextBox Name="AmountTextBox" Margin="0,0,0,10"/>

                <!-- المورد -->
                <Label Content="المورد:" FontWeight="Bold"/>
                <TextBox Name="VendorTextBox" Margin="0,0,0,10"/>

                <!-- رقم الفاتورة -->
                <Label Content="رقم الفاتورة:" FontWeight="Bold"/>
                <TextBox Name="InvoiceNumberTextBox" Margin="0,0,0,10"/>

                <!-- الوصف -->
                <Label Content="الوصف:" FontWeight="Bold"/>
                <TextBox Name="DescriptionTextBox" Height="60" 
                         TextWrapping="Wrap" AcceptsReturn="True" 
                         VerticalScrollBarVisibility="Auto" Margin="0,0,0,10"/>

                <!-- ملاحظات -->
                <Label Content="ملاحظات:" FontWeight="Bold"/>
                <TextBox Name="NotesTextBox" Height="60" 
                         TextWrapping="Wrap" AcceptsReturn="True" 
                         VerticalScrollBarVisibility="Auto" Margin="0,0,0,10"/>

                <!-- معتمد -->
                <CheckBox Name="IsApprovedCheckBox" Content="معتمد" Margin="0,10,0,10"
                          Checked="IsApprovedCheckBox_Checked" Unchecked="IsApprovedCheckBox_Unchecked"/>

                <!-- المعتمد من -->
                <Label Name="ApprovedByLabel" Content="المعتمد من:" FontWeight="Bold" Visibility="Collapsed"/>
                <TextBox Name="ApprovedByTextBox" Margin="0,0,0,10" Visibility="Collapsed"/>
            </StackPanel>
        </ScrollViewer>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Name="SaveButton" Content="حفظ" 
                    Width="100" Margin="0,0,10,0" Click="SaveButton_Click"/>
            <Button Name="CancelButton" Content="إلغاء" 
                    Width="100" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
