using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace SimpleAccountingApp.Windows
{
    public partial class AddSalesInvoiceWindow : Window
    {
        private SalesInvoice? _editingInvoice;
        private bool _isEditMode;
        private ObservableCollection<InvoiceItem> _invoiceItems;
        private List<InvoiceCustomer> _customers;
        private List<InvoiceProduct> _products;

        public AddSalesInvoiceWindow()
        {
            InitializeComponent();
            _isEditMode = false;
            InitializeData();
            GenerateInvoiceNumber();
        }

        public AddSalesInvoiceWindow(SalesInvoice invoiceToEdit)
        {
            InitializeComponent();
            _editingInvoice = invoiceToEdit;
            _isEditMode = true;
            
            WindowTitle.Text = "تعديل فاتورة المبيعات";
            this.Title = "تعديل فاتورة المبيعات - نظام المحاسبة المالية";
            
            InitializeData();
            LoadInvoiceData();
        }

        private void InitializeData()
        {
            _invoiceItems = new ObservableCollection<InvoiceItem>();
            ItemsDataGrid.ItemsSource = _invoiceItems;

            // تحميل العملاء
            _customers = new List<InvoiceCustomer>
            {
                new InvoiceCustomer { CustomerId = 1, CustomerName = "شركة الأمل للتجارة" },
                new InvoiceCustomer { CustomerId = 2, CustomerName = "أحمد محمد العلي" },
                new InvoiceCustomer { CustomerId = 3, CustomerName = "مؤسسة النور للمقاولات" },
                new InvoiceCustomer { CustomerId = 4, CustomerName = "فاطمة سالم القحطاني" }
            };
            CustomerComboBox.ItemsSource = _customers;

            // تحميل الأصناف
            _products = new List<InvoiceProduct>
            {
                new InvoiceProduct { ProductId = 1, ProductCode = "P001", ProductName = "لابتوب ديل إنسبايرون", SalePrice = 3200 },
                new InvoiceProduct { ProductId = 2, ProductCode = "P002", ProductName = "طابعة HP ليزر", SalePrice = 1100 },
                new InvoiceProduct { ProductId = 3, ProductCode = "P003", ProductName = "ورق A4", SalePrice = 35 },
                new InvoiceProduct { ProductId = 4, ProductCode = "P004", ProductName = "ماوس لاسلكي", SalePrice = 65 },
                new InvoiceProduct { ProductId = 5, ProductCode = "P005", ProductName = "كيبورد ميكانيكي", SalePrice = 180 }
            };

            PaymentMethodComboBox.SelectedIndex = 0;
            UpdateTotals();
        }

        private void GenerateInvoiceNumber()
        {
            Random random = new Random();
            InvoiceNumberTextBox.Text = "S" + DateTime.Now.ToString("yyyyMMdd") + random.Next(100, 999).ToString();
        }

        private void LoadInvoiceData()
        {
            if (_editingInvoice != null)
            {
                InvoiceNumberTextBox.Text = _editingInvoice.InvoiceNumber;
                InvoiceDatePicker.SelectedDate = _editingInvoice.InvoiceDate;
                
                // تحديد العميل
                var customer = _customers.FirstOrDefault(c => c.CustomerName == _editingInvoice.CustomerName);
                if (customer != null)
                {
                    CustomerComboBox.SelectedItem = customer;
                }

                // تحديد طريقة الدفع
                foreach (ComboBoxItem item in PaymentMethodComboBox.Items)
                {
                    if (item.Content.ToString() == _editingInvoice.PaymentMethod)
                    {
                        PaymentMethodComboBox.SelectedItem = item;
                        break;
                    }
                }
            }
        }

        private void AddItemButton_Click(object sender, RoutedEventArgs e)
        {
            var selectProductWindow = new SelectProductWindow(_products);
            if (selectProductWindow.ShowDialog() == true && selectProductWindow.SelectedProduct != null)
            {
                var selectedProduct = selectProductWindow.SelectedProduct;
                
                // التحقق من عدم وجود الصنف مسبقاً
                var existingItem = _invoiceItems.FirstOrDefault(item => item.ProductCode == selectedProduct.ProductCode);
                if (existingItem != null)
                {
                    MessageBox.Show("هذا الصنف موجود بالفعل في الفاتورة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var invoiceItem = new InvoiceItem
                {
                    ProductCode = selectedProduct.ProductCode,
                    ProductName = selectedProduct.ProductName,
                    Quantity = 1,
                    UnitPrice = selectedProduct.SalePrice,
                    DiscountAmount = 0,
                    TaxPercentage = 15,
                    TaxAmount = selectedProduct.SalePrice * 0.15m, // ضريبة على السعر الكامل (بدون خصم)
                    TotalAmount = selectedProduct.SalePrice + (selectedProduct.SalePrice * 0.15m)
                };

                _invoiceItems.Add(invoiceItem);
                UpdateTotals();
            }
        }

        private void UpdateTotals()
        {
            // حساب المبلغ الفرعي (قبل الخصم والضريبة)
            decimal subTotal = _invoiceItems.Sum(item => item.Quantity * item.UnitPrice);

            // حساب إجمالي الخصومات
            decimal totalDiscount = _invoiceItems.Sum(item => item.DiscountAmount);

            // حساب إجمالي الضرائب (بعد الخصم)
            decimal totalTax = _invoiceItems.Sum(item => item.TaxAmount);

            // حساب المبلغ الإجمالي النهائي
            decimal grandTotal = subTotal - totalDiscount + totalTax;

            // عرض النتائج
            SubTotalText.Text = subTotal.ToString("N2") + " ريال";
            TotalDiscountText.Text = totalDiscount.ToString("N2") + " ريال";
            TotalTaxText.Text = totalTax.ToString("N2") + " ريال (ضريبة القيمة المضافة 15%)";
            GrandTotalText.Text = grandTotal.ToString("N2") + " ريال";

            // حفظ معلومات الضريبة للتقارير الضريبية
            SaveTaxInformation(subTotal, totalDiscount, totalTax, grandTotal);
        }

        private void SaveTaxInformation(decimal subTotal, decimal discount, decimal vatAmount, decimal total)
        {
            // معلومات ضريبية للتقارير والامتثال
            var taxInfo = new
            {
                InvoiceDate = DateTime.Now,
                InvoiceNumber = InvoiceNumberTextBox.Text,
                CustomerName = ((InvoiceCustomer?)CustomerComboBox.SelectedItem)?.CustomerName ?? "",
                SubTotal = subTotal,
                DiscountAmount = discount,
                TaxableAmount = subTotal - discount,
                VATRate = 15m,
                VATAmount = vatAmount,
                TotalAmount = total,
                CompanyVATNumber = "300123456789003", // الرقم الضريبي للشركة
                IsVATInvoice = true,
                Currency = "SAR"
            };

            // في التطبيق الحقيقي، سيتم حفظ هذه المعلومات في قاعدة البيانات
            // لاستخدامها في التقارير الضريبية والإقرارات
            // TODO: حفظ معلومات الضريبة في قاعدة البيانات
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInvoice())
            {
                SaveInvoice();
                this.DialogResult = true;
                this.Close();
            }
        }

        private void SaveAndPrintButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInvoice())
            {
                SaveInvoice();
                MessageBox.Show("تم حفظ الفاتورة. الطباعة قيد التطوير.", "حفظ وطباعة", MessageBoxButton.OK, MessageBoxImage.Information);
                this.DialogResult = true;
                this.Close();
            }
        }

        private bool ValidateInvoice()
        {
            if (string.IsNullOrWhiteSpace(InvoiceNumberTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الفاتورة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (CustomerComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار العميل", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (_invoiceItems.Count == 0)
            {
                MessageBox.Show("يرجى إضافة أصناف للفاتورة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private void SaveInvoice()
        {
            // في التطبيق الحقيقي، سيتم حفظ الفاتورة في قاعدة البيانات
            // TODO: حفظ الفاتورة والأصناف في قاعدة البيانات
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            this.DialogResult = false;
            this.Close();
        }

        private void ItemsDataGrid_CellEditEnding(object sender, DataGridCellEditEndingEventArgs e)
        {
            // تحديث الحسابات عند تعديل أي خلية في الجدول
            if (e.EditAction == DataGridEditAction.Commit)
            {
                // استخدام Dispatcher لتأخير التحديث حتى يتم حفظ القيمة الجديدة
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    UpdateItemCalculations();
                    UpdateTotals();
                }), System.Windows.Threading.DispatcherPriority.Background);
            }
        }

        private void UpdateItemCalculations()
        {
            // تحديث حسابات كل صنف في الفاتورة
            foreach (var item in _invoiceItems)
            {
                // حساب المبلغ بعد الخصم
                decimal itemSubTotal = item.Quantity * item.UnitPrice;
                decimal itemAfterDiscount = itemSubTotal - item.DiscountAmount;

                // حساب الضريبة على المبلغ بعد الخصم
                item.TaxAmount = itemAfterDiscount * (item.TaxPercentage / 100);

                // حساب المبلغ الإجمالي
                item.TotalAmount = itemAfterDiscount + item.TaxAmount;
            }

            // تحديث عرض البيانات
            ItemsDataGrid.Items.Refresh();
        }
    }

    // نماذج البيانات المساعدة
    public class InvoiceItem
    {
        public string ProductCode { get; set; } = "";
        public string ProductName { get; set; } = "";
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxPercentage { get; set; } = 15;
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
    }

    public class InvoiceCustomer
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = "";
    }

    public class InvoiceProduct
    {
        public int ProductId { get; set; }
        public string ProductCode { get; set; } = "";
        public string ProductName { get; set; } = "";
        public decimal SalePrice { get; set; }
    }
}
