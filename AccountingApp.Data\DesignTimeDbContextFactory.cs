using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace AccountingApp.Data
{
    /// <summary>
    /// مصنع DbContext لوقت التصميم - Design Time DbContext Factory
    /// </summary>
    public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<AccountingDbContext>
    {
        public AccountingDbContext CreateDbContext(string[] args)
        {
            var optionsBuilder = new DbContextOptionsBuilder<AccountingDbContext>();
            
            // استخدام LocalDB للتطوير
            optionsBuilder.UseSqlServer("Server=(localdb)\\mssqllocaldb;Database=AccountingAppDb;Trusted_Connection=true;MultipleActiveResultSets=true");
            
            return new AccountingDbContext(optionsBuilder.Options);
        }
    }
}
