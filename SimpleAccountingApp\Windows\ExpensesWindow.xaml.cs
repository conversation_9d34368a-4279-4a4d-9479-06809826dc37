using System.Collections.ObjectModel;
using System.Windows;
using SimpleAccountingApp.Models;
using SimpleAccountingApp.Data;
using Microsoft.EntityFrameworkCore;

namespace SimpleAccountingApp.Windows
{
    public partial class ExpensesWindow : Window
    {
        private ObservableCollection<Expense> expenses = new();
        private AccountingDbContext _context;

        public ExpensesWindow()
        {
            InitializeComponent();
            _context = new AccountingDbContext();
            LoadExpenses();
        }

        private async void LoadExpenses()
        {
            try
            {
                await _context.InitializeDatabaseAsync();
                var expensesList = await _context.Expenses
                    .Include(e => e.BankAccount)
                    .OrderByDescending(e => e.ExpenseDate)
                    .ToListAsync();

                expenses.Clear();
                foreach (var expense in expensesList)
                {
                    expenses.Add(expense);
                }

                ExpensesDataGrid.ItemsSource = expenses;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المصروفات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void AddExpenseButton_Click(object sender, RoutedEventArgs e)
        {
            var addExpenseWindow = new AddExpenseWindow();
            if (addExpenseWindow.ShowDialog() == true)
            {
                var newExpense = addExpenseWindow.NewExpense;
                if (newExpense != null)
                {
                    try
                    {
                        _context.Expenses.Add(newExpense);
                        await _context.SaveChangesAsync();

                        expenses.Add(newExpense);
                        RefreshExpensesGrid();

                        MessageBox.Show("تم إضافة المصروف بنجاح!", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حفظ المصروف: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private async void EditExpenseButton_Click(object sender, RoutedEventArgs e)
        {
            if (ExpensesDataGrid.SelectedItem is Expense selectedExpense)
            {
                var editExpenseWindow = new AddExpenseWindow(selectedExpense);
                if (editExpenseWindow.ShowDialog() == true)
                {
                    try
                    {
                        _context.Expenses.Update(selectedExpense);
                        await _context.SaveChangesAsync();

                        RefreshExpensesGrid();

                        MessageBox.Show("تم تعديل المصروف بنجاح!", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في تحديث المصروف: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار مصروف للتعديل.", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void DeleteExpenseButton_Click(object sender, RoutedEventArgs e)
        {
            if (ExpensesDataGrid.SelectedItem is Expense selectedExpense)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف المصروف '{selectedExpense.ExpenseName}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        _context.Expenses.Remove(selectedExpense);
                        await _context.SaveChangesAsync();

                        expenses.Remove(selectedExpense);
                        RefreshExpensesGrid();

                        MessageBox.Show("تم حذف المصروف بنجاح!", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف المصروف: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار مصروف للحذف.", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadExpenses();
        }

        private void RefreshExpensesGrid()
        {
            ExpensesDataGrid.Items.Refresh();
        }
    }
}
