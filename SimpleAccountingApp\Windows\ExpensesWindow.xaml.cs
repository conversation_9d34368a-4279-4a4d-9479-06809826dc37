using System.Collections.ObjectModel;
using System.Windows;
using SimpleAccountingApp.Models;

namespace SimpleAccountingApp.Windows
{
    public partial class ExpensesWindow : Window
    {
        private ObservableCollection<Expense> expenses = new();

        public ExpensesWindow()
        {
            InitializeComponent();
            LoadExpenses();
        }

        private void LoadExpenses()
        {
            // مؤقتاً سنعرض قائمة فارغة
            ExpensesDataGrid.ItemsSource = expenses;
        }

        private void AddExpenseButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void EditExpenseButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void DeleteExpenseButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadExpenses();
        }
    }
}
