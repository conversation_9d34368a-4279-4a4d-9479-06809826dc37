<Window x:Class="SimpleAccountingApp.Windows.AddAccountWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة حساب جديد - نظام المحاسبة المالية" 
        Height="500" Width="600"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#4CAF50" Padding="15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="➕" FontSize="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock x:Name="WindowTitle" 
                          Text="إضافة حساب جديد" 
                          FontSize="18" 
                          FontWeight="Bold" 
                          Foreground="White" 
                          VerticalAlignment="Center"/>
            </StackPanel>
        </Border>
        
        <!-- Form Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <StackPanel>
                <!-- Account Code -->
                <TextBlock Text="رقم الحساب *" FontWeight="Bold" Margin="0,10,0,5"/>
                <TextBox x:Name="AccountCodeTextBox" 
                        Height="35" 
                        Padding="10"
                        FontSize="14"/>
                <TextBlock Text="سيتم إنشاء رقم تلقائي إذا ترك فارغاً" 
                          FontSize="12" 
                          Foreground="#666" 
                          Margin="0,2,0,0"/>
                
                <!-- Account Name -->
                <TextBlock Text="اسم الحساب *" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBox x:Name="AccountNameTextBox" 
                        Height="35" 
                        Padding="10"
                        FontSize="14"/>
                
                <!-- Account Name English -->
                <TextBlock Text="اسم الحساب بالإنجليزية" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBox x:Name="AccountNameEnglishTextBox" 
                        Height="35" 
                        Padding="10"
                        FontSize="14"/>
                
                <!-- Account Type -->
                <TextBlock Text="نوع الحساب *" FontWeight="Bold" Margin="0,15,0,5"/>
                <ComboBox x:Name="AccountTypeComboBox" 
                         Height="35" 
                         Padding="10"
                         FontSize="14">
                    <ComboBoxItem Content="أصول"/>
                    <ComboBoxItem Content="خصوم"/>
                    <ComboBoxItem Content="حقوق ملكية"/>
                    <ComboBoxItem Content="إيرادات"/>
                    <ComboBoxItem Content="مصروفات"/>
                    <ComboBoxItem Content="تكلفة البضاعة المباعة"/>
                </ComboBox>
                
                <!-- Parent Account -->
                <TextBlock Text="الحساب الأب" FontWeight="Bold" Margin="0,15,0,5"/>
                <ComboBox x:Name="ParentAccountComboBox" 
                         Height="35" 
                         Padding="10"
                         FontSize="14">
                    <ComboBoxItem Content="لا يوجد (حساب رئيسي)"/>
                </ComboBox>
                
                <!-- Opening Balance -->
                <TextBlock Text="الرصيد الافتتاحي" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBox x:Name="OpeningBalanceTextBox" 
                        Height="35" 
                        Padding="10"
                        FontSize="14"
                        Text="0"/>
                
                <!-- Balance Type -->
                <TextBlock Text="نوع الرصيد" FontWeight="Bold" Margin="0,15,0,5"/>
                <StackPanel Orientation="Horizontal">
                    <RadioButton x:Name="DebitRadioButton" 
                               Content="مدين" 
                               IsChecked="True" 
                               Margin="0,0,20,0"
                               VerticalAlignment="Center"/>
                    <RadioButton x:Name="CreditRadioButton" 
                               Content="دائن" 
                               VerticalAlignment="Center"/>
                </StackPanel>
                
                <!-- Is Active -->
                <CheckBox x:Name="IsActiveCheckBox" 
                         Content="حساب نشط" 
                         IsChecked="True" 
                         Margin="0,15,0,0"
                         FontWeight="Bold"/>
                
                <!-- Description -->
                <TextBlock Text="الوصف" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBox x:Name="DescriptionTextBox" 
                        Height="80" 
                        Padding="10"
                        FontSize="14"
                        TextWrapping="Wrap"
                        AcceptsReturn="True"
                        VerticalScrollBarVisibility="Auto"/>
            </StackPanel>
        </ScrollViewer>
        
        <!-- Buttons -->
        <Border Grid.Row="2" Background="#F5F5F5" Padding="15" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton" 
                       Content="💾 حفظ" 
                       Background="#4CAF50" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="20,10" 
                       Margin="10,0"
                       FontSize="14"
                       FontWeight="Bold"
                       Click="SaveButton_Click"/>
                
                <Button x:Name="CancelButton" 
                       Content="❌ إلغاء" 
                       Background="#F44336" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="20,10" 
                       Margin="10,0"
                       FontSize="14"
                       FontWeight="Bold"
                       Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
