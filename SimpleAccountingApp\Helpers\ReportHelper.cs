using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Xps.Packaging;
using System.Windows.Xps;
using Microsoft.Win32;
using ClosedXML.Excel;
using System.Data;

namespace SimpleAccountingApp.Helpers
{
    public static class ReportHelper
    {
        /// <summary>
        /// طباعة DataGrid
        /// </summary>
        public static void PrintDataGrid(DataGrid dataGrid, string reportTitle)
        {
            try
            {
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    // إنشاء مستند للطباعة
                    var flowDocument = CreateFlowDocument(dataGrid, reportTitle);
                    
                    // طباعة المستند
                    IDocumentPaginatorSource idpSource = flowDocument;
                    printDialog.PrintDocument(idpSource.DocumentPaginator, reportTitle);
                    
                    MessageBox.Show("تم إرسال التقرير للطباعة بنجاح!", "طباعة", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تصدير DataGrid إلى Excel
        /// </summary>
        public static void ExportDataGridToExcel(DataGrid dataGrid, string reportTitle)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx",
                    Title = "حفظ التقرير",
                    FileName = $"{reportTitle}_{DateTime.Now:yyyy-MM-dd}.xlsx"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    using var workbook = new XLWorkbook();
                    var worksheet = workbook.Worksheets.Add(reportTitle);

                    // إضافة العنوان
                    worksheet.Cell(1, 1).Value = reportTitle;
                    worksheet.Cell(1, 1).Style.Font.Bold = true;
                    worksheet.Cell(1, 1).Style.Font.FontSize = 16;
                    worksheet.Range(1, 1, 1, dataGrid.Columns.Count).Merge();

                    // إضافة التاريخ
                    worksheet.Cell(2, 1).Value = $"تاريخ التقرير: {DateTime.Now:yyyy-MM-dd HH:mm}";
                    worksheet.Range(2, 1, 2, dataGrid.Columns.Count).Merge();

                    // إضافة رؤوس الأعمدة
                    int headerRow = 4;
                    for (int i = 0; i < dataGrid.Columns.Count; i++)
                    {
                        var header = dataGrid.Columns[i].Header?.ToString() ?? $"Column {i + 1}";
                        worksheet.Cell(headerRow, i + 1).Value = header;
                        worksheet.Cell(headerRow, i + 1).Style.Font.Bold = true;
                        worksheet.Cell(headerRow, i + 1).Style.Fill.BackgroundColor = XLColor.LightGray;
                    }

                    // إضافة البيانات
                    int dataRow = headerRow + 1;
                    foreach (var item in dataGrid.Items)
                    {
                        if (item == null) continue;

                        for (int i = 0; i < dataGrid.Columns.Count; i++)
                        {
                            var column = dataGrid.Columns[i];
                            var value = GetCellValue(item, column);
                            worksheet.Cell(dataRow, i + 1).Value = value;
                        }
                        dataRow++;
                    }

                    // تنسيق الجدول
                    var tableRange = worksheet.Range(headerRow, 1, dataRow - 1, dataGrid.Columns.Count);
                    tableRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thick;
                    tableRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;

                    // ضبط عرض الأعمدة
                    worksheet.Columns().AdjustToContents();

                    // حفظ الملف
                    workbook.SaveAs(saveFileDialog.FileName);

                    MessageBox.Show($"تم تصدير التقرير بنجاح إلى:\n{saveFileDialog.FileName}", 
                        "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);

                    // فتح الملف
                    if (MessageBox.Show("هل تريد فتح الملف الآن؟", "فتح الملف", 
                        MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes)
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = saveFileDialog.FileName,
                            UseShellExecute = true
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إنشاء مستند للطباعة
        /// </summary>
        private static FlowDocument CreateFlowDocument(DataGrid dataGrid, string reportTitle)
        {
            var flowDocument = new FlowDocument();
            flowDocument.PagePadding = new Thickness(50);

            // إضافة العنوان
            var titleParagraph = new Paragraph(new Run(reportTitle))
            {
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            flowDocument.Blocks.Add(titleParagraph);

            // إضافة التاريخ
            var dateParagraph = new Paragraph(new Run($"تاريخ التقرير: {DateTime.Now:yyyy-MM-dd HH:mm}"))
            {
                FontSize = 12,
                TextAlignment = TextAlignment.Right,
                Margin = new Thickness(0, 0, 0, 20)
            };
            flowDocument.Blocks.Add(dateParagraph);

            // إنشاء الجدول
            var table = new Table();
            table.CellSpacing = 0;
            table.BorderBrush = Brushes.Black;
            table.BorderThickness = new Thickness(1);

            // إضافة الأعمدة
            for (int i = 0; i < dataGrid.Columns.Count; i++)
            {
                table.Columns.Add(new TableColumn());
            }

            // إضافة مجموعة الصفوف
            var rowGroup = new TableRowGroup();

            // إضافة صف الرؤوس
            var headerRow = new TableRow();
            headerRow.Background = Brushes.LightGray;
            
            foreach (var column in dataGrid.Columns)
            {
                var cell = new TableCell(new Paragraph(new Run(column.Header?.ToString() ?? "")))
                {
                    BorderBrush = Brushes.Black,
                    BorderThickness = new Thickness(1),
                    Padding = new Thickness(5),
                    FontWeight = FontWeights.Bold
                };
                headerRow.Cells.Add(cell);
            }
            rowGroup.Rows.Add(headerRow);

            // إضافة صفوف البيانات
            foreach (var item in dataGrid.Items)
            {
                if (item == null) continue;

                var dataRow = new TableRow();
                
                foreach (var column in dataGrid.Columns)
                {
                    var value = GetCellValue(item, column);
                    var cell = new TableCell(new Paragraph(new Run(value)))
                    {
                        BorderBrush = Brushes.Black,
                        BorderThickness = new Thickness(1),
                        Padding = new Thickness(5)
                    };
                    dataRow.Cells.Add(cell);
                }
                rowGroup.Rows.Add(dataRow);
            }

            table.RowGroups.Add(rowGroup);
            flowDocument.Blocks.Add(table);

            return flowDocument;
        }

        /// <summary>
        /// الحصول على قيمة الخلية
        /// </summary>
        private static string GetCellValue(object item, DataGridColumn column)
        {
            try
            {
                if (column is DataGridTextColumn textColumn && textColumn.Binding is System.Windows.Data.Binding binding)
                {
                    var propertyName = binding.Path.Path;
                    var property = item.GetType().GetProperty(propertyName);
                    var value = property?.GetValue(item);
                    
                    if (value is decimal decimalValue)
                        return decimalValue.ToString("N2");
                    if (value is DateTime dateValue)
                        return dateValue.ToString("yyyy-MM-dd");
                    
                    return value?.ToString() ?? "";
                }
                return "";
            }
            catch
            {
                return "";
            }
        }

        /// <summary>
        /// طباعة نص بسيط
        /// </summary>
        public static void PrintText(string content, string title)
        {
            try
            {
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    var flowDocument = new FlowDocument();
                    flowDocument.PagePadding = new Thickness(50);

                    var titleParagraph = new Paragraph(new Run(title))
                    {
                        FontSize = 18,
                        FontWeight = FontWeights.Bold,
                        TextAlignment = TextAlignment.Center,
                        Margin = new Thickness(0, 0, 0, 20)
                    };
                    flowDocument.Blocks.Add(titleParagraph);

                    var contentParagraph = new Paragraph(new Run(content))
                    {
                        FontSize = 12,
                        TextAlignment = TextAlignment.Right
                    };
                    flowDocument.Blocks.Add(contentParagraph);

                    IDocumentPaginatorSource idpSource = flowDocument;
                    printDialog.PrintDocument(idpSource.DocumentPaginator, title);

                    MessageBox.Show("تم إرسال التقرير للطباعة بنجاح!", "طباعة", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
