using System.ComponentModel.DataAnnotations;

namespace SimpleAccountingApp.Models
{
    public class Account
    {
        [Key]
        public int AccountId { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string AccountCode { get; set; } = "";
        
        [Required]
        [MaxLength(200)]
        public string AccountName { get; set; } = "";
        
        [Required]
        [MaxLength(50)]
        public string AccountType { get; set; } = "";
        
        public decimal Balance { get; set; }
        
        [MaxLength(500)]
        public string Description { get; set; } = "";
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public bool IsActive { get; set; } = true;
    }
}
