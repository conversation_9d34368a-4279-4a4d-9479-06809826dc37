<Window x:Class="SimpleAccountingApp.Windows.SelectEmployeeWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="اختيار موظف" Height="500" Width="700"
        WindowStartupLocation="CenterScreen" FlowDirection="RightToLeft">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="اختيار موظف" 
                   FontSize="18" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- بحث -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,20">
            <Label Content="بحث:" FontWeight="Bold" VerticalAlignment="Center"/>
            <TextBox Name="SearchTextBox" Width="200" Margin="5,0,10,0" 
                     TextChanged="SearchTextBox_TextChanged"/>
            <Label Content="القسم:" FontWeight="Bold" VerticalAlignment="Center" Margin="20,0,5,0"/>
            <ComboBox Name="DepartmentComboBox" Width="150" Margin="5,0,0,0"
                      SelectionChanged="DepartmentComboBox_SelectionChanged"/>
        </StackPanel>

        <!-- جدول الموظفين -->
        <DataGrid Grid.Row="2" Name="EmployeesDataGrid" 
                  AutoGenerateColumns="False" 
                  CanUserAddRows="False" 
                  CanUserDeleteRows="False"
                  IsReadOnly="True"
                  GridLinesVisibility="All"
                  HeadersVisibility="Column"
                  SelectionMode="Single"
                  MouseDoubleClick="EmployeesDataGrid_MouseDoubleClick">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="كود الموظف" Binding="{Binding EmployeeCode}" Width="100"/>
                <DataGridTextColumn Header="الاسم الكامل" Binding="{Binding FullName}" Width="200"/>
                <DataGridTextColumn Header="المسمى الوظيفي" Binding="{Binding JobTitle}" Width="150"/>
                <DataGridTextColumn Header="القسم" Binding="{Binding Department}" Width="120"/>
                <DataGridTextColumn Header="الراتب الأساسي" Binding="{Binding BasicSalary, StringFormat='{}{0:N2} ريال'}" Width="120"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" 
                    HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Name="SelectButton" Content="اختيار" 
                    Width="100" Margin="0,0,10,0" Click="SelectButton_Click"/>
            <Button Name="CancelButton" Content="إلغاء" 
                    Width="100" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
