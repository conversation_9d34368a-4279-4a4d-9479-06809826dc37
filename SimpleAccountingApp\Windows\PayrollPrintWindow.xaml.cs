using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using SimpleAccountingApp.Models;

namespace SimpleAccountingApp.Windows
{
    public partial class PayrollPrintWindow : Window
    {
        private Payroll _payroll;

        public PayrollPrintWindow(Payroll payroll)
        {
            InitializeComponent();
            _payroll = payroll;
            LoadPayrollContent();
        }

        private void LoadPayrollContent()
        {
            PayrollContentPanel.Children.Clear();

            // عنوان الشركة
            var companyTitle = new TextBlock
            {
                Text = "شركة المحاسبة المتقدمة",
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            PayrollContentPanel.Children.Add(companyTitle);

            // عنوان كشف الراتب
            var payrollTitle = new TextBlock
            {
                Text = "كشف راتب",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            PayrollContentPanel.Children.Add(payrollTitle);

            // معلومات الموظف
            var employeeInfo = new StackPanel { Margin = new Thickness(0, 0, 0, 20) };
            
            employeeInfo.Children.Add(CreateInfoRow("اسم الموظف:", _payroll.Employee?.FullName ?? "غير محدد"));
            employeeInfo.Children.Add(CreateInfoRow("كود الموظف:", _payroll.Employee?.EmployeeCode ?? "غير محدد"));
            employeeInfo.Children.Add(CreateInfoRow("القسم:", _payroll.Employee?.Department ?? "غير محدد"));
            employeeInfo.Children.Add(CreateInfoRow("المسمى الوظيفي:", _payroll.Employee?.JobTitle ?? "غير محدد"));
            employeeInfo.Children.Add(CreateInfoRow("الشهر:", $"{_payroll.MonthName} {_payroll.Year}"));
            employeeInfo.Children.Add(CreateInfoRow("تاريخ الراتب:", _payroll.PayrollDate.ToString("yyyy/MM/dd")));

            PayrollContentPanel.Children.Add(employeeInfo);

            // تفاصيل الراتب
            var salaryDetails = new Grid { Margin = new Thickness(0, 20, 0, 20) };
            salaryDetails.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            salaryDetails.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // العمود الأيسر - الاستحقاقات
            var earningsPanel = new StackPanel();
            earningsPanel.Children.Add(CreateSectionHeader("الاستحقاقات"));
            earningsPanel.Children.Add(CreateAmountRow("الراتب الأساسي:", _payroll.BasicSalary));
            earningsPanel.Children.Add(CreateAmountRow("بدل السكن:", _payroll.HousingAllowance));
            earningsPanel.Children.Add(CreateAmountRow("بدل المواصلات:", _payroll.TransportationAllowance));
            earningsPanel.Children.Add(CreateAmountRow("بدل الطعام:", _payroll.FoodAllowance));
            earningsPanel.Children.Add(CreateAmountRow("بدلات أخرى:", _payroll.OtherAllowances));
            earningsPanel.Children.Add(CreateAmountRow("أجر الساعات الإضافية:", _payroll.OvertimePay));
            earningsPanel.Children.Add(CreateSeparator());
            earningsPanel.Children.Add(CreateAmountRow("إجمالي الاستحقاقات:", _payroll.GrossSalary, true));

            Grid.SetColumn(earningsPanel, 0);
            salaryDetails.Children.Add(earningsPanel);

            // العمود الأيمن - الخصومات
            var deductionsPanel = new StackPanel { Margin = new Thickness(20, 0, 0, 0) };
            deductionsPanel.Children.Add(CreateSectionHeader("الخصومات"));
            deductionsPanel.Children.Add(CreateAmountRow("التأمينات الاجتماعية:", _payroll.SocialInsurance));
            deductionsPanel.Children.Add(CreateAmountRow("ضريبة الدخل:", _payroll.IncomeTax));
            deductionsPanel.Children.Add(CreateAmountRow("قرض:", _payroll.Loan));
            deductionsPanel.Children.Add(CreateAmountRow("خصومات أخرى:", _payroll.OtherDeductions));
            deductionsPanel.Children.Add(CreateSeparator());
            deductionsPanel.Children.Add(CreateAmountRow("إجمالي الخصومات:", _payroll.TotalDeductions, true));

            Grid.SetColumn(deductionsPanel, 1);
            salaryDetails.Children.Add(deductionsPanel);

            PayrollContentPanel.Children.Add(salaryDetails);

            // الراتب الصافي
            var netSalaryPanel = new Border
            {
                BorderBrush = Brushes.Black,
                BorderThickness = new Thickness(2),
                Padding = new Thickness(10),
                Margin = new Thickness(0, 20, 0, 0),
                Background = new SolidColorBrush(Color.FromRgb(240, 240, 240))
            };

            var netSalaryText = new TextBlock
            {
                Text = $"صافي الراتب: {_payroll.NetSalary:N2} ريال",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            netSalaryPanel.Child = netSalaryText;
            PayrollContentPanel.Children.Add(netSalaryPanel);

            // ملاحظات
            if (!string.IsNullOrEmpty(_payroll.Notes))
            {
                var notesPanel = new StackPanel { Margin = new Thickness(0, 20, 0, 0) };
                notesPanel.Children.Add(CreateSectionHeader("ملاحظات"));
                notesPanel.Children.Add(new TextBlock { Text = _payroll.Notes, TextWrapping = TextWrapping.Wrap });
                PayrollContentPanel.Children.Add(notesPanel);
            }
        }

        private StackPanel CreateInfoRow(string label, string value)
        {
            var panel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 2, 0, 2) };
            
            panel.Children.Add(new TextBlock 
            { 
                Text = label, 
                FontWeight = FontWeights.Bold, 
                Width = 120,
                Margin = new Thickness(0, 0, 10, 0)
            });
            
            panel.Children.Add(new TextBlock { Text = value });
            
            return panel;
        }

        private StackPanel CreateAmountRow(string label, decimal amount, bool isBold = false)
        {
            var panel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 2, 0, 2) };
            
            var labelBlock = new TextBlock 
            { 
                Text = label, 
                Width = 150,
                Margin = new Thickness(0, 0, 10, 0)
            };
            
            var amountBlock = new TextBlock 
            { 
                Text = $"{amount:N2} ريال",
                HorizontalAlignment = HorizontalAlignment.Right
            };

            if (isBold)
            {
                labelBlock.FontWeight = FontWeights.Bold;
                amountBlock.FontWeight = FontWeights.Bold;
            }
            
            panel.Children.Add(labelBlock);
            panel.Children.Add(amountBlock);
            
            return panel;
        }

        private TextBlock CreateSectionHeader(string text)
        {
            return new TextBlock
            {
                Text = text,
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 10),
                Foreground = new SolidColorBrush(Color.FromRgb(0, 100, 200))
            };
        }

        private Border CreateSeparator()
        {
            return new Border
            {
                BorderBrush = Brushes.Gray,
                BorderThickness = new Thickness(0, 1, 0, 0),
                Margin = new Thickness(0, 5, 0, 5)
            };
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    // إنشاء مستند للطباعة
                    var flowDoc = new FlowDocument();
                    var paragraph = new Paragraph();
                    
                    // تحويل المحتوى إلى نص للطباعة
                    var content = $@"
كشف راتب

اسم الموظف: {_payroll.Employee?.FullName}
كود الموظف: {_payroll.Employee?.EmployeeCode}
القسم: {_payroll.Employee?.Department}
المسمى الوظيفي: {_payroll.Employee?.JobTitle}
الشهر: {_payroll.MonthName} {_payroll.Year}

الاستحقاقات:
الراتب الأساسي: {_payroll.BasicSalary:N2} ريال
بدل السكن: {_payroll.HousingAllowance:N2} ريال
بدل المواصلات: {_payroll.TransportationAllowance:N2} ريال
بدل الطعام: {_payroll.FoodAllowance:N2} ريال
بدلات أخرى: {_payroll.OtherAllowances:N2} ريال
أجر الساعات الإضافية: {_payroll.OvertimePay:N2} ريال
إجمالي الاستحقاقات: {_payroll.GrossSalary:N2} ريال

الخصومات:
التأمينات الاجتماعية: {_payroll.SocialInsurance:N2} ريال
ضريبة الدخل: {_payroll.IncomeTax:N2} ريال
قرض: {_payroll.Loan:N2} ريال
خصومات أخرى: {_payroll.OtherDeductions:N2} ريال
إجمالي الخصومات: {_payroll.TotalDeductions:N2} ريال

صافي الراتب: {_payroll.NetSalary:N2} ريال
";

                    paragraph.Inlines.Add(new Run(content));
                    flowDoc.Blocks.Add(paragraph);

                    printDialog.PrintDocument(((IDocumentPaginatorSource)flowDoc).DocumentPaginator, "كشف راتب");
                    
                    MessageBox.Show("تم إرسال كشف الراتب للطباعة بنجاح!", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
