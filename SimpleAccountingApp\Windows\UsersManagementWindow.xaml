<Window x:Class="SimpleAccountingApp.Windows.UsersManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة المستخدمين - نظام المحاسبة المالية" 
        Height="700" Width="1100"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="50"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#673AB7" Padding="15">
            <DockPanel>
                <Button DockPanel.Dock="Left" 
                       Content="✖ إغلاق" 
                       Background="#512DA8" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,5" 
                       Click="CloseButton_Click"/>
                
                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                    <TextBlock Text="👥" FontSize="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="إدارة المستخدمين" 
                              FontSize="20" 
                              FontWeight="Bold" 
                              Foreground="White" 
                              VerticalAlignment="Center"/>
                </StackPanel>
            </DockPanel>
        </Border>
        
        <!-- Toolbar -->
        <Border Grid.Row="1" Background="#F5F5F5" Padding="10" BorderBrush="#DDD" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <Button x:Name="AddUserButton" 
                       Content="➕ إضافة مستخدم جديد" 
                       Background="#4CAF50" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="5,0"
                       FontWeight="Bold"
                       Click="AddUserButton_Click"/>
                
                <Button x:Name="EditUserButton" 
                       Content="✏️ تعديل" 
                       Background="#FF9800" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="5,0"
                       FontWeight="Bold"
                       IsEnabled="False"
                       Click="EditUserButton_Click"/>
                
                <Button x:Name="DeleteUserButton" 
                       Content="🗑️ حذف" 
                       Background="#F44336" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="5,0"
                       FontWeight="Bold"
                       IsEnabled="False"
                       Click="DeleteUserButton_Click"/>
                
                <Button x:Name="ResetPasswordButton" 
                       Content="🔑 إعادة تعيين كلمة المرور" 
                       Background="#9C27B0" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="5,0"
                       FontWeight="Bold"
                       IsEnabled="False"
                       Click="ResetPasswordButton_Click"/>
                
                <Separator Margin="10,0"/>
                
                <TextBlock Text="البحث:" VerticalAlignment="Center" Margin="10,0,5,0" FontWeight="Bold"/>
                <TextBox x:Name="SearchTextBox" 
                        Width="200" 
                        Height="30"
                        Padding="5"
                        FontSize="14"
                        TextChanged="SearchTextBox_TextChanged"/>
            </StackPanel>
        </Border>
        
        <!-- Users DataGrid -->
        <Grid Grid.Row="2" Margin="10">
            <DataGrid x:Name="UsersDataGrid"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     AlternatingRowBackground="#F9F9F9"
                     SelectionChanged="UsersDataGrid_SelectionChanged">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="الاسم الكامل" 
                                       Binding="{Binding FullName}" 
                                       Width="200"/>
                    
                    <DataGridTextColumn Header="اسم المستخدم" 
                                       Binding="{Binding Username}" 
                                       Width="150"/>
                    
                    <DataGridTextColumn Header="البريد الإلكتروني" 
                                       Binding="{Binding Email}" 
                                       Width="200"/>
                    
                    <DataGridTextColumn Header="الدور" 
                                       Binding="{Binding Role}" 
                                       Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding Role}" Value="مدير النظام">
                                        <Setter Property="Foreground" Value="Red"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding Role}" Value="محاسب">
                                        <Setter Property="Foreground" Value="Blue"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="القسم" 
                                       Binding="{Binding Department}" 
                                       Width="120"/>
                    
                    <DataGridTextColumn Header="رقم الهاتف" 
                                       Binding="{Binding Phone}" 
                                       Width="120"/>
                    
                    <DataGridTextColumn Header="تاريخ الإنشاء" 
                                       Binding="{Binding CreatedDate, StringFormat=yyyy/MM/dd}" 
                                       Width="120"/>
                    
                    <DataGridTextColumn Header="آخر دخول" 
                                       Binding="{Binding LastLoginDate, StringFormat=yyyy/MM/dd HH:mm}" 
                                       Width="140"/>
                    
                    <DataGridTextColumn Header="الحالة" 
                                       Binding="{Binding IsActiveText}" 
                                       Width="80">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsActive}" Value="True">
                                        <Setter Property="Foreground" Value="Green"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding IsActive}" Value="False">
                                        <Setter Property="Foreground" Value="Red"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>
    </Grid>
</Window>
