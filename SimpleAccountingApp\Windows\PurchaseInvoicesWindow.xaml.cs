using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using SimpleAccountingApp.Helpers;

namespace SimpleAccountingApp.Windows
{
    public partial class PurchaseInvoicesWindow : Window
    {
        private ObservableCollection<PurchaseInvoice> purchaseInvoices;

        public PurchaseInvoicesWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            purchaseInvoices = new ObservableCollection<PurchaseInvoice>
            {
                new PurchaseInvoice
                {
                    InvoiceNumber = "P001",
                    InvoiceDate = DateTime.Now.AddDays(-7),
                    SupplierName = "شركة التقنية المتقدمة",
                    SubTotal = 15000,
                    DiscountAmount = 750,
                    TaxAmount = 2137.50m,
                    TotalAmount = 16387.50m,
                    PaidAmount = 16387.50m,
                    RemainingAmount = 0,
                    Status = InvoiceStatus.Paid,
                    PaymentMethod = "تحويل بنكي",
                    Notes = "فاتورة أجهزة كمبيوتر"
                },
                new PurchaseInvoice
                {
                    InvoiceNumber = "P002",
                    InvoiceDate = DateTime.Now.AddDays(-4),
                    SupplierName = "مؤسسة الخليج للمواد الغذائية",
                    SubTotal = 8500,
                    DiscountAmount = 0,
                    TaxAmount = 1275,
                    TotalAmount = 9775,
                    PaidAmount = 5000,
                    RemainingAmount = 4775,
                    Status = InvoiceStatus.PartiallyPaid,
                    PaymentMethod = "آجل",
                    Notes = "دفعة أولى"
                },
                new PurchaseInvoice
                {
                    InvoiceNumber = "P003",
                    InvoiceDate = DateTime.Now.AddDays(-2),
                    SupplierName = "محمد أحمد للتجارة",
                    SubTotal = 3200,
                    DiscountAmount = 200,
                    TaxAmount = 450,
                    TotalAmount = 3450,
                    PaidAmount = 0,
                    RemainingAmount = 3450,
                    Status = InvoiceStatus.Confirmed,
                    PaymentMethod = "آجل",
                    Notes = "قطع غيار"
                },
                new PurchaseInvoice
                {
                    InvoiceNumber = "P004",
                    InvoiceDate = DateTime.Now,
                    SupplierName = "شركة البناء الحديث",
                    SubTotal = 25000,
                    DiscountAmount = 1250,
                    TaxAmount = 3562.50m,
                    TotalAmount = 27312.50m,
                    PaidAmount = 27312.50m,
                    RemainingAmount = 0,
                    Status = InvoiceStatus.Paid,
                    PaymentMethod = "شيك",
                    Notes = "مواد بناء"
                }
            };

            InvoicesDataGrid.ItemsSource = purchaseInvoices;
        }

        private void InvoicesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            bool hasSelection = InvoicesDataGrid.SelectedItem != null;
            EditInvoiceButton.IsEnabled = hasSelection;
            DeleteInvoiceButton.IsEnabled = hasSelection;
            PrintInvoiceButton.IsEnabled = hasSelection;
        }

        private void AddInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var addInvoiceWindow = new AddPurchaseInvoiceWindow();
            if (addInvoiceWindow.ShowDialog() == true)
            {
                // الحصول على الفاتورة الجديدة من النافذة
                var newInvoice = addInvoiceWindow.NewInvoice;
                if (newInvoice != null)
                {
                    // إضافة الفاتورة الجديدة إلى القائمة
                    purchaseInvoices.Add(newInvoice);

                    // تحديث عرض البيانات
                    RefreshInvoicesGrid();

                    MessageBox.Show("تم إنشاء فاتورة المشتريات بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void RefreshInvoicesGrid()
        {
            InvoicesDataGrid.ItemsSource = null;
            InvoicesDataGrid.ItemsSource = purchaseInvoices;
        }

        private void EditInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is PurchaseInvoice selectedInvoice)
            {
                MessageBox.Show($"تعديل الفاتورة {selectedInvoice.InvoiceNumber} - قيد التطوير", "تعديل", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void DeleteInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is PurchaseInvoice selectedInvoice)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الفاتورة '{selectedInvoice.InvoiceNumber}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    purchaseInvoices.Remove(selectedInvoice);
                    MessageBox.Show("تم حذف الفاتورة بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void PrintInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is PurchaseInvoice selectedInvoice)
            {
                PrintInvoice(selectedInvoice);
            }
            else
            {
                MessageBox.Show("يرجى اختيار فاتورة للطباعة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void PrintInvoice(PurchaseInvoice invoice)
        {
            var invoiceContent = $@"
فاتورة مشتريات
================

رقم الفاتورة: {invoice.InvoiceNumber}
التاريخ: {invoice.InvoiceDate:yyyy-MM-dd}
المورد: {invoice.SupplierName}

تفاصيل الفاتورة:
-----------------
المبلغ الفرعي: {invoice.SubTotal:N2} ريال
الخصم: {invoice.DiscountAmount:N2} ريال
ضريبة القيمة المضافة (15%): {invoice.TaxAmount:N2} ريال
المبلغ الإجمالي: {invoice.TotalAmount:N2} ريال

الحالة: {GetInvoiceStatusText(invoice.Status)}
ملاحظات: {invoice.Notes}

---
الرقم الضريبي: 300123456789003
تاريخ الطباعة: {DateTime.Now:yyyy-MM-dd HH:mm}
";

            ReportHelper.PrintText(invoiceContent, $"فاتورة مشتريات - {invoice.InvoiceNumber}");
        }

        private string GetInvoiceStatusText(InvoiceStatus status)
        {
            return status switch
            {
                InvoiceStatus.Draft => "مسودة",
                InvoiceStatus.Confirmed => "مؤكدة",
                InvoiceStatus.Paid => "مدفوعة",
                InvoiceStatus.PartiallyPaid => "مدفوعة جزئياً",
                InvoiceStatus.Cancelled => "ملغاة",
                _ => "غير محدد"
            };
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            string searchText = SearchTextBox.Text.ToLower();
            if (string.IsNullOrWhiteSpace(searchText))
            {
                InvoicesDataGrid.ItemsSource = purchaseInvoices;
            }
            else
            {
                var filteredInvoices = purchaseInvoices.Where(inv =>
                    inv.InvoiceNumber.ToLowerInvariant().Contains(searchText) ||
                    inv.SupplierName.ToLowerInvariant().Contains(searchText) ||
                    inv.StatusText.ToLowerInvariant().Contains(searchText)
                ).ToList();
                
                InvoicesDataGrid.ItemsSource = filteredInvoices;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    // نموذج بيانات فاتورة المشتريات
    public class PurchaseInvoice
    {
        public string InvoiceNumber { get; set; } = "";
        public DateTime InvoiceDate { get; set; }
        public string SupplierName { get; set; } = "";
        public decimal SubTotal { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public InvoiceStatus Status { get; set; } = InvoiceStatus.Draft;
        public string PaymentMethod { get; set; } = "";
        public string Notes { get; set; } = "";

        public string StatusText => GetStatusText(Status);

        private static string GetStatusText(InvoiceStatus status)
        {
            return status switch
            {
                InvoiceStatus.Draft => "مسودة",
                InvoiceStatus.Confirmed => "مؤكدة",
                InvoiceStatus.Paid => "مدفوعة",
                InvoiceStatus.PartiallyPaid => "مدفوعة جزئياً",
                InvoiceStatus.Cancelled => "ملغاة",
                _ => "غير محدد"
            };
        }
    }

    public enum InvoiceStatus
    {
        Draft = 1,      // مسودة
        Confirmed = 2,  // مؤكدة
        Paid = 3,       // مدفوعة
        PartiallyPaid = 4, // مدفوعة جزئياً
        Cancelled = 5   // ملغاة
    }
}
