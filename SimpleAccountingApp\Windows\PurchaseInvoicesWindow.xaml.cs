using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace SimpleAccountingApp.Windows
{
    public partial class PurchaseInvoicesWindow : Window
    {
        private ObservableCollection<PurchaseInvoice> purchaseInvoices;

        public PurchaseInvoicesWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            purchaseInvoices = new ObservableCollection<PurchaseInvoice>
            {
                new PurchaseInvoice
                {
                    InvoiceNumber = "P001",
                    InvoiceDate = DateTime.Now.AddDays(-7),
                    SupplierName = "شركة التقنية المتقدمة",
                    SubTotal = 15000,
                    DiscountAmount = 750,
                    TaxAmount = 2137.50m,
                    TotalAmount = 16387.50m,
                    PaidAmount = 16387.50m,
                    RemainingAmount = 0,
                    Status = "مدفوعة",
                    PaymentMethod = "تحويل بنكي",
                    Notes = "فاتورة أجهزة كمبيوتر"
                },
                new PurchaseInvoice
                {
                    InvoiceNumber = "P002",
                    InvoiceDate = DateTime.Now.AddDays(-4),
                    SupplierName = "مؤسسة الخليج للمواد الغذائية",
                    SubTotal = 8500,
                    DiscountAmount = 0,
                    TaxAmount = 1275,
                    TotalAmount = 9775,
                    PaidAmount = 5000,
                    RemainingAmount = 4775,
                    Status = "مدفوعة جزئياً",
                    PaymentMethod = "آجل",
                    Notes = "دفعة أولى"
                },
                new PurchaseInvoice
                {
                    InvoiceNumber = "P003",
                    InvoiceDate = DateTime.Now.AddDays(-2),
                    SupplierName = "محمد أحمد للتجارة",
                    SubTotal = 3200,
                    DiscountAmount = 200,
                    TaxAmount = 450,
                    TotalAmount = 3450,
                    PaidAmount = 0,
                    RemainingAmount = 3450,
                    Status = "غير مدفوعة",
                    PaymentMethod = "آجل",
                    Notes = "قطع غيار"
                },
                new PurchaseInvoice
                {
                    InvoiceNumber = "P004",
                    InvoiceDate = DateTime.Now,
                    SupplierName = "شركة البناء الحديث",
                    SubTotal = 25000,
                    DiscountAmount = 1250,
                    TaxAmount = 3562.50m,
                    TotalAmount = 27312.50m,
                    PaidAmount = 27312.50m,
                    RemainingAmount = 0,
                    Status = "مدفوعة",
                    PaymentMethod = "شيك",
                    Notes = "مواد بناء"
                }
            };

            InvoicesDataGrid.ItemsSource = purchaseInvoices;
        }

        private void InvoicesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            bool hasSelection = InvoicesDataGrid.SelectedItem != null;
            EditInvoiceButton.IsEnabled = hasSelection;
            DeleteInvoiceButton.IsEnabled = hasSelection;
            PrintInvoiceButton.IsEnabled = hasSelection;
        }

        private void AddInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var addInvoiceWindow = new AddPurchaseInvoiceWindow();
            if (addInvoiceWindow.ShowDialog() == true)
            {
                MessageBox.Show("تم إنشاء فاتورة المشتريات بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: إعادة تحميل البيانات
            }
        }

        private void EditInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is PurchaseInvoice selectedInvoice)
            {
                MessageBox.Show($"تعديل الفاتورة {selectedInvoice.InvoiceNumber} - قيد التطوير", "تعديل", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void DeleteInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is PurchaseInvoice selectedInvoice)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الفاتورة '{selectedInvoice.InvoiceNumber}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    purchaseInvoices.Remove(selectedInvoice);
                    MessageBox.Show("تم حذف الفاتورة بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void PrintInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is PurchaseInvoice selectedInvoice)
            {
                MessageBox.Show($"طباعة الفاتورة {selectedInvoice.InvoiceNumber} - قيد التطوير", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            string searchText = SearchTextBox.Text.ToLower();
            if (string.IsNullOrWhiteSpace(searchText))
            {
                InvoicesDataGrid.ItemsSource = purchaseInvoices;
            }
            else
            {
                var filteredInvoices = purchaseInvoices.Where(inv => 
                    inv.InvoiceNumber.ToLower().Contains(searchText) ||
                    inv.SupplierName.ToLower().Contains(searchText) ||
                    inv.Status.ToLower().Contains(searchText)
                ).ToList();
                
                InvoicesDataGrid.ItemsSource = filteredInvoices;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    // نموذج بيانات فاتورة المشتريات
    public class PurchaseInvoice
    {
        public string InvoiceNumber { get; set; } = "";
        public DateTime InvoiceDate { get; set; }
        public string SupplierName { get; set; } = "";
        public decimal SubTotal { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public string Status { get; set; } = "";
        public string PaymentMethod { get; set; } = "";
        public string Notes { get; set; } = "";
        
        public string StatusText => Status;
    }
}
