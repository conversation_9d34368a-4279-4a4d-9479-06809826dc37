===============================================
    نظام المحاسبة المالية المتكامل
    Comprehensive Financial Accounting System
===============================================

🎉 مرحباً بك في نظام المحاسبة المالية المتكامل!

📋 محتويات المجلد:
==================
- SimpleAccountingApp.exe  ← الملف التنفيذي الرئيسي
- ملفات النظام المطلوبة للتشغيل
- README.txt ← هذا الملف

🚀 كيفية التشغيل:
=================
1. انقر نقراً مزدوجاً على "SimpleAccountingApp.exe"
2. سيفتح التطبيق مباشرة بدون الحاجة لتثبيت

✨ الوظائف المتاحة:
==================

📊 دليل الحسابات:
- عرض هرمي للحسابات (أصول، خصوم، حقوق ملكية، إيرادات، مصروفات)
- إضافة وتعديل وحذف الحسابات
- تفاصيل كاملة مع الأرصدة
- بحث متقدم

👥 إدارة العملاء:
- قائمة شاملة بالعملاء
- إضافة وتعديل وحذف العملاء
- أنواع العملاء (فرد، شركة، مؤسسة)
- حدود الائتمان ومدة السداد
- بحث وفلترة

🏢 إدارة الموردين:
- قائمة شاملة بالموردين
- إضافة وتعديل وحذف الموردين
- تتبع الأرصدة والمستحقات
- بحث متقدم

📦 إدارة الأصناف:
- قائمة شاملة بالأصناف
- إدارة الفئات والوحدات
- تتبع أسعار الشراء والبيع
- حساب هامش الربح تلقائياً
- تتبع مستويات المخزون
- تنبيهات المخزون المنخفض

🧾 فواتير المبيعات:
- إنشاء فواتير مبيعات جديدة
- إضافة أصناف متعددة للفاتورة
- حساب الضرائب والخصومات تلقائياً
- تتبع حالة الدفع (مدفوعة، جزئياً، غير مدفوعة)
- طرق دفع متنوعة
- بحث وفلترة الفواتير

📋 فواتير المشتريات:
- إنشاء فواتير مشتريات جديدة
- إضافة أصناف متعددة للفاتورة
- ربط بالموردين مع تفاصيل كاملة
- تتبع المبالغ المدفوعة والمتبقية
- حالات الدفع المختلفة
- بحث متقدم في الفواتير

🏪 إدارة المخازن:
- إضافة وإدارة المخازن المتعددة
- تتبع المواقع والمسؤولين
- حساب قيمة المخزون لكل مخزن
- إدارة المساحات والسعات التخزينية
- تقارير حركة المخزون التفصيلية

📈 التقارير المالية الشاملة:
- قائمة الدخل مع تفاصيل الإيرادات والمصروفات
- الميزانية العمومية (الأصول والخصوم)
- تقارير ملخص المبيعات والمشتريات
- تقارير العملاء والموردين
- تقارير المخزون والتقييم

📈 تقرير المخزون:
- ملخص إحصائي شامل
- عرض حالة المخزون لكل صنف
- قيمة المخزون الإجمالية
- تحديد الأصناف منخفضة المخزون

👤 نظام المستخدمين:
===================
- شاشة تسجيل دخول احترافية مع بيانات افتراضية
- إدارة المستخدمين مع الأدوار والصلاحيات
- إنشاء مستخدمين جدد مع تحديد الأقسام
- 4 مستخدمين تجريبيين بأدوار مختلفة
- نظام صلاحيات متقدم

🏛️ نظام الزكاة والضرائب السعودي:
===================================
- ربط كامل مع هيئة الزكاة والضرائب والجمارك
- حاسبة ضريبة القيمة المضافة (15%)
- حاسبة الزكاة مع النصاب الشرعي
- إدارة التسجيل الضريبي
- الإقرارات الضريبية الإلكترونية
- الفوترة الإلكترونية ورموز QR
- التقارير الضريبية المتوافقة

🎯 آخر التحديثات في هذا الإصدار:
=====================================
✅ **إصلاح جذري لوضوح النصوص والأزرار** - خط أسود عريض حجم 20 مع حدود سوداء
✅ **تنفيذ قاعدة بيانات SQLite فعلية** - حفظ دائم للبيانات
✅ **إصلاح مشكلة حفظ العملاء والموردين** - حفظ فعلي في قاعدة البيانات
✅ **إصلاح مشكلة عرض البيانات المحفوظة** - تحميل من قاعدة البيانات عند فتح الشاشات
✅ **تفعيل وظائف الطباعة والتصدير** - طباعة التقارير وتصدير Excel
✅ **تحسين التحقق من صحة البيانات** - حقول اختيارية ومرونة أكبر
✅ **تفعيل طباعة الميزانية العمومية** - تقرير مفصل قابل للطباعة
✅ **تفعيل طباعة وتصدير ملخص المبيعات** - تقارير Excel منسقة
✅ **تحسين رسائل الخطأ** - رسائل توضيحية أفضل للمستخدم
✅ تحسين وضوح جميع النصوص والأزرار في التطبيق
✅ إضافة نظام الزكاة والضرائب السعودي المتكامل
✅ إضافة حاسبة ضريبة القيمة المضافة (15%)
✅ إضافة حاسبة الزكاة بالنصاب الشرعي
✅ تحسين نظام الخصم في الفواتير
✅ إضافة إدارة المستخدمين الكاملة
✅ إصلاح مشكلة إضافة الحسابات المالية الجديدة
✅ تحسين واجهة المستخدم وسهولة الاستخدام
✅ إضافة التكامل مع نظام الفوترة الإلكترونية
✅ إضافة مولد رمز QR للفواتير
✅ تحسين أمان النظام وحماية البيانات

🎯 البيانات التجريبية:
======================
التطبيق يحتوي على بيانات تجريبية جاهزة:
- دليل حسابات كامل ومنظم
- 4 عملاء بأنواع مختلفة
- 4 موردين بتخصصات متنوعة
- 5 أصناف مع تتبع المخزون
- 4 مخازن بمواقع مختلفة
- 4 فواتير مبيعات بحالات مختلفة
- 4 فواتير مشتريات مع تفاصيل كاملة
- 6 حركات مخزون متنوعة
- 4 مستخدمين بأدوار مختلفة
- تقارير مالية شاملة بأرقام واقعية

🔐 بيانات تسجيل الدخول الافتراضية:
====================================
اسم المستخدم: admin
كلمة المرور: 123456

مستخدمين آخرين:
- محاسب / password
- مدير / manager123
- مستخدم / user123

🚀 كيفية الاستخدام:
==================
1. شغل الملف التنفيذي SimpleAccountingApp.exe
2. ستظهر شاشة تسجيل الدخول أولاً - النصوص واضحة بخط أسود عريض حجم 18
3. سجل الدخول باستخدام البيانات الافتراضية
4. بعد رسالة الترحيب، ستفتح الشاشة الرئيسية تلقائياً في المقدمة
5. جميع الأزرار والنصوص الآن واضحة بخط أسود عريض حجم 18
6. مربعات النص واضحة جداً بخط أسود عريض على خلفية بيضاء
7. استكشف القائمة الجانبية للوصول لجميع الوظائف (أزرار بيضاء مع حدود)
8. أضف موردين وعملاء وأصناف جديدة - ستظهر فوراً في القوائم
9. أضف فواتير مبيعات ومشتريات جديدة - ستظهر فوراً في قوائم الفواتير
10. استخدم أزرار الطباعة والتصدير في التقارير - تعمل بشكل كامل
11. جرب نظام الزكاة والضرائب السعودي الجديد
12. استخدم حاسبات الضرائب والزكاة المتقدمة
13. أضف مستخدمين جدد من قسم إدارة المستخدمين
14. أضف حسابات مالية جديدة - ستكون منفصلة عن الحسابات القديمة
15. في الفواتير: أدخل الخصم وسيتم حسابه تلقائياً مع الضرائب
16. استخدم التقارير لمتابعة أداء عملك

💡 نصائح للاستخدام:
===================
1. ابدأ بتسجيل الدخول - ستظهر شاشة تسجيل الدخول أولاً قبل الشاشة الرئيسية
2. النصوص في شاشة تسجيل الدخول واضحة بخط أسود عريض حجم 18 على خلفية بيضاء
3. بعد تسجيل الدخول الناجح، ستفتح الشاشة الرئيسية تلقائياً
4. جميع الأزرار في التطبيق الآن واضحة بخط أسود عريض حجم 18
5. أزرار القائمة الجانبية بيضاء مع حدود زرقاء عند التمرير
6. مربعات النص واضحة جداً بخط أسود عريض حجم 18 على خلفية بيضاء
7. عند إضافة مورد جديد، سيظهر فوراً في قائمة الموردين
8. عند إضافة عميل جديد، سيظهر فوراً في قائمة العملاء
9. عند إضافة صنف جديد، سيظهر فوراً في قائمة الأصناف
10. عند إضافة فاتورة مبيعات جديدة، ستظهر فوراً في قائمة فواتير المبيعات
11. عند إضافة فاتورة مشتريات جديدة، ستظهر فوراً في قائمة فواتير المشتريات
12. **لإضافة فاتورة:** اضغط "إضافة فاتورة" → أدخل البيانات → اضغط "إضافة صنف" → احفظ
13. **حقول اختيارية:** حد الائتمان ومدة السداد للعملاء والموردين اختيارية
14. عند إضافة حساب مالي جديد، سيكون منفصل تماماً عن الحسابات القديمة
15. استخدم أزرار الطباعة في التقارير - تعمل بشكل كامل
16. استخدم أزرار التصدير إلى Excel - تعمل بشكل كامل
15. استخدم البحث للعثور على البيانات
16. اطلع على تقرير المخزون لرؤية الإحصائيات
17. جرب تعديل وحذف البيانات الموجودة
18. في الفواتير: أدخل الكمية والخصم وسيتم الحساب تلقائياً
19. الضرائب تحسب تلقائياً بمعدل 15% (ضريبة القيمة المضافة السعودية)
20. استخدم حاسبة ضريبة القيمة المضافة المنفصلة
21. احسب الزكاة باستخدام النصاب الشرعي (17,000 ريال)
22. جميع الحسابات مرتبطة بنظام الزكاة والضرائب السعودي

🔧 متطلبات النظام:
==================
- Windows 7 أو أحدث
- .NET Runtime (مدمج في التطبيق)
- ذاكرة: 512 MB RAM أو أكثر
- مساحة القرص: 100 MB

🎨 المميزات:
=============
✅ واجهة عربية احترافية مع دعم RTL
✅ تصميم Material Design عصري
✅ بيانات تجريبية واقعية
✅ التحقق من صحة البيانات
✅ بحث وفلترة متقدمة
✅ حسابات تلقائية للأرباح
✅ تنبيهات ذكية للمخزون
✅ سهولة في الاستخدام

📞 الدعم:
==========
هذا تطبيق تجريبي تم تطويره لأغراض التعلم والاختبار.

🎊 استمتع باستخدام نظام المحاسبة المالية!

===============================================
© 2025 نظام المحاسبة المالية - جميع الحقوق محفوظة
===============================================
