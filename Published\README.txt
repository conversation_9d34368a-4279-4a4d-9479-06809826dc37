===============================================
    نظام المحاسبة المالية المتكامل
    Comprehensive Financial Accounting System
===============================================

🎉 مرحباً بك في نظام المحاسبة المالية المتكامل!

📋 محتويات المجلد:
==================
- SimpleAccountingApp.exe  ← الملف التنفيذي الرئيسي
- ملفات النظام المطلوبة للتشغيل
- README.txt ← هذا الملف

🚀 كيفية التشغيل:
=================
1. انقر نقراً مزدوجاً على "SimpleAccountingApp.exe"
2. سيفتح التطبيق مباشرة بدون الحاجة لتثبيت

✨ الوظائف المتاحة:
==================

📊 دليل الحسابات:
- عرض هرمي للحسابات (أصول، خصوم، حقوق ملكية، إيرادات، مصروفات)
- إضافة وتعديل وحذف الحسابات
- تفاصيل كاملة مع الأرصدة
- بحث متقدم

👥 إدارة العملاء:
- قائمة شاملة بالعملاء
- إضافة وتعديل وحذف العملاء
- أنواع العملاء (فرد، شركة، مؤسسة)
- حدود الائتمان ومدة السداد
- بحث وفلترة

🏢 إدارة الموردين:
- قائمة شاملة بالموردين
- إضافة وتعديل وحذف الموردين
- تتبع الأرصدة والمستحقات
- بحث متقدم

📦 إدارة الأصناف:
- قائمة شاملة بالأصناف
- إدارة الفئات والوحدات
- تتبع أسعار الشراء والبيع
- حساب هامش الربح تلقائياً
- تتبع مستويات المخزون
- تنبيهات المخزون المنخفض

🧾 فواتير المبيعات:
- إنشاء فواتير مبيعات جديدة
- إضافة أصناف متعددة للفاتورة
- حساب الضرائب والخصومات تلقائياً
- تتبع حالة الدفع (مدفوعة، جزئياً، غير مدفوعة)
- طرق دفع متنوعة
- بحث وفلترة الفواتير

📋 فواتير المشتريات:
- إنشاء فواتير مشتريات جديدة
- إضافة أصناف متعددة للفاتورة
- ربط بالموردين مع تفاصيل كاملة
- تتبع المبالغ المدفوعة والمتبقية
- حالات الدفع المختلفة
- بحث متقدم في الفواتير

🏪 إدارة المخازن:
- إضافة وإدارة المخازن المتعددة
- تتبع المواقع والمسؤولين
- حساب قيمة المخزون لكل مخزن
- إدارة المساحات والسعات التخزينية
- تقارير حركة المخزون التفصيلية

📈 التقارير المالية الشاملة:
- قائمة الدخل مع تفاصيل الإيرادات والمصروفات
- الميزانية العمومية (الأصول والخصوم)
- تقارير ملخص المبيعات والمشتريات
- تقارير العملاء والموردين
- تقارير المخزون والتقييم

📈 تقرير المخزون:
- ملخص إحصائي شامل
- عرض حالة المخزون لكل صنف
- قيمة المخزون الإجمالية
- تحديد الأصناف منخفضة المخزون

👤 نظام المستخدمين:
===================
- شاشة تسجيل دخول احترافية مع بيانات افتراضية
- إدارة المستخدمين مع الأدوار والصلاحيات
- إنشاء مستخدمين جدد مع تحديد الأقسام
- 4 مستخدمين تجريبيين بأدوار مختلفة
- نظام صلاحيات متقدم

🏛️ نظام الزكاة والضرائب السعودي:
===================================
- ربط كامل مع هيئة الزكاة والضرائب والجمارك
- حاسبة ضريبة القيمة المضافة (15%)
- حاسبة الزكاة مع النصاب الشرعي
- إدارة التسجيل الضريبي
- الإقرارات الضريبية الإلكترونية
- الفوترة الإلكترونية ورموز QR
- التقارير الضريبية المتوافقة

🎯 البيانات التجريبية:
======================
التطبيق يحتوي على بيانات تجريبية جاهزة:
- دليل حسابات كامل ومنظم
- 4 عملاء بأنواع مختلفة
- 4 موردين بتخصصات متنوعة
- 5 أصناف مع تتبع المخزون
- 4 مخازن بمواقع مختلفة
- 4 فواتير مبيعات بحالات مختلفة
- 4 فواتير مشتريات مع تفاصيل كاملة
- 6 حركات مخزون متنوعة
- 4 مستخدمين بأدوار مختلفة
- تقارير مالية شاملة بأرقام واقعية

🔐 بيانات تسجيل الدخول الافتراضية:
====================================
اسم المستخدم: admin
كلمة المرور: 123456

مستخدمين آخرين:
- محاسب / password
- مدير / manager123
- مستخدم / user123

🚀 كيفية الاستخدام:
==================
1. شغل الملف التنفيذي SimpleAccountingApp.exe
2. ستظهر شاشة تسجيل الدخول أولاً - سجل الدخول باستخدام البيانات الافتراضية
3. بعد تسجيل الدخول الناجح، ستفتح الشاشة الرئيسية
4. استكشف القائمة الجانبية للوصول لجميع الوظائف (النصوص الآن واضحة باللون الأسود)
5. جرب نظام الزكاة والضرائب السعودي الجديد
6. استخدم حاسبات الضرائب والزكاة المتقدمة
7. أضف مستخدمين جدد من قسم إدارة المستخدمين
8. أضف حسابات مالية جديدة - ستكون منفصلة عن الحسابات القديمة
9. في الفواتير: أدخل الخصم وسيتم حسابه تلقائياً مع الضرائب
10. استخدم التقارير لمتابعة أداء عملك

💡 نصائح للاستخدام:
===================
1. ابدأ بتسجيل الدخول - ستظهر شاشة تسجيل الدخول أولاً قبل الشاشة الرئيسية
2. استكشف جميع القوائم في الجانب الأيسر (النصوص واضحة باللون الأسود)
3. جرب إضافة بيانات جديدة في كل قسم
4. عند إضافة حساب مالي جديد، سيكون منفصل تماماً عن الحسابات القديمة
5. استخدم البحث للعثور على البيانات
6. اطلع على تقرير المخزون لرؤية الإحصائيات
7. جرب تعديل وحذف البيانات الموجودة
8. في الفواتير: أدخل الكمية والخصم وسيتم الحساب تلقائياً
9. الضرائب تحسب تلقائياً بمعدل 15% (ضريبة القيمة المضافة السعودية)
10. استخدم حاسبة ضريبة القيمة المضافة المنفصلة
11. احسب الزكاة باستخدام النصاب الشرعي (17,000 ريال)
12. جميع الحسابات مرتبطة بنظام الزكاة والضرائب السعودي

🔧 متطلبات النظام:
==================
- Windows 7 أو أحدث
- .NET Runtime (مدمج في التطبيق)
- ذاكرة: 512 MB RAM أو أكثر
- مساحة القرص: 100 MB

🎨 المميزات:
=============
✅ واجهة عربية احترافية مع دعم RTL
✅ تصميم Material Design عصري
✅ بيانات تجريبية واقعية
✅ التحقق من صحة البيانات
✅ بحث وفلترة متقدمة
✅ حسابات تلقائية للأرباح
✅ تنبيهات ذكية للمخزون
✅ سهولة في الاستخدام

📞 الدعم:
==========
هذا تطبيق تجريبي تم تطويره لأغراض التعلم والاختبار.

🎊 استمتع باستخدام نظام المحاسبة المالية!

===============================================
© 2025 نظام المحاسبة المالية - جميع الحقوق محفوظة
===============================================
