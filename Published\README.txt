===============================================
    نظام المحاسبة المالية المتكامل
    Comprehensive Financial Accounting System
===============================================

🎉 مرحباً بك في نظام المحاسبة المالية المتكامل!

📋 محتويات المجلد:
==================
- SimpleAccountingApp.exe  ← الملف التنفيذي الرئيسي
- ملفات النظام المطلوبة للتشغيل
- README.txt ← هذا الملف

🚀 كيفية التشغيل:
=================
1. انقر نقراً مزدوجاً على "SimpleAccountingApp.exe"
2. سيفتح التطبيق مباشرة بدون الحاجة لتثبيت

✨ الوظائف المتاحة:
==================

📊 دليل الحسابات:
- عرض هرمي للحسابات (أصول، خصوم، حقوق ملكية، إيرادات، مصروفات)
- إضافة وتعديل وحذف الحسابات
- تفاصيل كاملة مع الأرصدة
- بحث متقدم

👥 إدارة العملاء:
- قائمة شاملة بالعملاء
- إضافة وتعديل وحذف العملاء
- أنواع العملاء (فرد، شركة، مؤسسة)
- حدود الائتمان ومدة السداد
- بحث وفلترة

🏢 إدارة الموردين:
- قائمة شاملة بالموردين
- إضافة وتعديل وحذف الموردين
- تتبع الأرصدة والمستحقات
- بحث متقدم

📦 إدارة الأصناف:
- قائمة شاملة بالأصناف
- إدارة الفئات والوحدات
- تتبع أسعار الشراء والبيع
- حساب هامش الربح تلقائياً
- تتبع مستويات المخزون
- تنبيهات المخزون المنخفض

🧾 فواتير المبيعات:
- إنشاء فواتير مبيعات جديدة
- إضافة أصناف متعددة للفاتورة
- حساب الضرائب والخصومات تلقائياً
- تتبع حالة الدفع (مدفوعة، جزئياً، غير مدفوعة)
- طرق دفع متنوعة
- بحث وفلترة الفواتير

📋 فواتير المشتريات:
- إنشاء فواتير مشتريات جديدة
- إضافة أصناف متعددة للفاتورة
- ربط بالموردين مع تفاصيل كاملة
- تتبع المبالغ المدفوعة والمتبقية
- حالات الدفع المختلفة
- بحث متقدم في الفواتير

🏪 إدارة المخازن:
- إضافة وإدارة المخازن المتعددة
- تتبع المواقع والمسؤولين
- حساب قيمة المخزون لكل مخزن
- إدارة المساحات والسعات التخزينية
- تقارير حركة المخزون التفصيلية

📈 التقارير المالية الشاملة:
- قائمة الدخل مع تفاصيل الإيرادات والمصروفات
- الميزانية العمومية (الأصول والخصوم)
- تقارير ملخص المبيعات والمشتريات
- تقارير العملاء والموردين
- تقارير المخزون والتقييم

📈 تقرير المخزون:
- ملخص إحصائي شامل
- عرض حالة المخزون لكل صنف
- قيمة المخزون الإجمالية
- تحديد الأصناف منخفضة المخزون

👤 نظام المستخدمين:
===================
- شاشة تسجيل دخول احترافية مع بيانات افتراضية
- إدارة المستخدمين مع الأدوار والصلاحيات
- إنشاء مستخدمين جدد مع تحديد الأقسام
- 4 مستخدمين تجريبيين بأدوار مختلفة
- نظام صلاحيات متقدم

🏛️ نظام الزكاة والضرائب السعودي:
===================================
- ربط كامل مع هيئة الزكاة والضرائب والجمارك
- حاسبة ضريبة القيمة المضافة (15%)
- حاسبة الزكاة مع النصاب الشرعي
- إدارة التسجيل الضريبي
- الإقرارات الضريبية الإلكترونية
- الفوترة الإلكترونية ورموز QR
- التقارير الضريبية المتوافقة

🎯 البيانات التجريبية:
======================
التطبيق يحتوي على بيانات تجريبية جاهزة:
- دليل حسابات كامل ومنظم
- 4 عملاء بأنواع مختلفة
- 4 موردين بتخصصات متنوعة
- 5 أصناف مع تتبع المخزون
- 4 مخازن بمواقع مختلفة
- 4 فواتير مبيعات بحالات مختلفة
- 4 فواتير مشتريات مع تفاصيل كاملة
- 6 حركات مخزون متنوعة
- 4 مستخدمين بأدوار مختلفة
- تقارير مالية شاملة بأرقام واقعية

🔐 بيانات تسجيل الدخول الافتراضية:
====================================
اسم المستخدم: admin
كلمة المرور: 123456

مستخدمين آخرين:
- محاسب / password
- مدير / manager123
- مستخدم / user123

🚀 كيفية الاستخدام:
==================
1. شغل الملف التنفيذي SimpleAccountingApp.exe
2. سجل الدخول باستخدام البيانات الافتراضية أو اضغط "تسجيل الدخول"
3. استكشف القائمة الجانبية للوصول لجميع الوظائف (النصوص الآن واضحة باللون الأسود)
4. جرب نظام الزكاة والضرائب السعودي الجديد
5. استخدم حاسبات الضرائب والزكاة المتقدمة
6. أضف مستخدمين جدد من قسم إدارة المستخدمين
7. ابدأ بإدخال بياناتك أو استخدم البيانات التجريبية
8. في الفواتير: أدخل الخصم وسيتم حسابه تلقائياً مع الضرائب
9. استخدم التقارير لمتابعة أداء عملك

💡 نصائح للاستخدام:
===================
1. استكشف جميع القوائم في الجانب الأيسر (النصوص واضحة باللون الأسود)
2. جرب إضافة بيانات جديدة في كل قسم
3. استخدم البحث للعثور على البيانات
4. اطلع على تقرير المخزون لرؤية الإحصائيات
5. جرب تعديل وحذف البيانات الموجودة
6. في الفواتير: أدخل الكمية والخصم وسيتم الحساب تلقائياً
7. الضرائب تحسب تلقائياً بمعدل 15% (ضريبة القيمة المضافة السعودية)
8. استخدم حاسبة ضريبة القيمة المضافة المنفصلة
9. احسب الزكاة باستخدام النصاب الشرعي (17,000 ريال)
10. جميع الحسابات مرتبطة بنظام الزكاة والضرائب السعودي

🔧 متطلبات النظام:
==================
- Windows 7 أو أحدث
- .NET Runtime (مدمج في التطبيق)
- ذاكرة: 512 MB RAM أو أكثر
- مساحة القرص: 100 MB

🎨 المميزات:
=============
✅ واجهة عربية احترافية مع دعم RTL
✅ تصميم Material Design عصري
✅ بيانات تجريبية واقعية
✅ التحقق من صحة البيانات
✅ بحث وفلترة متقدمة
✅ حسابات تلقائية للأرباح
✅ تنبيهات ذكية للمخزون
✅ سهولة في الاستخدام

📞 الدعم:
==========
هذا تطبيق تجريبي تم تطويره لأغراض التعلم والاختبار.

🎊 استمتع باستخدام نظام المحاسبة المالية!

===============================================
© 2025 نظام المحاسبة المالية - جميع الحقوق محفوظة
===============================================
