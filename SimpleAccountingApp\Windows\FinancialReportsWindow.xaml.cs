using System.Windows;

namespace SimpleAccountingApp.Windows
{
    public partial class FinancialReportsWindow : Window
    {
        public FinancialReportsWindow()
        {
            InitializeComponent();
        }

        // Financial Reports
        private void IncomeStatementButton_Click(object sender, RoutedEventArgs e)
        {
            var incomeStatementWindow = new IncomeStatementWindow();
            incomeStatementWindow.ShowDialog();
        }

        private void BalanceSheetButton_Click(object sender, RoutedEventArgs e)
        {
            var balanceSheetWindow = new BalanceSheetWindow();
            balanceSheetWindow.ShowDialog();
        }

        private void CashFlowButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("تقرير التدفقات النقدية - قيد التطوير", "التدفقات النقدية", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // Sales Reports
        private void SalesSummaryButton_Click(object sender, RoutedEventArgs e)
        {
            var salesSummaryWindow = new SalesSummaryWindow();
            salesSummaryWindow.ShowDialog();
        }

        private void CustomerSalesButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("تقرير مبيعات العملاء - قيد التطوير", "مبيعات العملاء", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ProductSalesButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("تقرير مبيعات الأصناف - قيد التطوير", "مبيعات الأصناف", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // Purchase Reports
        private void PurchaseSummaryButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("تقرير ملخص المشتريات - قيد التطوير", "ملخص المشتريات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void SupplierPurchasesButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("تقرير مشتريات الموردين - قيد التطوير", "مشتريات الموردين", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AccountsPayableButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("تقرير الحسابات الدائنة - قيد التطوير", "الحسابات الدائنة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // Inventory Reports
        private void StockValuationButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("تقرير تقييم المخزون - قيد التطوير", "تقييم المخزون", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void StockMovementReportButton_Click(object sender, RoutedEventArgs e)
        {
            var stockMovementWindow = new StockMovementWindow();
            stockMovementWindow.ShowDialog();
        }

        private void LowStockButton_Click(object sender, RoutedEventArgs e)
        {
            var stockReportWindow = new StockReportWindow(new List<Product>
            {
                new Product { ProductCode = "P001", ProductName = "لابتوب ديل إنسبايرون", CurrentStock = 15, MinimumStock = 5, PurchasePrice = 2500 },
                new Product { ProductCode = "P002", ProductName = "طابعة HP ليزر", CurrentStock = 8, MinimumStock = 3, PurchasePrice = 800 },
                new Product { ProductCode = "P003", ProductName = "ورق A4", CurrentStock = 2, MinimumStock = 10, PurchasePrice = 25 },
                new Product { ProductCode = "P004", ProductName = "ماوس لاسلكي", CurrentStock = 25, MinimumStock = 10, PurchasePrice = 45 },
                new Product { ProductCode = "P005", ProductName = "كيبورد ميكانيكي", CurrentStock = 0, MinimumStock = 5, PurchasePrice = 120 }
            });
            stockReportWindow.ShowDialog();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
