using System.Windows;
using System.Windows.Controls;
using SimpleAccountingApp.Models;

namespace SimpleAccountingApp.Windows
{
    public partial class AddProductWindow : Window
    {
        private Product? _editingProduct;
        private bool _isEditMode;

        public Product? NewProduct { get; private set; }

        public AddProductWindow()
        {
            InitializeComponent();
            _isEditMode = false;
            GenerateProductCode();
            UpdateProfitCalculation();
        }

        public AddProductWindow(Product productToEdit)
        {
            InitializeComponent();
            _editingProduct = productToEdit;
            _isEditMode = true;
            
            WindowTitle.Text = "تعديل الصنف";
            this.Title = "تعديل الصنف - نظام المحاسبة المالية";
            
            LoadProductData();
            UpdateProfitCalculation();
        }

        private void GenerateProductCode()
        {
            // إنشاء رقم صنف تلقائي
            Random random = new Random();
            ProductCodeTextBox.Text = "P" + (random.Next(100, 999)).ToString();
        }

        private void LoadProductData()
        {
            if (_editingProduct != null)
            {
                ProductCodeTextBox.Text = _editingProduct.ProductCode;
                ProductNameTextBox.Text = _editingProduct.ProductName;
                BarcodeTextBox.Text = _editingProduct.Barcode;
                
                // تحديد الفئة
                CategoryComboBox.Text = _editingProduct.Category;
                
                // تحديد الوحدة
                UnitComboBox.Text = _editingProduct.Unit;
                
                PurchasePriceTextBox.Text = _editingProduct.PurchasePrice.ToString();
                SalePriceTextBox.Text = _editingProduct.SalePrice.ToString();
                CurrentStockTextBox.Text = _editingProduct.CurrentStock.ToString();
                MinimumStockTextBox.Text = _editingProduct.MinimumStock.ToString();
                MaximumStockTextBox.Text = _editingProduct.MaximumStock.ToString();
                DescriptionTextBox.Text = _editingProduct.Description;
                IsActiveCheckBox.IsChecked = _editingProduct.IsActive;
            }
        }

        private void PriceTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateProfitCalculation();
        }

        private void UpdateProfitCalculation()
        {
            if (decimal.TryParse(PurchasePriceTextBox.Text, out decimal purchasePrice) &&
                decimal.TryParse(SalePriceTextBox.Text, out decimal salePrice))
            {
                decimal profitMargin = salePrice - purchasePrice;
                decimal profitPercentage = purchasePrice > 0 ? (profitMargin / purchasePrice) * 100 : 0;
                
                ProfitMarginText.Text = $"هامش الربح: {profitMargin:N2} ريال";
                ProfitPercentageText.Text = $"نسبة الربح: {profitPercentage:N2}%";
                
                // تغيير لون النص حسب الربح
                if (profitMargin > 0)
                {
                    ProfitMarginText.Foreground = System.Windows.Media.Brushes.Green;
                    ProfitPercentageText.Foreground = System.Windows.Media.Brushes.Green;
                }
                else if (profitMargin < 0)
                {
                    ProfitMarginText.Foreground = System.Windows.Media.Brushes.Red;
                    ProfitPercentageText.Foreground = System.Windows.Media.Brushes.Red;
                }
                else
                {
                    ProfitMarginText.Foreground = System.Windows.Media.Brushes.Black;
                    ProfitPercentageText.Foreground = System.Windows.Media.Brushes.Black;
                }
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                if (_isEditMode)
                {
                    UpdateProduct();
                }
                else
                {
                    CreateNewProduct();
                }
                
                this.DialogResult = true;
                this.Close();
            }
        }

        private bool ValidateInput()
        {
            // التحقق من رقم الصنف
            if (string.IsNullOrWhiteSpace(ProductCodeTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الصنف", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                ProductCodeTextBox.Focus();
                return false;
            }

            // التحقق من اسم الصنف
            if (string.IsNullOrWhiteSpace(ProductNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الصنف", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                ProductNameTextBox.Focus();
                return false;
            }

            // التحقق من الفئة
            if (string.IsNullOrWhiteSpace(CategoryComboBox.Text))
            {
                MessageBox.Show("يرجى اختيار فئة الصنف", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CategoryComboBox.Focus();
                return false;
            }

            // التحقق من الوحدة
            if (string.IsNullOrWhiteSpace(UnitComboBox.Text))
            {
                MessageBox.Show("يرجى اختيار وحدة الصنف", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                UnitComboBox.Focus();
                return false;
            }

            // التحقق من سعر الشراء
            if (!decimal.TryParse(PurchasePriceTextBox.Text, out decimal purchasePrice) || purchasePrice < 0)
            {
                MessageBox.Show("يرجى إدخال سعر شراء صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                PurchasePriceTextBox.Focus();
                return false;
            }

            // التحقق من سعر البيع
            if (!decimal.TryParse(SalePriceTextBox.Text, out decimal salePrice) || salePrice < 0)
            {
                MessageBox.Show("يرجى إدخال سعر بيع صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                SalePriceTextBox.Focus();
                return false;
            }

            // التحقق من الكميات
            if (!decimal.TryParse(CurrentStockTextBox.Text, out _))
            {
                MessageBox.Show("يرجى إدخال كمية حالية صحيحة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CurrentStockTextBox.Focus();
                return false;
            }

            if (!decimal.TryParse(MinimumStockTextBox.Text, out _))
            {
                MessageBox.Show("يرجى إدخال حد أدنى صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                MinimumStockTextBox.Focus();
                return false;
            }

            if (!decimal.TryParse(MaximumStockTextBox.Text, out _))
            {
                MessageBox.Show("يرجى إدخال حد أقصى صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                MaximumStockTextBox.Focus();
                return false;
            }

            return true;
        }

        private void CreateNewProduct()
        {
            NewProduct = new Product
            {
                ProductCode = ProductCodeTextBox.Text.Trim(),
                ProductName = ProductNameTextBox.Text.Trim(),
                Barcode = BarcodeTextBox.Text.Trim(),
                Category = CategoryComboBox.Text.Trim(),
                Unit = UnitComboBox.Text.Trim(),
                PurchasePrice = decimal.Parse(PurchasePriceTextBox.Text),
                SalePrice = decimal.Parse(SalePriceTextBox.Text),
                CurrentStock = decimal.Parse(CurrentStockTextBox.Text),
                MinimumStock = decimal.Parse(MinimumStockTextBox.Text),
                MaximumStock = decimal.Parse(MaximumStockTextBox.Text),
                Description = DescriptionTextBox.Text.Trim(),
                IsActive = IsActiveCheckBox.IsChecked ?? true
            };

            // في التطبيق الحقيقي، سيتم حفظ الصنف في قاعدة البيانات
            // TODO: حفظ في قاعدة البيانات
        }

        private void UpdateProduct()
        {
            if (_editingProduct != null)
            {
                _editingProduct.ProductCode = ProductCodeTextBox.Text.Trim();
                _editingProduct.ProductName = ProductNameTextBox.Text.Trim();
                _editingProduct.Barcode = BarcodeTextBox.Text.Trim();
                _editingProduct.Category = CategoryComboBox.Text.Trim();
                _editingProduct.Unit = UnitComboBox.Text.Trim();
                _editingProduct.PurchasePrice = decimal.Parse(PurchasePriceTextBox.Text);
                _editingProduct.SalePrice = decimal.Parse(SalePriceTextBox.Text);
                _editingProduct.CurrentStock = decimal.Parse(CurrentStockTextBox.Text);
                _editingProduct.MinimumStock = decimal.Parse(MinimumStockTextBox.Text);
                _editingProduct.MaximumStock = decimal.Parse(MaximumStockTextBox.Text);
                _editingProduct.Description = DescriptionTextBox.Text.Trim();
                _editingProduct.IsActive = IsActiveCheckBox.IsChecked ?? true;

                // في التطبيق الحقيقي، سيتم تحديث الصنف في قاعدة البيانات
                // TODO: تحديث في قاعدة البيانات
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            this.DialogResult = false;
            this.Close();
        }
    }
}
