<Window x:Class="SimpleAccountingApp.Windows.SelectProductWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="اختيار صنف - نظام المحاسبة المالية" 
        Height="500" Width="700"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="50"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" Padding="15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📦" FontSize="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock Text="اختيار صنف للفاتورة" 
                          FontSize="18" 
                          FontWeight="Bold" 
                          Foreground="White" 
                          VerticalAlignment="Center"/>
            </StackPanel>
        </Border>
        
        <!-- Search -->
        <Border Grid.Row="1" Background="#F5F5F5" Padding="10" BorderBrush="#DDD" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="البحث:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBox x:Name="SearchTextBox" 
                        Width="300" 
                        Height="30"
                        Padding="5"
                        TextChanged="SearchTextBox_TextChanged"/>
            </StackPanel>
        </Border>
        
        <!-- Products List -->
        <DataGrid x:Name="ProductsDataGrid" 
                 Grid.Row="2"
                 Margin="10"
                 AutoGenerateColumns="False"
                 CanUserAddRows="False"
                 CanUserDeleteRows="False"
                 IsReadOnly="True"
                 SelectionMode="Single"
                 GridLinesVisibility="Horizontal"
                 HeadersVisibility="Column"
                 AlternatingRowBackground="#F9F9F9"
                 MouseDoubleClick="ProductsDataGrid_MouseDoubleClick">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="رقم الصنف" 
                                   Binding="{Binding ProductCode}" 
                                   Width="100"/>
                
                <DataGridTextColumn Header="اسم الصنف" 
                                   Binding="{Binding ProductName}" 
                                   Width="300"/>
                
                <DataGridTextColumn Header="سعر البيع" 
                                   Binding="{Binding SalePrice, StringFormat=N2}" 
                                   Width="120">
                    <DataGridTextColumn.ElementStyle>
                        <Style TargetType="TextBlock">
                            <Setter Property="HorizontalAlignment" Value="Right"/>
                            <Setter Property="FontWeight" Value="Bold"/>
                        </Style>
                    </DataGridTextColumn.ElementStyle>
                </DataGridTextColumn>
            </DataGrid.Columns>
        </DataGrid>
        
        <!-- Buttons -->
        <Border Grid.Row="3" Background="#F5F5F5" Padding="15" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SelectButton" 
                       Content="✅ اختيار" 
                       Background="#4CAF50" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="20,10" 
                       Margin="10,0"
                       FontSize="14"
                       FontWeight="Bold"
                       IsEnabled="False"
                       Click="SelectButton_Click"/>
                
                <Button x:Name="CancelButton" 
                       Content="❌ إلغاء" 
                       Background="#F44336" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="20,10" 
                       Margin="10,0"
                       FontSize="14"
                       FontWeight="Bold"
                       Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
