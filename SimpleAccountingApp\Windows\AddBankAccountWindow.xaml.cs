using System.Windows;
using System.Windows.Controls;
using SimpleAccountingApp.Models;

namespace SimpleAccountingApp.Windows
{
    public partial class AddBankAccountWindow : Window
    {
        public BankAccount? NewBankAccount { get; private set; }
        private bool _isEditMode = false;
        private BankAccount? _editingBankAccount;

        public AddBankAccountWindow()
        {
            InitializeComponent();
            _isEditMode = false;
        }

        public AddBankAccountWindow(BankAccount bankAccount)
        {
            InitializeComponent();
            _isEditMode = true;
            _editingBankAccount = bankAccount;
            TitleTextBlock.Text = "تعديل حساب بنكي";
            LoadBankAccountData();
        }

        private void LoadBankAccountData()
        {
            if (_editingBankAccount != null)
            {
                AccountNumberTextBox.Text = _editingBankAccount.AccountNumber;
                AccountNameTextBox.Text = _editingBankAccount.AccountName;
                BankNameTextBox.Text = _editingBankAccount.BankName;
                BranchNameTextBox.Text = _editingBankAccount.BranchName;
                IBANTextBox.Text = _editingBankAccount.IBAN;
                SwiftCodeTextBox.Text = _editingBankAccount.SwiftCode;
                BalanceTextBox.Text = _editingBankAccount.Balance.ToString();
                CreditLimitTextBox.Text = _editingBankAccount.CreditLimit.ToString();
                IsActiveCheckBox.IsChecked = _editingBankAccount.IsActive;
                NotesTextBox.Text = _editingBankAccount.Notes;

                // تحديد نوع الحساب
                foreach (ComboBoxItem item in AccountTypeComboBox.Items)
                {
                    if (item.Tag.ToString() == _editingBankAccount.AccountType.ToString())
                    {
                        AccountTypeComboBox.SelectedItem = item;
                        break;
                    }
                }

                // تحديد العملة
                foreach (ComboBoxItem item in CurrencyComboBox.Items)
                {
                    if (item.Tag.ToString() == _editingBankAccount.Currency)
                    {
                        CurrencyComboBox.SelectedItem = item;
                        break;
                    }
                }
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(AccountNumberTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال رقم الحساب.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (string.IsNullOrWhiteSpace(AccountNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم الحساب.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (string.IsNullOrWhiteSpace(BankNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم البنك.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (AccountTypeComboBox.SelectedItem == null)
                {
                    MessageBox.Show("يرجى اختيار نوع الحساب.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // التحقق من الرصيد
                if (!decimal.TryParse(BalanceTextBox.Text, out decimal balance))
                {
                    balance = 0;
                }

                // التحقق من حد الائتمان
                if (!decimal.TryParse(CreditLimitTextBox.Text, out decimal creditLimit))
                {
                    creditLimit = 0;
                }

                if (_isEditMode && _editingBankAccount != null)
                {
                    // تحديث البيانات الموجودة
                    _editingBankAccount.AccountNumber = AccountNumberTextBox.Text.Trim();
                    _editingBankAccount.AccountName = AccountNameTextBox.Text.Trim();
                    _editingBankAccount.BankName = BankNameTextBox.Text.Trim();
                    _editingBankAccount.BranchName = BranchNameTextBox.Text.Trim();
                    _editingBankAccount.IBAN = IBANTextBox.Text.Trim();
                    _editingBankAccount.SwiftCode = SwiftCodeTextBox.Text.Trim();
                    _editingBankAccount.Balance = balance;
                    _editingBankAccount.CreditLimit = creditLimit;
                    _editingBankAccount.IsActive = IsActiveCheckBox.IsChecked ?? true;
                    _editingBankAccount.Notes = NotesTextBox.Text.Trim();

                    // تحديد نوع الحساب
                    var selectedAccountType = (ComboBoxItem)AccountTypeComboBox.SelectedItem;
                    _editingBankAccount.AccountType = Enum.Parse<BankAccountType>(selectedAccountType.Tag.ToString()!);

                    // تحديد العملة
                    var selectedCurrency = (ComboBoxItem)CurrencyComboBox.SelectedItem;
                    _editingBankAccount.Currency = selectedCurrency.Tag.ToString()!;

                    NewBankAccount = _editingBankAccount;
                }
                else
                {
                    // إنشاء حساب بنكي جديد
                    var selectedAccountType = (ComboBoxItem)AccountTypeComboBox.SelectedItem;
                    var selectedCurrency = (ComboBoxItem)CurrencyComboBox.SelectedItem;

                    NewBankAccount = new BankAccount
                    {
                        AccountNumber = AccountNumberTextBox.Text.Trim(),
                        AccountName = AccountNameTextBox.Text.Trim(),
                        BankName = BankNameTextBox.Text.Trim(),
                        BranchName = BranchNameTextBox.Text.Trim(),
                        IBAN = IBANTextBox.Text.Trim(),
                        SwiftCode = SwiftCodeTextBox.Text.Trim(),
                        AccountType = Enum.Parse<BankAccountType>(selectedAccountType.Tag.ToString()!),
                        Balance = balance,
                        CreditLimit = creditLimit,
                        Currency = selectedCurrency.Tag.ToString()!,
                        IsActive = IsActiveCheckBox.IsChecked ?? true,
                        Notes = NotesTextBox.Text.Trim()
                    };
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
