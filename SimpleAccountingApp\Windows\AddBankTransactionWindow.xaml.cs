using System.Windows;
using SimpleAccountingApp.Models;
using SimpleAccountingApp.Data;
using Microsoft.EntityFrameworkCore;

namespace SimpleAccountingApp.Windows
{
    public partial class AddBankTransactionWindow : Window
    {
        public BankTransaction? NewTransaction { get; private set; }
        private BankAccount _bankAccount;
        private BankTransactionType _transactionType;
        private AccountingDbContext _context;

        public AddBankTransactionWindow(BankAccount bankAccount, BankTransactionType transactionType)
        {
            InitializeComponent();
            _bankAccount = bankAccount;
            _transactionType = transactionType;
            _context = new AccountingDbContext();
            InitializeWindow();
        }

        private async void InitializeWindow()
        {
            // تحديد العنوان ونوع العملية
            string operationType = _transactionType switch
            {
                BankTransactionType.Deposit => "إيداع",
                BankTransactionType.Withdrawal => "سحب",
                BankTransactionType.Transfer => "تحويل",
                _ => "عملية مصرفية"
            };

            TitleTextBlock.Text = $"إضافة {operationType}";
            TransactionTypeTextBox.Text = operationType;

            // إنشاء رقم عملية تلقائي
            TransactionNumberTextBox.Text = GenerateTransactionNumber();

            // تحديد التاريخ الحالي
            TransactionDatePicker.SelectedDate = DateTime.Now;

            // إظهار حقل الحساب المستقبل للتحويل
            if (_transactionType == BankTransactionType.Transfer)
            {
                ToAccountLabel.Visibility = Visibility.Visible;
                ToAccountComboBox.Visibility = Visibility.Visible;
                await LoadBankAccounts();
            }
        }

        private async Task LoadBankAccounts()
        {
            try
            {
                await _context.InitializeDatabaseAsync();
                var bankAccounts = await _context.BankAccounts
                    .Where(b => b.IsActive && b.BankAccountId != _bankAccount.BankAccountId)
                    .OrderBy(b => b.BankName)
                    .ToListAsync();

                ToAccountComboBox.Items.Clear();
                foreach (var account in bankAccounts)
                {
                    var item = new System.Windows.Controls.ComboBoxItem
                    {
                        Content = $"{account.BankName} - {account.AccountName}",
                        Tag = account.BankAccountId
                    };
                    ToAccountComboBox.Items.Add(item);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل حسابات البنوك: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GenerateTransactionNumber()
        {
            string prefix = _transactionType switch
            {
                BankTransactionType.Deposit => "DEP",
                BankTransactionType.Withdrawal => "WTH",
                BankTransactionType.Transfer => "TRF",
                _ => "TXN"
            };

            return $"{prefix}{DateTime.Now:yyyyMMddHHmmss}";
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!decimal.TryParse(AmountTextBox.Text, out decimal amount) || amount <= 0)
                {
                    MessageBox.Show("يرجى إدخال مبلغ صحيح.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (string.IsNullOrWhiteSpace(DescriptionTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال وصف العملية.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (TransactionDatePicker.SelectedDate == null)
                {
                    MessageBox.Show("يرجى اختيار تاريخ العملية.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // التحقق من الرصيد في حالة السحب أو التحويل
                if (_transactionType == BankTransactionType.Withdrawal || _transactionType == BankTransactionType.Transfer)
                {
                    if (amount > _bankAccount.AvailableBalance)
                    {
                        MessageBox.Show($"المبلغ المطلوب أكبر من الرصيد المتاح ({_bankAccount.AvailableBalance:N2} ريال).",
                            "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }
                }

                // التحقق من الحساب المستقبل في حالة التحويل
                int? toBankAccountId = null;
                if (_transactionType == BankTransactionType.Transfer)
                {
                    if (ToAccountComboBox.SelectedItem == null)
                    {
                        MessageBox.Show("يرجى اختيار الحساب المستقبل للتحويل.", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }
                    toBankAccountId = (int)((System.Windows.Controls.ComboBoxItem)ToAccountComboBox.SelectedItem).Tag;
                }

                // إنشاء العملية الجديدة
                NewTransaction = new BankTransaction
                {
                    TransactionNumber = TransactionNumberTextBox.Text,
                    TransactionDate = TransactionDatePicker.SelectedDate.Value,
                    BankAccountId = _bankAccount.BankAccountId,
                    ToBankAccountId = toBankAccountId,
                    TransactionType = _transactionType,
                    Amount = amount,
                    Description = DescriptionTextBox.Text.Trim(),
                    Reference = ReferenceTextBox.Text.Trim(),
                    Notes = NotesTextBox.Text.Trim(),
                    Status = TransactionStatus.Completed
                };

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
