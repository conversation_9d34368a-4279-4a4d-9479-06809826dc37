using AccountingApp.Models;
using AccountingApp.Data;
using Microsoft.EntityFrameworkCore;

namespace AccountingApp.Services
{
    /// <summary>
    /// خدمة إدارة الحسابات - Account Service
    /// </summary>
    public interface IAccountService
    {
        Task<IEnumerable<Account>> GetAllAccountsAsync();
        Task<IEnumerable<Account>> GetMainAccountsAsync();
        Task<IEnumerable<Account>> GetSubAccountsAsync(int parentAccountId);
        Task<Account?> GetAccountByIdAsync(int accountId);
        Task<Account?> GetAccountByCodeAsync(string accountCode);
        Task<Account> CreateAccountAsync(Account account);
        Task<Account> UpdateAccountAsync(Account account);
        Task<bool> DeleteAccountAsync(int accountId);
        Task<string> GenerateAccountCodeAsync(int? parentAccountId = null);
        Task<decimal> GetAccountBalanceAsync(int accountId, DateTime? asOfDate = null);
        Task<IEnumerable<Account>> GetAccountsByTypeAsync(AccountType accountType);
    }

    public class AccountService : IAccountService
    {
        private readonly AccountingDbContext _context;

        public AccountService(AccountingDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Account>> GetAllAccountsAsync()
        {
            return await _context.Accounts
                .Include(a => a.ParentAccount)
                .Include(a => a.SubAccounts)
                .OrderBy(a => a.AccountCode)
                .ToListAsync();
        }

        public async Task<IEnumerable<Account>> GetMainAccountsAsync()
        {
            return await _context.Accounts
                .Where(a => a.ParentAccountId == null)
                .OrderBy(a => a.AccountCode)
                .ToListAsync();
        }

        public async Task<IEnumerable<Account>> GetSubAccountsAsync(int parentAccountId)
        {
            return await _context.Accounts
                .Where(a => a.ParentAccountId == parentAccountId)
                .OrderBy(a => a.AccountCode)
                .ToListAsync();
        }

        public async Task<Account?> GetAccountByIdAsync(int accountId)
        {
            return await _context.Accounts
                .Include(a => a.ParentAccount)
                .Include(a => a.SubAccounts)
                .FirstOrDefaultAsync(a => a.AccountId == accountId);
        }

        public async Task<Account?> GetAccountByCodeAsync(string accountCode)
        {
            return await _context.Accounts
                .Include(a => a.ParentAccount)
                .Include(a => a.SubAccounts)
                .FirstOrDefaultAsync(a => a.AccountCode == accountCode);
        }

        public async Task<Account> CreateAccountAsync(Account account)
        {
            if (string.IsNullOrEmpty(account.AccountCode))
            {
                account.AccountCode = await GenerateAccountCodeAsync(account.ParentAccountId);
            }

            // تحديد المستوى
            if (account.ParentAccountId.HasValue)
            {
                var parentAccount = await GetAccountByIdAsync(account.ParentAccountId.Value);
                account.Level = parentAccount?.Level + 1 ?? 1;
            }
            else
            {
                account.Level = 1;
            }

            account.CreatedDate = DateTime.Now;
            _context.Accounts.Add(account);
            await _context.SaveChangesAsync();
            return account;
        }

        public async Task<Account> UpdateAccountAsync(Account account)
        {
            account.ModifiedDate = DateTime.Now;
            _context.Accounts.Update(account);
            await _context.SaveChangesAsync();
            return account;
        }

        public async Task<bool> DeleteAccountAsync(int accountId)
        {
            var account = await _context.Accounts
                .Include(a => a.SubAccounts)
                .Include(a => a.JournalEntryDetails)
                .FirstOrDefaultAsync(a => a.AccountId == accountId);

            if (account != null)
            {
                // التحقق من وجود حسابات فرعية
                if (account.SubAccounts.Any())
                {
                    throw new InvalidOperationException("لا يمكن حذف حساب يحتوي على حسابات فرعية");
                }

                // التحقق من وجود حركات محاسبية
                if (account.JournalEntryDetails.Any())
                {
                    throw new InvalidOperationException("لا يمكن حذف حساب يحتوي على حركات محاسبية");
                }

                _context.Accounts.Remove(account);
                await _context.SaveChangesAsync();
                return true;
            }
            return false;
        }

        public async Task<string> GenerateAccountCodeAsync(int? parentAccountId = null)
        {
            if (parentAccountId.HasValue)
            {
                var parentAccount = await GetAccountByIdAsync(parentAccountId.Value);
                if (parentAccount != null)
                {
                    var lastSubAccount = await _context.Accounts
                        .Where(a => a.ParentAccountId == parentAccountId)
                        .OrderByDescending(a => a.AccountCode)
                        .FirstOrDefaultAsync();

                    if (lastSubAccount != null)
                    {
                        var lastCode = lastSubAccount.AccountCode;
                        var lastNumber = int.Parse(lastCode.Substring(lastCode.Length - 2));
                        return parentAccount.AccountCode + (lastNumber + 1).ToString("00");
                    }
                    else
                    {
                        return parentAccount.AccountCode + "01";
                    }
                }
            }
            else
            {
                var lastMainAccount = await _context.Accounts
                    .Where(a => a.ParentAccountId == null)
                    .OrderByDescending(a => a.AccountCode)
                    .FirstOrDefaultAsync();

                if (lastMainAccount != null)
                {
                    var lastNumber = int.Parse(lastMainAccount.AccountCode);
                    return (lastNumber + 1).ToString("0000");
                }
                else
                {
                    return "1000";
                }
            }

            return "1000";
        }

        public async Task<decimal> GetAccountBalanceAsync(int accountId, DateTime? asOfDate = null)
        {
            var query = _context.JournalEntryDetails
                .Include(jed => jed.JournalEntry)
                .Where(jed => jed.AccountId == accountId && jed.JournalEntry.IsPosted);

            if (asOfDate.HasValue)
            {
                query = query.Where(jed => jed.JournalEntry.EntryDate <= asOfDate.Value);
            }

            var account = await GetAccountByIdAsync(accountId);
            if (account == null) return 0;

            var totalDebit = await query.SumAsync(jed => jed.DebitAmount);
            var totalCredit = await query.SumAsync(jed => jed.CreditAmount);

            // حساب الرصيد حسب نوع الحساب
            switch (account.AccountType)
            {
                case AccountType.Assets:
                case AccountType.Expenses:
                case AccountType.Cost:
                    return account.OpeningBalance + totalDebit - totalCredit;
                case AccountType.Liabilities:
                case AccountType.Equity:
                case AccountType.Revenue:
                    return account.OpeningBalance + totalCredit - totalDebit;
                default:
                    return account.OpeningBalance + totalDebit - totalCredit;
            }
        }

        public async Task<IEnumerable<Account>> GetAccountsByTypeAsync(AccountType accountType)
        {
            return await _context.Accounts
                .Where(a => a.AccountType == accountType && a.IsActive)
                .OrderBy(a => a.AccountCode)
                .ToListAsync();
        }
    }
}
