{"version": 3, "targets": {"net9.0-windows7.0": {"Microsoft.NET.ILLink.Tasks/9.0.6": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}}, "net9.0-windows7.0/win-x64": {"Microsoft.NET.ILLink.Tasks/9.0.6": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}}}, "libraries": {"Microsoft.NET.ILLink.Tasks/9.0.6": {"sha512": "TXy3SbJzGXQbxxIxCjdrp8bwEyTDImyYNpTpd6v7P3JL2Y7dno8EYG7dPezfYTa5SoWKdhbH9cbnwHHs3BR5gA==", "type": "package", "path": "microsoft.net.illink.tasks/9.0.6", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "Sdk/Sdk.props", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/ILLink.CodeFixProvider.dll", "analyzers/dotnet/cs/ILLink.RoslynAnalyzer.dll", "build/Microsoft.NET.ILLink.Analyzers.props", "build/Microsoft.NET.ILLink.Tasks.props", "build/Microsoft.NET.ILLink.targets", "microsoft.net.illink.tasks.9.0.6.nupkg.sha512", "microsoft.net.illink.tasks.nuspec", "tools/net472/ILLink.Tasks.dll", "tools/net472/ILLink.Tasks.dll.config", "tools/net472/Mono.Cecil.Mdb.dll", "tools/net472/Mono.Cecil.Pdb.dll", "tools/net472/Mono.Cecil.Rocks.dll", "tools/net472/Mono.Cecil.dll", "tools/net472/Sdk/Sdk.props", "tools/net472/System.Buffers.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/System.Memory.dll", "tools/net472/System.Numerics.Vectors.dll", "tools/net472/System.Reflection.Metadata.dll", "tools/net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/net472/build/Microsoft.NET.ILLink.Analyzers.props", "tools/net472/build/Microsoft.NET.ILLink.Tasks.props", "tools/net472/build/Microsoft.NET.ILLink.targets", "tools/net9.0/ILLink.Tasks.deps.json", "tools/net9.0/ILLink.Tasks.dll", "tools/net9.0/Mono.Cecil.Mdb.dll", "tools/net9.0/Mono.Cecil.Pdb.dll", "tools/net9.0/Mono.Cecil.Rocks.dll", "tools/net9.0/Mono.Cecil.dll", "tools/net9.0/Sdk/Sdk.props", "tools/net9.0/build/Microsoft.NET.ILLink.Analyzers.props", "tools/net9.0/build/Microsoft.NET.ILLink.Tasks.props", "tools/net9.0/build/Microsoft.NET.ILLink.targets", "tools/net9.0/illink.deps.json", "tools/net9.0/illink.dll", "tools/net9.0/illink.runtimeconfig.json", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net9.0-windows7.0": ["Microsoft.NET.ILLink.Tasks >= 9.0.6"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\account\\SimpleAccountingApp\\SimpleAccountingApp.csproj", "projectName": "SimpleAccountingApp", "projectPath": "C:\\Users\\<USER>\\Desktop\\account\\SimpleAccountingApp\\SimpleAccountingApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\account\\SimpleAccountingApp\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.6, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[9.0.6, 9.0.6]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[9.0.6, 9.0.6]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[9.0.6, 9.0.6]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}