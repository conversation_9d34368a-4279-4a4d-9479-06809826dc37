<Window x:Class="AccountingApp.UI.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تسجيل الدخول - نظام المحاسبة المالية" 
        Height="600" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Style="{StaticResource MaterialWindow}"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <materialDesign:DialogHost Identifier="LoginDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- Background Gradient -->
            <Grid.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#E3F2FD" Offset="0"/>
                    <GradientStop Color="#BBDEFB" Offset="1"/>
                </LinearGradientBrush>
            </Grid.Background>
            
            <!-- Main Content -->
            <materialDesign:Card Grid.Row="0" 
                               Margin="40" 
                               Padding="32"
                               VerticalAlignment="Center">
                <StackPanel>
                    <!-- Logo and Title -->
                    <materialDesign:PackIcon Kind="AccountBalance" 
                                           Width="80" Height="80"
                                           HorizontalAlignment="Center"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           Margin="0,0,0,16"/>
                    
                    <TextBlock Text="نظام المحاسبة المالية"
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Margin="0,0,0,8"/>
                    
                    <TextBlock Text="تسجيل الدخول"
                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                             Margin="0,0,0,32"/>
                    
                    <!-- Login Form -->
                    <StackPanel>
                        <!-- Username -->
                        <TextBox x:Name="UsernameTextBox"
                               materialDesign:HintAssist.Hint="اسم المستخدم"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               Margin="0,8"
                               FontSize="14"
                               Text="admin"/>
                        
                        <!-- Password -->
                        <PasswordBox x:Name="PasswordBox"
                                   materialDesign:HintAssist.Hint="كلمة المرور"
                                   Style="{StaticResource MaterialDesignOutlinedPasswordBox}"
                                   Margin="0,8"
                                   FontSize="14"/>
                        
                        <!-- Remember Me -->
                        <CheckBox x:Name="RememberMeCheckBox"
                                Content="تذكرني"
                                Margin="0,16,0,8"
                                Style="{StaticResource MaterialDesignCheckBox}"/>
                        
                        <!-- Login Button -->
                        <Button x:Name="LoginButton"
                              Content="تسجيل الدخول"
                              Style="{StaticResource MaterialDesignRaisedButton}"
                              Background="{DynamicResource PrimaryHueMidBrush}"
                              Foreground="White"
                              FontSize="16"
                              Height="48"
                              Margin="0,24,0,16"
                              Click="LoginButton_Click"/>
                        
                        <!-- Error Message -->
                        <TextBlock x:Name="ErrorMessageTextBlock"
                                 Foreground="Red"
                                 HorizontalAlignment="Center"
                                 Margin="0,8"
                                 Visibility="Collapsed"
                                 TextWrapping="Wrap"/>
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>
            
            <!-- Footer -->
            <StackPanel Grid.Row="1" 
                      Orientation="Horizontal" 
                      HorizontalAlignment="Center"
                      Margin="16">
                <TextBlock Text="© 2025 نظام المحاسبة المالية - جميع الحقوق محفوظة"
                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            </StackPanel>
        </Grid>
    </materialDesign:DialogHost>
</Window>
