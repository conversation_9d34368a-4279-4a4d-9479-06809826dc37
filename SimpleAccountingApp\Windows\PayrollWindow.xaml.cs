using System.Collections.ObjectModel;
using System.Windows;
using SimpleAccountingApp.Models;

namespace SimpleAccountingApp.Windows
{
    public partial class PayrollWindow : Window
    {
        private Employee _employee;
        private ObservableCollection<Payroll> payrolls = new();

        public PayrollWindow(Employee employee)
        {
            InitializeComponent();
            _employee = employee;
            InitializeWindow();
            LoadPayrolls();
        }

        private void InitializeWindow()
        {
            TitleTextBlock.Text = $"إدارة الرواتب - {_employee.FullName}";
            EmployeeInfoTextBlock.Text = $"الموظف: {_employee.FullName} | القسم: {_employee.Department} | المسمى: {_employee.JobTitle} | الراتب الأساسي: {_employee.BasicSalary:N2} ريال";
        }

        private void LoadPayrolls()
        {
            // هنا سيتم تحميل الرواتب من قاعدة البيانات
            // مؤقتاً سنعرض قائمة فارغة
            PayrollsDataGrid.ItemsSource = payrolls;
        }

        private void AddPayrollButton_Click(object sender, RoutedEventArgs e)
        {
            var payrollWindow = new AddPayrollWindow(_employee);
            if (payrollWindow.ShowDialog() == true)
            {
                LoadPayrolls();
            }
        }

        private void EditPayrollButton_Click(object sender, RoutedEventArgs e)
        {
            if (PayrollsDataGrid.SelectedItem is Payroll selectedPayroll)
            {
                var payrollWindow = new AddPayrollWindow(_employee, selectedPayroll);
                if (payrollWindow.ShowDialog() == true)
                {
                    LoadPayrolls();
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار راتب للتعديل.", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void DeletePayrollButton_Click(object sender, RoutedEventArgs e)
        {
            if (PayrollsDataGrid.SelectedItem is Payroll selectedPayroll)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف راتب {selectedPayroll.MonthName} {selectedPayroll.Year}؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    // هنا سيتم حذف الراتب من قاعدة البيانات
                    payrolls.Remove(selectedPayroll);
                    MessageBox.Show("تم حذف الراتب بنجاح!", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار راتب للحذف.", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadPayrolls();
        }
    }
}
