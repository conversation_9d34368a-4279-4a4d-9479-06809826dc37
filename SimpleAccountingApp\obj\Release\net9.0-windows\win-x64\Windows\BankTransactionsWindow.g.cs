﻿#pragma checksum "..\..\..\..\..\Windows\BankTransactionsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "0C78C992EB04090B78C70BE1ED0788C1ABEE2346"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleAccountingApp.Windows {
    
    
    /// <summary>
    /// BankTransactionsWindow
    /// </summary>
    public partial class BankTransactionsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 16 "..\..\..\..\..\Windows\BankTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleTextBlock;
        
        #line default
        #line hidden
        
        
        #line 24 "..\..\..\..\..\Windows\BankTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AccountInfoTextBlock;
        
        #line default
        #line hidden
        
        
        #line 31 "..\..\..\..\..\Windows\BankTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddDepositButton;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\..\..\Windows\BankTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddWithdrawalButton;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\..\..\Windows\BankTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddTransferButton;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\..\..\Windows\BankTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\..\Windows\BankTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid TransactionsDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleAccountingApp;component/windows/banktransactionswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\BankTransactionsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TitleTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.AccountInfoTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.AddDepositButton = ((System.Windows.Controls.Button)(target));
            
            #line 32 "..\..\..\..\..\Windows\BankTransactionsWindow.xaml"
            this.AddDepositButton.Click += new System.Windows.RoutedEventHandler(this.AddDepositButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.AddWithdrawalButton = ((System.Windows.Controls.Button)(target));
            
            #line 34 "..\..\..\..\..\Windows\BankTransactionsWindow.xaml"
            this.AddWithdrawalButton.Click += new System.Windows.RoutedEventHandler(this.AddWithdrawalButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.AddTransferButton = ((System.Windows.Controls.Button)(target));
            
            #line 36 "..\..\..\..\..\Windows\BankTransactionsWindow.xaml"
            this.AddTransferButton.Click += new System.Windows.RoutedEventHandler(this.AddTransferButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 38 "..\..\..\..\..\Windows\BankTransactionsWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.TransactionsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

