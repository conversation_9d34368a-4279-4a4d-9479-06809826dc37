<Window x:Class="SimpleAccountingApp.Windows.StockReportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تقرير المخزون - نظام المحاسبة المالية" 
        Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" Padding="15">
            <DockPanel>
                <Button DockPanel.Dock="Left" 
                       Content="✖ إغلاق" 
                       Background="#1976D2" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,5" 
                       Click="CloseButton_Click"/>
                
                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                    <TextBlock Text="📊" FontSize="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="تقرير المخزون" 
                              FontSize="20" 
                              FontWeight="Bold" 
                              Foreground="White" 
                              VerticalAlignment="Center"/>
                </StackPanel>
            </DockPanel>
        </Border>
        
        <!-- Summary Cards -->
        <Grid Grid.Row="1" Margin="15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Total Products -->
            <Border Grid.Column="0" Background="#E3F2FD" Padding="15" Margin="5" CornerRadius="5">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="📦" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="TotalProductsText" Text="0" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center"/>
                    <TextBlock Text="إجمالي الأصناف" FontSize="12" HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>
            
            <!-- Low Stock Items -->
            <Border Grid.Column="1" Background="#FFEBEE" Padding="15" Margin="5" CornerRadius="5">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="⚠️" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="LowStockText" Text="0" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Red"/>
                    <TextBlock Text="أصناف منخفضة المخزون" FontSize="12" HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>
            
            <!-- Total Stock Value -->
            <Border Grid.Column="2" Background="#E8F5E8" Padding="15" Margin="5" CornerRadius="5">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="💰" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="TotalValueText" Text="0.00" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Green"/>
                    <TextBlock Text="قيمة المخزون الإجمالية" FontSize="12" HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>
            
            <!-- Out of Stock -->
            <Border Grid.Column="3" Background="#FFF3E0" Padding="15" Margin="5" CornerRadius="5">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="❌" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="OutOfStockText" Text="0" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Orange"/>
                    <TextBlock Text="أصناف نفدت من المخزون" FontSize="12" HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>
        </Grid>
        
        <!-- Stock Report DataGrid -->
        <Grid Grid.Row="2" Margin="15,0,15,0">
            <DataGrid x:Name="StockReportDataGrid"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     AlternatingRowBackground="#F9F9F9">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم الصنف" 
                                       Binding="{Binding ProductCode}" 
                                       Width="100"/>
                    
                    <DataGridTextColumn Header="اسم الصنف" 
                                       Binding="{Binding ProductName}" 
                                       Width="200"/>
                    
                    <DataGridTextColumn Header="الفئة" 
                                       Binding="{Binding Category}" 
                                       Width="120"/>
                    
                    <DataGridTextColumn Header="الوحدة" 
                                       Binding="{Binding Unit}" 
                                       Width="80"/>
                    
                    <DataGridTextColumn Header="الكمية الحالية"
                                       Binding="{Binding CurrentStock, StringFormat=N2}"
                                       Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="الحد الأدنى" 
                                       Binding="{Binding MinimumStock, StringFormat=N2}" 
                                       Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="سعر الشراء" 
                                       Binding="{Binding PurchasePrice, StringFormat=N2}" 
                                       Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="قيمة المخزون" 
                                       Binding="{Binding StockValue, StringFormat=N2}" 
                                       Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="الحالة" 
                                       Binding="{Binding StockStatus}" 
                                       Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding StockStatus}" Value="نفد المخزون">
                                        <Setter Property="Foreground" Value="Orange"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding StockStatus}" Value="مخزون منخفض">
                                        <Setter Property="Foreground" Value="Red"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding StockStatus}" Value="مخزون جيد">
                                        <Setter Property="Foreground" Value="Green"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>
        
        <!-- Footer Buttons -->
        <Border Grid.Row="3" Background="#F5F5F5" Padding="15" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="PrintButton" 
                       Content="🖨️ طباعة" 
                       Background="#2196F3" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="20,10" 
                       Margin="10,0"
                       FontSize="14"
                       FontWeight="Bold"
                       Click="PrintButton_Click"/>
                
                <Button x:Name="ExportButton" 
                       Content="📤 تصدير إلى Excel" 
                       Background="#4CAF50" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="20,10" 
                       Margin="10,0"
                       FontSize="14"
                       FontWeight="Bold"
                       Click="ExportButton_Click"/>
                
                <Button x:Name="RefreshButton" 
                       Content="🔄 تحديث" 
                       Background="#FF9800" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="20,10" 
                       Margin="10,0"
                       FontSize="14"
                       FontWeight="Bold"
                       Click="RefreshButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
