<Window x:Class="SimpleAccountingApp.Windows.GeneratePayrollWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إنشاء رواتب الشهر" Height="400" Width="500"
        WindowStartupLocation="CenterScreen" FlowDirection="RightToLeft">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="إنشاء رواتب الشهر" 
                   FontSize="18" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- الإعدادات -->
        <StackPanel Grid.Row="1">
            <Label Content="الشهر:" FontWeight="Bold"/>
            <ComboBox Name="MonthComboBox" Margin="0,0,0,15">
                <ComboBoxItem Content="يناير" Tag="1"/>
                <ComboBoxItem Content="فبراير" Tag="2"/>
                <ComboBoxItem Content="مارس" Tag="3"/>
                <ComboBoxItem Content="أبريل" Tag="4"/>
                <ComboBoxItem Content="مايو" Tag="5"/>
                <ComboBoxItem Content="يونيو" Tag="6"/>
                <ComboBoxItem Content="يوليو" Tag="7"/>
                <ComboBoxItem Content="أغسطس" Tag="8"/>
                <ComboBoxItem Content="سبتمبر" Tag="9"/>
                <ComboBoxItem Content="أكتوبر" Tag="10"/>
                <ComboBoxItem Content="نوفمبر" Tag="11"/>
                <ComboBoxItem Content="ديسمبر" Tag="12"/>
            </ComboBox>

            <Label Content="السنة:" FontWeight="Bold"/>
            <TextBox Name="YearTextBox" Margin="0,0,0,15"/>

            <Label Content="القسم:" FontWeight="Bold"/>
            <ComboBox Name="DepartmentComboBox" Margin="0,0,0,15"/>

            <CheckBox Name="OverwriteExistingCheckBox" Content="استبدال الرواتب الموجودة" Margin="0,10,0,0"/>
            
            <TextBlock Text="ملاحظة: سيتم إنشاء رواتب لجميع الموظفين النشطين في القسم المحدد أو جميع الأقسام." 
                       TextWrapping="Wrap" Margin="0,15,0,0" FontStyle="Italic" Foreground="Gray"/>
        </StackPanel>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Name="GenerateButton" Content="إنشاء الرواتب" 
                    Width="120" Margin="0,0,10,0" Click="GenerateButton_Click"/>
            <Button Name="CancelButton" Content="إلغاء" 
                    Width="100" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
