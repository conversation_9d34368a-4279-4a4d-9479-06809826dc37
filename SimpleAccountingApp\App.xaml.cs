﻿using System.Configuration;
using System.Data;
using System.Windows;
using SimpleAccountingApp.Windows;

namespace SimpleAccountingApp;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        // إظهار شاشة تسجيل الدخول أولاً
        var loginWindow = new LoginWindow();
        if (loginWindow.ShowDialog() == true)
        {
            // إذا نجح تسجيل الدخول، افتح الشاشة الرئيسية
            var mainWindow = new MainWindow();
            mainWindow.Show();
        }
        else
        {
            // إذا تم إلغاء تسجيل الدخول، أغلق التطبيق
            this.Shutdown();
        }
    }
}

