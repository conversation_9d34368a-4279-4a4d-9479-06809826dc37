﻿#pragma checksum "..\..\..\..\Windows\VATCalculatorWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "8343E1FB1D83F52C4D29FCE517A9AA3BB980481A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleAccountingApp.Windows {
    
    
    /// <summary>
    /// VATCalculatorWindow
    /// </summary>
    public partial class VATCalculatorWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 42 "..\..\..\..\Windows\VATCalculatorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton AddVATRadio;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\Windows\VATCalculatorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton RemoveVATRadio;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\Windows\VATCalculatorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AmountLabel;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\Windows\VATCalculatorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\Windows\VATCalculatorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox VATRateTextBox;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\Windows\VATCalculatorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BaseAmountResult;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\Windows\VATCalculatorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock VATAmountResult;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\Windows\VATCalculatorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountResult;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\Windows\VATCalculatorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearButton;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\Windows\VATCalculatorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleAccountingApp;component/windows/vatcalculatorwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\VATCalculatorWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AddVATRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 47 "..\..\..\..\Windows\VATCalculatorWindow.xaml"
            this.AddVATRadio.Checked += new System.Windows.RoutedEventHandler(this.CalculationType_Changed);
            
            #line default
            #line hidden
            return;
            case 2:
            this.RemoveVATRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 51 "..\..\..\..\Windows\VATCalculatorWindow.xaml"
            this.RemoveVATRadio.Checked += new System.Windows.RoutedEventHandler(this.CalculationType_Changed);
            
            #line default
            #line hidden
            return;
            case 3:
            this.AmountLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.AmountTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 64 "..\..\..\..\Windows\VATCalculatorWindow.xaml"
            this.AmountTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.AmountTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.VATRateTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 78 "..\..\..\..\Windows\VATCalculatorWindow.xaml"
            this.VATRateTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.AmountTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BaseAmountResult = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.VATAmountResult = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TotalAmountResult = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.ClearButton = ((System.Windows.Controls.Button)(target));
            
            #line 142 "..\..\..\..\Windows\VATCalculatorWindow.xaml"
            this.ClearButton.Click += new System.Windows.RoutedEventHandler(this.ClearButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 153 "..\..\..\..\Windows\VATCalculatorWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

