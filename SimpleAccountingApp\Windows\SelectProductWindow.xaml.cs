using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace SimpleAccountingApp.Windows
{
    public partial class SelectProductWindow : Window
    {
        private List<InvoiceProduct> _products;
        private List<InvoiceProduct> _filteredProducts;

        public InvoiceProduct? SelectedProduct { get; private set; }

        public SelectProductWindow(List<InvoiceProduct> products)
        {
            InitializeComponent();
            _products = products;
            _filteredProducts = new List<InvoiceProduct>(_products);
            LoadProducts();
        }

        private void LoadProducts()
        {
            ProductsDataGrid.ItemsSource = _filteredProducts;
            ProductsDataGrid.SelectionChanged += ProductsDataGrid_SelectionChanged;
        }

        private void ProductsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            SelectButton.IsEnabled = ProductsDataGrid.SelectedItem != null;
        }

        private void ProductsDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (ProductsDataGrid.SelectedItem != null)
            {
                SelectProduct();
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            string searchText = SearchTextBox.Text.ToLower();
            
            if (string.IsNullOrWhiteSpace(searchText))
            {
                _filteredProducts = new List<InvoiceProduct>(_products);
            }
            else
            {
                _filteredProducts = _products.Where(p =>
                    p.ProductCode.ToLower().Contains(searchText) ||
                    p.ProductName.ToLower().Contains(searchText)
                ).ToList();
            }
            
            ProductsDataGrid.ItemsSource = _filteredProducts;
        }

        private void SelectButton_Click(object sender, RoutedEventArgs e)
        {
            SelectProduct();
        }

        private void SelectProduct()
        {
            if (ProductsDataGrid.SelectedItem is InvoiceProduct selectedProduct)
            {
                SelectedProduct = selectedProduct;
                this.DialogResult = true;
                this.Close();
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            this.DialogResult = false;
            this.Close();
        }
    }
}
