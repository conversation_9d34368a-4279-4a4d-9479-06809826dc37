<Window x:Class="SimpleAccountingApp.Windows.PayrollExportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تصدير الرواتب" Height="300" Width="400"
        WindowStartupLocation="CenterScreen" FlowDirection="RightToLeft">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="تصدير الرواتب إلى Excel" 
                   FontSize="18" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- الخيارات -->
        <StackPanel Grid.Row="1">
            <Label Content="اسم الملف:" FontWeight="Bold"/>
            <TextBox Name="FileNameTextBox" Margin="0,0,0,15"/>

            <Label Content="مسار الحفظ:" FontWeight="Bold"/>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <TextBox Name="SavePathTextBox" Grid.Column="0" IsReadOnly="True"/>
                <Button Name="BrowseButton" Grid.Column="1" Content="تصفح" 
                        Width="60" Margin="5,0,0,0" Click="BrowseButton_Click"/>
            </Grid>

            <CheckBox Name="IncludeDetailsCheckBox" Content="تضمين التفاصيل الكاملة" 
                      IsChecked="True" Margin="0,15,0,0"/>
        </StackPanel>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Name="ExportButton" Content="تصدير" 
                    Width="100" Margin="0,0,10,0" Click="ExportButton_Click"/>
            <Button Name="CancelButton" Content="إلغاء" 
                    Width="100" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
