using System.Collections.ObjectModel;
using System.Windows;
using SimpleAccountingApp.Models;
using SimpleAccountingApp.Data;
using Microsoft.EntityFrameworkCore;

namespace SimpleAccountingApp.Windows
{
    public partial class EmployeesWindow : Window
    {
        private ObservableCollection<Employee> employees = new();
        private AccountingDbContext _context;

        public EmployeesWindow()
        {
            InitializeComponent();
            _context = new AccountingDbContext();
            LoadEmployees();
        }

        private async void LoadEmployees()
        {
            try
            {
                await _context.InitializeDatabaseAsync();
                var employeesList = await _context.Employees
                    .Where(e => e.Status != EmployeeStatus.Terminated)
                    .OrderBy(e => e.Department)
                    .ThenBy(e => e.FirstName)
                    .ToListAsync();

                employees.Clear();
                foreach (var employee in employeesList)
                {
                    employees.Add(employee);
                }

                EmployeesDataGrid.ItemsSource = employees;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void AddEmployeeButton_Click(object sender, RoutedEventArgs e)
        {
            var addEmployeeWindow = new AddEmployeeWindow();
            if (addEmployeeWindow.ShowDialog() == true)
            {
                var newEmployee = addEmployeeWindow.NewEmployee;
                if (newEmployee != null)
                {
                    try
                    {
                        _context.Employees.Add(newEmployee);
                        await _context.SaveChangesAsync();

                        employees.Add(newEmployee);
                        RefreshEmployeesGrid();

                        MessageBox.Show("تم إضافة الموظف بنجاح!", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حفظ الموظف: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private async void EditEmployeeButton_Click(object sender, RoutedEventArgs e)
        {
            if (EmployeesDataGrid.SelectedItem is Employee selectedEmployee)
            {
                var editEmployeeWindow = new AddEmployeeWindow(selectedEmployee);
                if (editEmployeeWindow.ShowDialog() == true)
                {
                    try
                    {
                        _context.Employees.Update(selectedEmployee);
                        await _context.SaveChangesAsync();

                        RefreshEmployeesGrid();

                        MessageBox.Show("تم تعديل الموظف بنجاح!", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في تحديث الموظف: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار موظف للتعديل.", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void DeleteEmployeeButton_Click(object sender, RoutedEventArgs e)
        {
            if (EmployeesDataGrid.SelectedItem is Employee selectedEmployee)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الموظف '{selectedEmployee.FullName}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        _context.Employees.Remove(selectedEmployee);
                        await _context.SaveChangesAsync();

                        employees.Remove(selectedEmployee);
                        RefreshEmployeesGrid();

                        MessageBox.Show("تم حذف الموظف بنجاح!", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الموظف: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار موظف للحذف.", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void PayrollButton_Click(object sender, RoutedEventArgs e)
        {
            if (EmployeesDataGrid.SelectedItem is Employee selectedEmployee)
            {
                var payrollWindow = new PayrollWindow(selectedEmployee);
                payrollWindow.ShowDialog();
            }
            else
            {
                MessageBox.Show("يرجى اختيار موظف لإدارة الرواتب.", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void LeavesButton_Click(object sender, RoutedEventArgs e)
        {
            if (EmployeesDataGrid.SelectedItem is Employee selectedEmployee)
            {
                var leavesWindow = new LeavesWindow(selectedEmployee);
                leavesWindow.ShowDialog();
            }
            else
            {
                MessageBox.Show("يرجى اختيار موظف لإدارة الإجازات.", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadEmployees();
        }

        private void RefreshEmployeesGrid()
        {
            EmployeesDataGrid.Items.Refresh();
        }
    }
}
