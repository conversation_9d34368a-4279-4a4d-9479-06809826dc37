using System.Collections.ObjectModel;
using System.Windows;
using SimpleAccountingApp.Models;

namespace SimpleAccountingApp.Windows
{
    public partial class EmployeesWindow : Window
    {
        private ObservableCollection<Employee> employees = new();

        public EmployeesWindow()
        {
            InitializeComponent();
            LoadEmployees();
        }

        private void LoadEmployees()
        {
            // مؤقتاً سنعرض قائمة فارغة
            EmployeesDataGrid.ItemsSource = employees;
        }

        private void AddEmployeeButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void EditEmployeeButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void DeleteEmployeeButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void PayrollButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void LeavesButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadEmployees();
        }
    }
}
