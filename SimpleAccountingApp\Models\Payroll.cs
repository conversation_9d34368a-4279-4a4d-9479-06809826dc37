using System.ComponentModel.DataAnnotations;

namespace SimpleAccountingApp.Models
{
    public enum PayrollStatus
    {
        Draft = 1,         // مسودة
        Approved = 2,      // معتمد
        Paid = 3,          // مدفوع
        Cancelled = 4      // ملغي
    }

    public class Payroll
    {
        [Key]
        public int PayrollId { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string PayrollNumber { get; set; } = "";
        
        public int EmployeeId { get; set; }
        public Employee? Employee { get; set; }
        
        public DateTime PayrollDate { get; set; } = DateTime.Now;
        
        public int Month { get; set; }
        public int Year { get; set; }
        
        public decimal BasicSalary { get; set; }
        
        public decimal HousingAllowance { get; set; }
        public decimal TransportationAllowance { get; set; }
        public decimal FoodAllowance { get; set; }
        public decimal OtherAllowances { get; set; }
        
        public decimal OvertimeHours { get; set; }
        public decimal OvertimeRate { get; set; }
        
        public decimal SocialInsurance { get; set; }
        public decimal IncomeTax { get; set; }
        public decimal Loan { get; set; }
        public decimal OtherDeductions { get; set; }
        
        public PayrollStatus Status { get; set; } = PayrollStatus.Draft;
        
        [MaxLength(1000)]
        public string Notes { get; set; } = "";
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        // خصائص محسوبة
        public decimal TotalAllowances => HousingAllowance + TransportationAllowance + FoodAllowance + OtherAllowances;
        public decimal OvertimePay => OvertimeHours * OvertimeRate;
        public decimal GrossSalary => BasicSalary + TotalAllowances + OvertimePay;
        public decimal TotalDeductions => SocialInsurance + IncomeTax + Loan + OtherDeductions;
        public decimal NetSalary => GrossSalary - TotalDeductions;
        public string StatusText => GetStatusText(Status);
        public string MonthName => GetMonthName(Month);
        
        private static string GetStatusText(PayrollStatus status)
        {
            return status switch
            {
                PayrollStatus.Draft => "مسودة",
                PayrollStatus.Approved => "معتمد",
                PayrollStatus.Paid => "مدفوع",
                PayrollStatus.Cancelled => "ملغي",
                _ => "غير محدد"
            };
        }
        
        private static string GetMonthName(int month)
        {
            return month switch
            {
                1 => "يناير",
                2 => "فبراير",
                3 => "مارس",
                4 => "أبريل",
                5 => "مايو",
                6 => "يونيو",
                7 => "يوليو",
                8 => "أغسطس",
                9 => "سبتمبر",
                10 => "أكتوبر",
                11 => "نوفمبر",
                12 => "ديسمبر",
                _ => "غير محدد"
            };
        }
    }
}
