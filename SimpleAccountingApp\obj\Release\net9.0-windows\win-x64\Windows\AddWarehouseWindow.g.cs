﻿#pragma checksum "..\..\..\..\..\Windows\AddWarehouseWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1837F670A50A680FCB469775B4C169D769FE8A29"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleAccountingApp.Windows {
    
    
    /// <summary>
    /// AddWarehouseWindow
    /// </summary>
    public partial class AddWarehouseWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 23 "..\..\..\..\..\Windows\AddWarehouseWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WindowTitle;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\..\..\Windows\AddWarehouseWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox WarehouseCodeTextBox;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\..\Windows\AddWarehouseWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox WarehouseNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\..\Windows\AddWarehouseWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LocationTextBox;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\..\Windows\AddWarehouseWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ManagerTextBox;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\..\Windows\AddWarehouseWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PhoneTextBox;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\..\Windows\AddWarehouseWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AreaTextBox;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\..\Windows\AddWarehouseWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CapacityTextBox;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\..\..\Windows\AddWarehouseWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TemperatureControlComboBox;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\..\Windows\AddWarehouseWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SecurityLevelComboBox;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\..\Windows\AddWarehouseWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\..\Windows\AddWarehouseWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsActiveCheckBox;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\..\..\Windows\AddWarehouseWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\..\Windows\AddWarehouseWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleAccountingApp;component/windows/addwarehousewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\AddWarehouseWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.WindowTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.WarehouseCodeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.WarehouseNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.LocationTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.ManagerTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.PhoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.AreaTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.CapacityTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.TemperatureControlComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.SecurityLevelComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.IsActiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 13:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 160 "..\..\..\..\..\Windows\AddWarehouseWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 171 "..\..\..\..\..\Windows\AddWarehouseWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

