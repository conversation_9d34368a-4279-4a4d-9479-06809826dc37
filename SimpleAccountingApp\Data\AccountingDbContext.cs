using Microsoft.EntityFrameworkCore;
using SimpleAccountingApp.Models;
using System.IO;

namespace SimpleAccountingApp.Data
{
    public class AccountingDbContext : DbContext
    {
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<SalesInvoice> SalesInvoices { get; set; }
        public DbSet<PurchaseInvoice> PurchaseInvoices { get; set; }
        public DbSet<Account> Accounts { get; set; }
        public DbSet<User> Users { get; set; }

        // الجداول الجديدة
        public DbSet<BankAccount> BankAccounts { get; set; }
        public DbSet<BankTransaction> BankTransactions { get; set; }
        public DbSet<Expense> Expenses { get; set; }
        public DbSet<Employee> Employees { get; set; }
        public DbSet<Payroll> Payrolls { get; set; }
        public DbSet<Leave> Leaves { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            var dbPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                "SimpleAccountingApp", "accounting.db");
            
            // إنشاء المجلد إذا لم يكن موجوداً
            var directory = Path.GetDirectoryName(dbPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory!);
            }

            optionsBuilder.UseSqlite($"Data Source={dbPath}");
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // تكوين العملاء
            modelBuilder.Entity<Customer>(entity =>
            {
                entity.HasKey(e => e.CustomerId);
                entity.Property(e => e.CustomerName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Phone).HasMaxLength(50);
                entity.Property(e => e.Email).HasMaxLength(100);
                entity.Property(e => e.Address).HasMaxLength(500);
                entity.Property(e => e.CreditLimit).HasColumnType("decimal(18,2)");
            });

            // تكوين الموردين
            modelBuilder.Entity<Supplier>(entity =>
            {
                entity.HasKey(e => e.SupplierId);
                entity.Property(e => e.SupplierCode).HasMaxLength(50);
                entity.Property(e => e.SupplierName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.SupplierType).HasMaxLength(50);
                entity.Property(e => e.Phone).HasMaxLength(50);
                entity.Property(e => e.Mobile).HasMaxLength(50);
                entity.Property(e => e.Email).HasMaxLength(100);
                entity.Property(e => e.Address).HasMaxLength(500);
                entity.Property(e => e.City).HasMaxLength(100);
                entity.Property(e => e.Country).HasMaxLength(100);
                entity.Property(e => e.TaxNumber).HasMaxLength(50);
                entity.Property(e => e.CurrentBalance).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Notes).HasMaxLength(1000);
            });

            // تكوين المنتجات
            modelBuilder.Entity<Product>(entity =>
            {
                entity.HasKey(e => e.ProductId);
                entity.Property(e => e.ProductCode).HasMaxLength(50);
                entity.Property(e => e.ProductName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Barcode).HasMaxLength(50);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.PurchasePrice).HasColumnType("decimal(18,2)");
                entity.Property(e => e.SalePrice).HasColumnType("decimal(18,2)");
                entity.Property(e => e.CurrentStock).HasColumnType("decimal(18,2)");
                entity.Property(e => e.MinimumStock).HasColumnType("decimal(18,2)");
                entity.Property(e => e.MaximumStock).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Category).HasMaxLength(100);
                entity.Property(e => e.Unit).HasMaxLength(50);
            });

            // تكوين فواتير المبيعات
            modelBuilder.Entity<SalesInvoice>(entity =>
            {
                entity.HasKey(e => e.InvoiceId);
                entity.Property(e => e.InvoiceNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.CustomerName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.SubTotal).HasColumnType("decimal(18,2)");
                entity.Property(e => e.DiscountAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TaxAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TotalAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.PaidAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.RemainingAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.PaymentMethod).HasMaxLength(50);
                entity.Property(e => e.Notes).HasMaxLength(1000);
            });

            // تكوين فواتير المشتريات
            modelBuilder.Entity<PurchaseInvoice>(entity =>
            {
                entity.HasKey(e => e.InvoiceId);
                entity.Property(e => e.InvoiceNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.SupplierName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.SubTotal).HasColumnType("decimal(18,2)");
                entity.Property(e => e.DiscountAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TaxAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TotalAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.PaidAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.RemainingAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.PaymentMethod).HasMaxLength(50);
                entity.Property(e => e.Notes).HasMaxLength(1000);
            });

            // تكوين الحسابات
            modelBuilder.Entity<Account>(entity =>
            {
                entity.HasKey(e => e.AccountId);
                entity.Property(e => e.AccountName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.AccountCode).IsRequired().HasMaxLength(50);
                entity.Property(e => e.AccountType).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Balance).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Description).HasMaxLength(500);
            });

            // تكوين المستخدمين
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.UserId);
                entity.Property(e => e.Username).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Password).IsRequired().HasMaxLength(200);
                entity.Property(e => e.FullName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Email).HasMaxLength(100);
                entity.Property(e => e.Role).IsRequired().HasMaxLength(50);
            });

            // تكوين حسابات البنوك
            modelBuilder.Entity<BankAccount>(entity =>
            {
                entity.HasKey(e => e.BankAccountId);
                entity.Property(e => e.AccountNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.AccountName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.BankName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.BranchName).HasMaxLength(100);
                entity.Property(e => e.IBAN).HasMaxLength(50);
                entity.Property(e => e.SwiftCode).HasMaxLength(50);
                entity.Property(e => e.Balance).HasColumnType("decimal(18,2)");
                entity.Property(e => e.CreditLimit).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Currency).HasMaxLength(50);
                entity.Property(e => e.Notes).HasMaxLength(1000);
            });

            // تكوين العمليات المصرفية
            modelBuilder.Entity<BankTransaction>(entity =>
            {
                entity.HasKey(e => e.TransactionId);
                entity.Property(e => e.TransactionNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Amount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Description).HasMaxLength(200);
                entity.Property(e => e.Reference).HasMaxLength(100);
                entity.Property(e => e.Notes).HasMaxLength(1000);

                entity.HasOne(e => e.BankAccount)
                    .WithMany()
                    .HasForeignKey(e => e.BankAccountId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.ToBankAccount)
                    .WithMany()
                    .HasForeignKey(e => e.ToBankAccountId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // تكوين المصروفات
            modelBuilder.Entity<Expense>(entity =>
            {
                entity.HasKey(e => e.ExpenseId);
                entity.Property(e => e.ExpenseNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.ExpenseName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Amount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Vendor).HasMaxLength(200);
                entity.Property(e => e.InvoiceNumber).HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Notes).HasMaxLength(1000);
                entity.Property(e => e.ApprovedBy).HasMaxLength(100);

                entity.HasOne(e => e.BankAccount)
                    .WithMany()
                    .HasForeignKey(e => e.BankAccountId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // تكوين الموظفين
            modelBuilder.Entity<Employee>(entity =>
            {
                entity.HasKey(e => e.EmployeeId);
                entity.Property(e => e.EmployeeCode).IsRequired().HasMaxLength(50);
                entity.Property(e => e.FirstName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.LastName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.NationalId).HasMaxLength(50);
                entity.Property(e => e.Gender).HasMaxLength(10);
                entity.Property(e => e.Phone).HasMaxLength(50);
                entity.Property(e => e.Mobile).HasMaxLength(50);
                entity.Property(e => e.Email).HasMaxLength(100);
                entity.Property(e => e.Address).HasMaxLength(500);
                entity.Property(e => e.City).HasMaxLength(100);
                entity.Property(e => e.Country).HasMaxLength(100);
                entity.Property(e => e.JobTitle).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Department).IsRequired().HasMaxLength(200);
                entity.Property(e => e.BasicSalary).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Allowances).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Deductions).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Notes).HasMaxLength(1000);
            });

            // تكوين الرواتب
            modelBuilder.Entity<Payroll>(entity =>
            {
                entity.HasKey(e => e.PayrollId);
                entity.Property(e => e.PayrollNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.BasicSalary).HasColumnType("decimal(18,2)");
                entity.Property(e => e.HousingAllowance).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TransportationAllowance).HasColumnType("decimal(18,2)");
                entity.Property(e => e.FoodAllowance).HasColumnType("decimal(18,2)");
                entity.Property(e => e.OtherAllowances).HasColumnType("decimal(18,2)");
                entity.Property(e => e.OvertimeHours).HasColumnType("decimal(18,2)");
                entity.Property(e => e.OvertimeRate).HasColumnType("decimal(18,2)");
                entity.Property(e => e.SocialInsurance).HasColumnType("decimal(18,2)");
                entity.Property(e => e.IncomeTax).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Loan).HasColumnType("decimal(18,2)");
                entity.Property(e => e.OtherDeductions).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Notes).HasMaxLength(1000);

                entity.HasOne(e => e.Employee)
                    .WithMany()
                    .HasForeignKey(e => e.EmployeeId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // تكوين الإجازات
            modelBuilder.Entity<Leave>(entity =>
            {
                entity.HasKey(e => e.LeaveId);
                entity.Property(e => e.LeaveNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Reason).HasMaxLength(500);
                entity.Property(e => e.ApprovedBy).HasMaxLength(100);
                entity.Property(e => e.RejectionReason).HasMaxLength(500);
                entity.Property(e => e.Notes).HasMaxLength(1000);

                entity.HasOne(e => e.Employee)
                    .WithMany()
                    .HasForeignKey(e => e.EmployeeId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            base.OnModelCreating(modelBuilder);
        }

        public async Task InitializeDatabaseAsync()
        {
            await Database.EnsureCreatedAsync();
            
            // إضافة بيانات تجريبية إذا لم تكن موجودة
            if (!Users.Any())
            {
                await SeedDataAsync();
            }
        }

        private async Task SeedDataAsync()
        {
            // إضافة مستخدمين افتراضيين
            var users = new[]
            {
                new User { Username = "admin", Password = "123456", FullName = "مدير النظام", Role = "Admin", Email = "<EMAIL>", IsActive = true },
                new User { Username = "محاسب", Password = "password", FullName = "المحاسب الرئيسي", Role = "Accountant", Email = "<EMAIL>", IsActive = true },
                new User { Username = "مدير", Password = "manager123", FullName = "مدير العمليات", Role = "Manager", Email = "<EMAIL>", IsActive = true },
                new User { Username = "مستخدم", Password = "user123", FullName = "مستخدم عادي", Role = "User", Email = "<EMAIL>", IsActive = true }
            };
            Users.AddRange(users);

            // إضافة عملاء تجريبيين
            var customers = new[]
            {
                new Customer {
                    CustomerCode = "C001",
                    CustomerName = "شركة الرياض للتجارة",
                    CustomerType = "شركة",
                    Phone = "**********",
                    Mobile = "**********",
                    Email = "<EMAIL>",
                    Address = "الرياض، حي الملك فهد",
                    City = "الرياض",
                    Country = "السعودية",
                    TaxNumber = "*********",
                    CreditLimit = 50000,
                    CurrentBalance = 15000,
                    PaymentTerms = 30,
                    Notes = "عميل مميز"
                },
                new Customer {
                    CustomerCode = "C002",
                    CustomerName = "مؤسسة جدة للمقاولات",
                    CustomerType = "مؤسسة",
                    Phone = "0*********",
                    Mobile = "0507654321",
                    Email = "<EMAIL>",
                    Address = "جدة، حي الصفا",
                    City = "جدة",
                    Country = "السعودية",
                    TaxNumber = "*********",
                    CreditLimit = 100000,
                    CurrentBalance = 25000,
                    PaymentTerms = 45,
                    Notes = "مقاول معتمد"
                },
                new Customer {
                    CustomerCode = "C003",
                    CustomerName = "محمد أحمد العلي",
                    CustomerType = "فرد",
                    Phone = "**********",
                    Mobile = "**********",
                    Email = "<EMAIL>",
                    Address = "الدمام، حي الشاطئ",
                    City = "الدمام",
                    Country = "السعودية",
                    TaxNumber = "",
                    CreditLimit = 25000,
                    CurrentBalance = 5000,
                    PaymentTerms = 15,
                    Notes = ""
                }
            };
            Customers.AddRange(customers);

            // إضافة موردين تجريبيين
            var suppliers = new[]
            {
                new Supplier {
                    SupplierCode = "S001",
                    SupplierName = "شركة المواد الأولية",
                    SupplierType = "شركة",
                    Phone = "0112223333",
                    Mobile = "0502223333",
                    Email = "<EMAIL>",
                    Address = "الرياض، حي العليا",
                    City = "الرياض",
                    Country = "السعودية",
                    TaxNumber = "*********",
                    CurrentBalance = 25000,
                    PaymentTerms = 30,
                    Notes = "مورد مواد أولية"
                },
                new Supplier {
                    SupplierCode = "S002",
                    SupplierName = "مصنع الأدوات الصناعية",
                    SupplierType = "مصنع",
                    Phone = "0123334444",
                    Mobile = "0503334444",
                    Email = "<EMAIL>",
                    Address = "الدمام، حي الصناعية",
                    City = "الدمام",
                    Country = "السعودية",
                    TaxNumber = "*********",
                    CurrentBalance = 15000,
                    PaymentTerms = 45,
                    Notes = "مورد أدوات صناعية"
                },
                new Supplier {
                    SupplierCode = "S003",
                    SupplierName = "شركة التقنيات المتقدمة",
                    SupplierType = "شركة",
                    Phone = "0134445555",
                    Mobile = "0504445555",
                    Email = "<EMAIL>",
                    Address = "جدة، حي التقنية",
                    City = "جدة",
                    Country = "السعودية",
                    TaxNumber = "*********",
                    CurrentBalance = 8000,
                    PaymentTerms = 60,
                    Notes = "مورد تقنيات متقدمة"
                }
            };
            Suppliers.AddRange(suppliers);

            // إضافة منتجات تجريبية
            var products = new[]
            {
                new Product {
                    ProductCode = "P001",
                    ProductName = "جهاز كمبيوتر محمول",
                    Barcode = "**********123",
                    Description = "جهاز كمبيوتر محمول عالي الأداء",
                    PurchasePrice = 2800,
                    SalePrice = 3500,
                    CurrentStock = 25,
                    MinimumStock = 5,
                    MaximumStock = 50,
                    Category = "إلكترونيات",
                    Unit = "قطعة"
                },
                new Product {
                    ProductCode = "P002",
                    ProductName = "طابعة ليزر",
                    Barcode = "2*********234",
                    Description = "طابعة ليزر ملونة",
                    PurchasePrice = 900,
                    SalePrice = 1200,
                    CurrentStock = 15,
                    MinimumStock = 3,
                    MaximumStock = 30,
                    Category = "إلكترونيات",
                    Unit = "قطعة"
                },
                new Product {
                    ProductCode = "P003",
                    ProductName = "مكتب خشبي",
                    Barcode = "*********2345",
                    Description = "مكتب خشبي فاخر",
                    PurchasePrice = 600,
                    SalePrice = 800,
                    CurrentStock = 10,
                    MinimumStock = 2,
                    MaximumStock = 20,
                    Category = "أثاث",
                    Unit = "قطعة"
                },
                new Product {
                    ProductCode = "P004",
                    ProductName = "كرسي مكتبي",
                    Barcode = "4************",
                    Description = "كرسي مكتبي مريح",
                    PurchasePrice = 300,
                    SalePrice = 450,
                    CurrentStock = 30,
                    MinimumStock = 5,
                    MaximumStock = 50,
                    Category = "أثاث",
                    Unit = "قطعة"
                }
            };
            Products.AddRange(products);

            // إضافة حسابات مالية تجريبية
            var accounts = new[]
            {
                new Account { AccountCode = "1001", AccountName = "النقدية", AccountType = "أصول", Balance = 100000, Description = "النقدية في الصندوق" },
                new Account { AccountCode = "1002", AccountName = "البنك", AccountType = "أصول", Balance = 250000, Description = "حساب البنك الجاري" },
                new Account { AccountCode = "1101", AccountName = "العملاء", AccountType = "أصول", Balance = 75000, Description = "مستحقات العملاء" },
                new Account { AccountCode = "1201", AccountName = "المخزون", AccountType = "أصول", Balance = 150000, Description = "قيمة المخزون" },
                new Account { AccountCode = "2001", AccountName = "الموردون", AccountType = "خصوم", Balance = 50000, Description = "مستحقات الموردين" },
                new Account { AccountCode = "3001", AccountName = "رأس المال", AccountType = "حقوق ملكية", Balance = 400000, Description = "رأس المال المدفوع" },
                new Account { AccountCode = "4001", AccountName = "إيرادات المبيعات", AccountType = "إيرادات", Balance = 0, Description = "إيرادات من المبيعات" },
                new Account { AccountCode = "5001", AccountName = "تكلفة البضاعة المباعة", AccountType = "مصروفات", Balance = 0, Description = "تكلفة البضاعة المباعة" }
            };
            Accounts.AddRange(accounts);

            // إضافة حسابات بنكية تجريبية
            var bankAccounts = new[]
            {
                new BankAccount
                {
                    AccountNumber = "**********",
                    AccountName = "الحساب الجاري الرئيسي",
                    BankName = "البنك الأهلي السعودي",
                    BranchName = "فرع الرياض الرئيسي",
                    IBAN = "SA********************12",
                    SwiftCode = "NCBKSARI",
                    AccountType = BankAccountType.Current,
                    Balance = 500000,
                    CreditLimit = 100000,
                    Currency = "SAR",
                    Notes = "الحساب الرئيسي للشركة"
                },
                new BankAccount
                {
                    AccountNumber = "**********",
                    AccountName = "حساب التوفير",
                    BankName = "بنك الراجحي",
                    BranchName = "فرع جدة",
                    IBAN = "SA*******************098",
                    SwiftCode = "RJHISARI",
                    AccountType = BankAccountType.Savings,
                    Balance = 250000,
                    CreditLimit = 0,
                    Currency = "SAR",
                    Notes = "حساب توفير للطوارئ"
                }
            };
            BankAccounts.AddRange(bankAccounts);

            await SaveChangesAsync();
        }
    }
}
