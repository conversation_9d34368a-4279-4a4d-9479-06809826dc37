using Microsoft.EntityFrameworkCore;
using SimpleAccountingApp.Models;
using System.IO;

namespace SimpleAccountingApp.Data
{
    public class AccountingDbContext : DbContext
    {
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<SalesInvoice> SalesInvoices { get; set; }
        public DbSet<PurchaseInvoice> PurchaseInvoices { get; set; }
        public DbSet<Account> Accounts { get; set; }
        public DbSet<User> Users { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            var dbPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                "SimpleAccountingApp", "accounting.db");
            
            // إنشاء المجلد إذا لم يكن موجوداً
            var directory = Path.GetDirectoryName(dbPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory!);
            }

            optionsBuilder.UseSqlite($"Data Source={dbPath}");
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // تكوين العملاء
            modelBuilder.Entity<Customer>(entity =>
            {
                entity.HasKey(e => e.CustomerId);
                entity.Property(e => e.CustomerName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Phone).HasMaxLength(50);
                entity.Property(e => e.Email).HasMaxLength(100);
                entity.Property(e => e.Address).HasMaxLength(500);
                entity.Property(e => e.CreditLimit).HasColumnType("decimal(18,2)");
            });

            // تكوين الموردين
            modelBuilder.Entity<Supplier>(entity =>
            {
                entity.HasKey(e => e.SupplierId);
                entity.Property(e => e.SupplierName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Phone).HasMaxLength(50);
                entity.Property(e => e.Email).HasMaxLength(100);
                entity.Property(e => e.Address).HasMaxLength(500);
            });

            // تكوين المنتجات
            modelBuilder.Entity<Product>(entity =>
            {
                entity.HasKey(e => e.ProductId);
                entity.Property(e => e.ProductName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.UnitPrice).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Category).HasMaxLength(100);
                entity.Property(e => e.Unit).HasMaxLength(50);
            });

            // تكوين فواتير المبيعات
            modelBuilder.Entity<SalesInvoice>(entity =>
            {
                entity.HasKey(e => e.InvoiceId);
                entity.Property(e => e.InvoiceNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.CustomerName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.SubTotal).HasColumnType("decimal(18,2)");
                entity.Property(e => e.DiscountAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TaxAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TotalAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.PaidAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.RemainingAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.PaymentMethod).HasMaxLength(50);
                entity.Property(e => e.Notes).HasMaxLength(1000);
            });

            // تكوين فواتير المشتريات
            modelBuilder.Entity<PurchaseInvoice>(entity =>
            {
                entity.HasKey(e => e.InvoiceId);
                entity.Property(e => e.InvoiceNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.SupplierName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.SubTotal).HasColumnType("decimal(18,2)");
                entity.Property(e => e.DiscountAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TaxAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TotalAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.PaidAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.RemainingAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.PaymentMethod).HasMaxLength(50);
                entity.Property(e => e.Notes).HasMaxLength(1000);
            });

            // تكوين الحسابات
            modelBuilder.Entity<Account>(entity =>
            {
                entity.HasKey(e => e.AccountId);
                entity.Property(e => e.AccountName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.AccountCode).IsRequired().HasMaxLength(50);
                entity.Property(e => e.AccountType).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Balance).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Description).HasMaxLength(500);
            });

            // تكوين المستخدمين
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.UserId);
                entity.Property(e => e.Username).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Password).IsRequired().HasMaxLength(200);
                entity.Property(e => e.FullName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Email).HasMaxLength(100);
                entity.Property(e => e.Role).IsRequired().HasMaxLength(50);
            });

            base.OnModelCreating(modelBuilder);
        }

        public async Task InitializeDatabaseAsync()
        {
            await Database.EnsureCreatedAsync();
            
            // إضافة بيانات تجريبية إذا لم تكن موجودة
            if (!Users.Any())
            {
                await SeedDataAsync();
            }
        }

        private async Task SeedDataAsync()
        {
            // إضافة مستخدمين افتراضيين
            var users = new[]
            {
                new User { Username = "admin", Password = "123456", FullName = "مدير النظام", Role = "Admin", Email = "<EMAIL>", IsActive = true },
                new User { Username = "محاسب", Password = "password", FullName = "المحاسب الرئيسي", Role = "Accountant", Email = "<EMAIL>", IsActive = true },
                new User { Username = "مدير", Password = "manager123", FullName = "مدير العمليات", Role = "Manager", Email = "<EMAIL>", IsActive = true },
                new User { Username = "مستخدم", Password = "user123", FullName = "مستخدم عادي", Role = "User", Email = "<EMAIL>", IsActive = true }
            };
            Users.AddRange(users);

            // إضافة عملاء تجريبيين
            var customers = new[]
            {
                new Customer { CustomerName = "شركة الرياض للتجارة", Phone = "**********", Email = "<EMAIL>", Address = "الرياض، المملكة العربية السعودية", CreditLimit = 50000, PaymentTerms = 30 },
                new Customer { CustomerName = "مؤسسة جدة للمقاولات", Phone = "**********", Email = "<EMAIL>", Address = "جدة، المملكة العربية السعودية", CreditLimit = 100000, PaymentTerms = 45 },
                new Customer { CustomerName = "محمد أحمد العلي", Phone = "0501234567", Email = "<EMAIL>", Address = "الدمام، المملكة العربية السعودية", CreditLimit = 25000, PaymentTerms = 15 }
            };
            Customers.AddRange(customers);

            // إضافة موردين تجريبيين
            var suppliers = new[]
            {
                new Supplier { SupplierName = "شركة المواد الأولية", Phone = "0112223333", Email = "<EMAIL>", Address = "الرياض، المملكة العربية السعودية", PaymentTerms = 30 },
                new Supplier { SupplierName = "مصنع الأدوات الصناعية", Phone = "0123334444", Email = "<EMAIL>", Address = "الدمام، المملكة العربية السعودية", PaymentTerms = 45 },
                new Supplier { SupplierName = "شركة التقنيات المتقدمة", Phone = "0134445555", Email = "<EMAIL>", Address = "جدة، المملكة العربية السعودية", PaymentTerms = 60 }
            };
            Suppliers.AddRange(suppliers);

            // إضافة منتجات تجريبية
            var products = new[]
            {
                new Product { ProductName = "جهاز كمبيوتر محمول", Description = "جهاز كمبيوتر محمول عالي الأداء", UnitPrice = 3500, StockQuantity = 25, Category = "إلكترونيات", Unit = "قطعة" },
                new Product { ProductName = "طابعة ليزر", Description = "طابعة ليزر ملونة", UnitPrice = 1200, StockQuantity = 15, Category = "إلكترونيات", Unit = "قطعة" },
                new Product { ProductName = "مكتب خشبي", Description = "مكتب خشبي فاخر", UnitPrice = 800, StockQuantity = 10, Category = "أثاث", Unit = "قطعة" },
                new Product { ProductName = "كرسي مكتبي", Description = "كرسي مكتبي مريح", UnitPrice = 450, StockQuantity = 30, Category = "أثاث", Unit = "قطعة" }
            };
            Products.AddRange(products);

            // إضافة حسابات مالية تجريبية
            var accounts = new[]
            {
                new Account { AccountCode = "1001", AccountName = "النقدية", AccountType = "أصول", Balance = 100000, Description = "النقدية في الصندوق" },
                new Account { AccountCode = "1002", AccountName = "البنك", AccountType = "أصول", Balance = 250000, Description = "حساب البنك الجاري" },
                new Account { AccountCode = "1101", AccountName = "العملاء", AccountType = "أصول", Balance = 75000, Description = "مستحقات العملاء" },
                new Account { AccountCode = "1201", AccountName = "المخزون", AccountType = "أصول", Balance = 150000, Description = "قيمة المخزون" },
                new Account { AccountCode = "2001", AccountName = "الموردون", AccountType = "خصوم", Balance = 50000, Description = "مستحقات الموردين" },
                new Account { AccountCode = "3001", AccountName = "رأس المال", AccountType = "حقوق ملكية", Balance = 400000, Description = "رأس المال المدفوع" },
                new Account { AccountCode = "4001", AccountName = "إيرادات المبيعات", AccountType = "إيرادات", Balance = 0, Description = "إيرادات من المبيعات" },
                new Account { AccountCode = "5001", AccountName = "تكلفة البضاعة المباعة", AccountType = "مصروفات", Balance = 0, Description = "تكلفة البضاعة المباعة" }
            };
            Accounts.AddRange(accounts);

            await SaveChangesAsync();
        }
    }
}
