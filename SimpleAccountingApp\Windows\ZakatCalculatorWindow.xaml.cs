using System.Windows;
using System.Windows.Controls;

namespace SimpleAccountingApp.Windows
{
    public partial class ZakatCalculatorWindow : Window
    {
        private const decimal NISAB_AMOUNT = 17000; // النصاب بالريال السعودي (تقريبي)
        private const decimal ZAKAT_RATE = 0.025m; // معدل الزكاة 2.5%

        public ZakatCalculatorWindow()
        {
            InitializeComponent();
            CalculateZakat();
        }

        private void ZakatTypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            CalculateZakat();
        }

        private void CalculateZakat(object sender = null, TextChangedEventArgs e = null)
        {
            try
            {
                // الحصول على القيم المدخلة
                decimal cash = GetDecimalValue(CashTextBox?.Text);
                decimal tradeGoods = GetDecimalValue(TradeGoodsTextBox?.Text);
                decimal receivables = GetDecimalValue(ReceivablesTextBox?.Text);
                decimal investments = GetDecimalValue(InvestmentsTextBox?.Text);
                decimal debts = GetDecimalValue(DebtsTextBox?.Text);

                // حساب إجمالي الأصول
                decimal totalAssets = cash + tradeGoods + receivables + investments;
                
                // حساب صافي الأصول الزكوية
                decimal netAssets = totalAssets - debts;
                
                // حساب الزكاة
                decimal zakatAmount = 0;
                bool isZakatDue = netAssets >= NISAB_AMOUNT;
                
                if (isZakatDue)
                {
                    zakatAmount = netAssets * ZAKAT_RATE;
                }

                // عرض النتائج
                UpdateResults(totalAssets, debts, netAssets, zakatAmount, isZakatDue);
            }
            catch
            {
                // في حالة حدوث خطأ، عرض قيم صفر
                UpdateResults(0, 0, 0, 0, false);
            }
        }

        private decimal GetDecimalValue(string text)
        {
            if (decimal.TryParse(text, out decimal value))
                return Math.Max(0, value); // التأكد من أن القيمة ليست سالبة
            return 0;
        }

        private void UpdateResults(decimal totalAssets, decimal totalLiabilities, decimal netAssets, decimal zakatAmount, bool isZakatDue)
        {
            if (TotalAssetsResult != null)
                TotalAssetsResult.Text = $"{totalAssets:N2} ريال";
            
            if (TotalLiabilitiesResult != null)
                TotalLiabilitiesResult.Text = $"{totalLiabilities:N2} ريال";
            
            if (NetAssetsResult != null)
                NetAssetsResult.Text = $"{netAssets:N2} ريال";
            
            if (NisabResult != null)
                NisabResult.Text = $"{NISAB_AMOUNT:N2} ريال";
            
            if (ZakatAmountResult != null)
                ZakatAmountResult.Text = $"{zakatAmount:N2} ريال";
            
            if (ZakatStatusText != null)
            {
                if (isZakatDue)
                {
                    ZakatStatusText.Text = "الزكاة مستحقة - المبلغ يتجاوز النصاب";
                    ZakatStatusText.Foreground = System.Windows.Media.Brushes.Green;
                }
                else
                {
                    ZakatStatusText.Text = "المبلغ أقل من النصاب - لا زكاة مستحقة";
                    ZakatStatusText.Foreground = System.Windows.Media.Brushes.Red;
                }
            }
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            CashTextBox.Text = "0";
            TradeGoodsTextBox.Text = "0";
            ReceivablesTextBox.Text = "0";
            InvestmentsTextBox.Text = "0";
            DebtsTextBox.Text = "0";
            ZakatTypeComboBox.SelectedIndex = 0;
            CashTextBox.Focus();
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            // في التطبيق الحقيقي، سيتم إنشاء تقرير مطبوع
            MessageBox.Show("سيتم طباعة تقرير حساب الزكاة\n\nيحتوي التقرير على:\n• تفاصيل الأصول والخصوم\n• حساب صافي الأصول الزكوية\n• مقارنة بالنصاب\n• مبلغ الزكاة المستحقة\n• التاريخ والتوقيع", 
                "طباعة تقرير الزكاة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
