<Window x:Class="SimpleAccountingApp.Windows.IncomeStatementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="قائمة الدخل - نظام المحاسبة المالية" 
        Height="700" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#4CAF50" Padding="15">
            <DockPanel>
                <Button DockPanel.Dock="Left" 
                       Content="✖ إغلاق" 
                       Background="#388E3C" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,5" 
                       Click="CloseButton_Click"/>
                
                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                    <TextBlock Text="📋" FontSize="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="قائمة الدخل" 
                              FontSize="20" 
                              FontWeight="Bold" 
                              Foreground="White" 
                              VerticalAlignment="Center"/>
                </StackPanel>
            </DockPanel>
        </Border>
        
        <!-- Period Selection -->
        <Border Grid.Row="1" Background="#F5F5F5" Padding="15" BorderBrush="#DDD" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="من تاريخ:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <DatePicker x:Name="FromDatePicker" Grid.Column="1" Height="35" SelectedDate="2024-01-01"/>

                <TextBlock Grid.Column="2" Text="إلى تاريخ:" VerticalAlignment="Center" Margin="15,0,10,0"/>
                <DatePicker x:Name="ToDatePicker" Grid.Column="3" Height="35" SelectedDate="2024-12-31"/>
                
                <Button x:Name="RefreshButton" 
                       Grid.Column="4" 
                       Content="🔄 تحديث" 
                       Background="#2196F3" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="15,0,0,0"
                       Click="RefreshButton_Click"/>
            </Grid>
        </Border>
        
        <!-- Income Statement Content -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto" Padding="20">
            <Border Background="White" BorderBrush="#DDD" BorderThickness="1" CornerRadius="5">
                <StackPanel Margin="30">
                    <!-- Company Header -->
                    <TextBlock Text="شركة المحاسبة المالية المتقدمة" 
                              FontSize="20" 
                              FontWeight="Bold" 
                              HorizontalAlignment="Center" 
                              Margin="0,0,0,5"/>
                    <TextBlock Text="قائمة الدخل" 
                              FontSize="16" 
                              FontWeight="Bold" 
                              HorizontalAlignment="Center" 
                              Margin="0,0,0,5"/>
                    <TextBlock x:Name="PeriodText" 
                              Text="للفترة من 2024/01/01 إلى 2024/12/31" 
                              FontSize="14" 
                              HorizontalAlignment="Center" 
                              Margin="0,0,0,30"/>
                    
                    <!-- Revenue Section -->
                    <Border Background="#E8F5E8" Padding="10" Margin="0,0,0,10">
                        <TextBlock Text="الإيرادات" FontSize="16" FontWeight="Bold"/>
                    </Border>
                    
                    <Grid Margin="20,0,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="150"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="إيرادات المبيعات" Margin="0,5"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="850,000.00" HorizontalAlignment="Right" Margin="0,5"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="إيرادات أخرى" Margin="0,5"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="25,000.00" HorizontalAlignment="Right" Margin="0,5"/>
                        
                        <Border Grid.Row="2" Grid.ColumnSpan="2" BorderBrush="#DDD" BorderThickness="0,1,0,0" Margin="0,10"/>
                        
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="إجمالي الإيرادات" FontWeight="Bold" Margin="0,5"/>
                        <TextBlock Grid.Row="3" Grid.Column="1" Text="875,000.00" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,5"/>
                    </Grid>
                    
                    <!-- Cost of Goods Sold -->
                    <Border Background="#FFEBEE" Padding="10" Margin="0,20,0,10">
                        <TextBlock Text="تكلفة البضاعة المباعة" FontSize="16" FontWeight="Bold"/>
                    </Border>
                    
                    <Grid Margin="20,0,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="150"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="مخزون أول المدة" Margin="0,5"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="120,000.00" HorizontalAlignment="Right" Margin="0,5"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="المشتريات" Margin="0,5"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="450,000.00" HorizontalAlignment="Right" Margin="0,5"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="ناقص: مخزون آخر المدة" Margin="0,5"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="(180,000.00)" HorizontalAlignment="Right" Margin="0,5"/>
                        
                        <Border Grid.Row="3" Grid.ColumnSpan="2" BorderBrush="#DDD" BorderThickness="0,1,0,0" Margin="0,10"/>
                    </Grid>
                    
                    <Grid Margin="0,5,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="150"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" Text="تكلفة البضاعة المباعة" FontWeight="Bold"/>
                        <TextBlock Grid.Column="1" Text="390,000.00" FontWeight="Bold" HorizontalAlignment="Right"/>
                    </Grid>
                    
                    <!-- Gross Profit -->
                    <Border Background="#E3F2FD" Padding="10" Margin="0,20,0,10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="150"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="إجمالي الربح" FontSize="16" FontWeight="Bold"/>
                            <TextBlock Grid.Column="1" Text="485,000.00" FontSize="16" FontWeight="Bold" HorizontalAlignment="Right" Foreground="Green"/>
                        </Grid>
                    </Border>
                    
                    <!-- Operating Expenses -->
                    <Border Background="#FFF3E0" Padding="10" Margin="0,20,0,10">
                        <TextBlock Text="المصروفات التشغيلية" FontSize="16" FontWeight="Bold"/>
                    </Border>
                    
                    <Grid Margin="20,0,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="150"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="رواتب ومكافآت" Margin="0,5"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="180,000.00" HorizontalAlignment="Right" Margin="0,5"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="إيجارات" Margin="0,5"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="60,000.00" HorizontalAlignment="Right" Margin="0,5"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="كهرباء ومياه" Margin="0,5"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="25,000.00" HorizontalAlignment="Right" Margin="0,5"/>
                        
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="مصروفات إدارية أخرى" Margin="0,5"/>
                        <TextBlock Grid.Row="3" Grid.Column="1" Text="45,000.00" HorizontalAlignment="Right" Margin="0,5"/>
                        
                        <Border Grid.Row="4" Grid.ColumnSpan="2" BorderBrush="#DDD" BorderThickness="0,1,0,0" Margin="0,10"/>
                        
                        <TextBlock Grid.Row="5" Grid.Column="0" Text="إجمالي المصروفات التشغيلية" FontWeight="Bold" Margin="0,5"/>
                        <TextBlock Grid.Row="5" Grid.Column="1" Text="310,000.00" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,5"/>
                    </Grid>
                    
                    <!-- Net Income -->
                    <Border Background="#C8E6C9" Padding="15" Margin="0,30,0,0" CornerRadius="5">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="150"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="صافي الربح" FontSize="18" FontWeight="Bold"/>
                            <TextBlock Grid.Column="1" Text="175,000.00" FontSize="18" FontWeight="Bold" HorizontalAlignment="Right" Foreground="Green"/>
                        </Grid>
                    </Border>
                </StackPanel>
            </Border>
        </ScrollViewer>
        
        <!-- Footer Buttons -->
        <Border Grid.Row="3" Background="#F5F5F5" Padding="15" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="PrintButton" 
                       Content="🖨️ طباعة" 
                       Background="#2196F3" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="20,10" 
                       Margin="10,0"
                       FontSize="14"
                       FontWeight="Bold"
                       Click="PrintButton_Click"/>
                
                <Button x:Name="ExportButton" 
                       Content="📤 تصدير" 
                       Background="#4CAF50" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="20,10" 
                       Margin="10,0"
                       FontSize="14"
                       FontWeight="Bold"
                       Click="ExportButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
