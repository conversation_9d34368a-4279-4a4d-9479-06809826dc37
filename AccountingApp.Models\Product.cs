using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingApp.Models
{
    /// <summary>
    /// نموذج الصنف - Product Model
    /// </summary>
    public class Product
    {
        [Key]
        public int ProductId { get; set; }

        [Required]
        [StringLength(20)]
        public string ProductCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string ProductName { get; set; } = string.Empty;

        [StringLength(100)]
        public string ProductNameEnglish { get; set; } = string.Empty;

        [StringLength(50)]
        public string Barcode { get; set; } = string.Empty;

        public int CategoryId { get; set; }

        [ForeignKey("CategoryId")]
        public virtual ProductCategory Category { get; set; } = null!;

        public int UnitId { get; set; }

        [ForeignKey("UnitId")]
        public virtual Unit Unit { get; set; } = null!;

        [Column(TypeName = "decimal(18,2)")]
        public decimal PurchasePrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal SalePrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal MinimumStock { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal MaximumStock { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal ReorderLevel { get; set; } = 0;

        public bool IsActive { get; set; } = true;

        public bool TrackInventory { get; set; } = true;

        [StringLength(500)]
        public string? Description { get; set; }

        public byte[]? Image { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? ModifiedDate { get; set; }

        public string? CreatedBy { get; set; }

        public string? ModifiedBy { get; set; }

        // Navigation Properties
        public virtual ICollection<ProductStock> ProductStocks { get; set; } = new List<ProductStock>();
        public virtual ICollection<SalesInvoiceDetail> SalesInvoiceDetails { get; set; } = new List<SalesInvoiceDetail>();
        public virtual ICollection<PurchaseInvoiceDetail> PurchaseInvoiceDetails { get; set; } = new List<PurchaseInvoiceDetail>();
    }

    /// <summary>
    /// نموذج فئة الصنف - Product Category Model
    /// </summary>
    public class ProductCategory
    {
        [Key]
        public int CategoryId { get; set; }

        [Required]
        [StringLength(50)]
        public string CategoryName { get; set; } = string.Empty;

        [StringLength(50)]
        public string CategoryNameEnglish { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    }

    /// <summary>
    /// نموذج الوحدة - Unit Model
    /// </summary>
    public class Unit
    {
        [Key]
        public int UnitId { get; set; }

        [Required]
        [StringLength(50)]
        public string UnitName { get; set; } = string.Empty;

        [StringLength(50)]
        public string UnitNameEnglish { get; set; } = string.Empty;

        [StringLength(10)]
        public string UnitSymbol { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    }

    /// <summary>
    /// نموذج المخزن - Warehouse Model
    /// </summary>
    public class Warehouse
    {
        [Key]
        public int WarehouseId { get; set; }

        [Required]
        [StringLength(20)]
        public string WarehouseCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string WarehouseName { get; set; } = string.Empty;

        [StringLength(500)]
        public string Address { get; set; } = string.Empty;

        [StringLength(20)]
        public string Phone { get; set; } = string.Empty;

        [StringLength(100)]
        public string Manager { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        public bool IsMainWarehouse { get; set; } = false;

        [StringLength(500)]
        public string? Description { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? ModifiedDate { get; set; }

        public string? CreatedBy { get; set; }

        public string? ModifiedBy { get; set; }

        // Navigation Properties
        public virtual ICollection<ProductStock> ProductStocks { get; set; } = new List<ProductStock>();
    }

    /// <summary>
    /// نموذج مخزون الصنف - Product Stock Model
    /// </summary>
    public class ProductStock
    {
        [Key]
        public int ProductStockId { get; set; }

        public int ProductId { get; set; }

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;

        public int WarehouseId { get; set; }

        [ForeignKey("WarehouseId")]
        public virtual Warehouse Warehouse { get; set; } = null!;

        [Column(TypeName = "decimal(18,3)")]
        public decimal Quantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal AverageCost { get; set; } = 0;

        public DateTime LastUpdated { get; set; } = DateTime.Now;
    }
}
