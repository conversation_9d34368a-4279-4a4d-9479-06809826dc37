using System.Windows;
using System.Windows.Controls;
using SimpleAccountingApp.Models;
using SimpleAccountingApp.Data;
using Microsoft.EntityFrameworkCore;

namespace SimpleAccountingApp.Windows
{
    public partial class GeneratePayrollWindow : Window
    {
        private AccountingDbContext _context;

        public GeneratePayrollWindow()
        {
            InitializeComponent();
            _context = new AccountingDbContext();
            InitializeWindow();
        }

        private async void InitializeWindow()
        {
            // تحديد الشهر والسنة الحالية
            var currentDate = DateTime.Now;
            YearTextBox.Text = currentDate.Year.ToString();
            
            foreach (ComboBoxItem item in MonthComboBox.Items)
            {
                if (int.Parse(item.Tag.ToString()!) == currentDate.Month)
                {
                    MonthComboBox.SelectedItem = item;
                    break;
                }
            }

            // تحميل الأقسام
            await LoadDepartments();
        }

        private async Task LoadDepartments()
        {
            try
            {
                await _context.InitializeDatabaseAsync();
                var departments = await _context.Employees
                    .Where(e => e.Status == EmployeeStatus.Active)
                    .Select(e => e.Department)
                    .Distinct()
                    .OrderBy(d => d)
                    .ToListAsync();

                DepartmentComboBox.Items.Clear();
                DepartmentComboBox.Items.Add(new ComboBoxItem { Content = "جميع الأقسام", Tag = "", IsSelected = true });
                
                foreach (var dept in departments)
                {
                    DepartmentComboBox.Items.Add(new ComboBoxItem { Content = dept, Tag = dept });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الأقسام: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void GenerateButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (MonthComboBox.SelectedItem == null)
                {
                    MessageBox.Show("يرجى اختيار الشهر.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (!int.TryParse(YearTextBox.Text, out int year) || year < 2000 || year > 2100)
                {
                    MessageBox.Show("يرجى إدخال سنة صحيحة.", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var selectedMonth = (ComboBoxItem)MonthComboBox.SelectedItem;
                var month = int.Parse(selectedMonth.Tag.ToString()!);
                
                var selectedDept = "";
                if (DepartmentComboBox.SelectedItem is ComboBoxItem deptItem)
                {
                    selectedDept = deptItem.Tag?.ToString() ?? "";
                }

                // الحصول على الموظفين
                var employeesQuery = _context.Employees.Where(e => e.Status == EmployeeStatus.Active);
                
                if (!string.IsNullOrEmpty(selectedDept))
                {
                    employeesQuery = employeesQuery.Where(e => e.Department == selectedDept);
                }

                var employees = await employeesQuery.ToListAsync();

                if (!employees.Any())
                {
                    MessageBox.Show("لا توجد موظفين نشطين في القسم المحدد.", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // التحقق من وجود رواتب مسبقة
                var existingPayrolls = await _context.Payrolls
                    .Where(p => p.Month == month && p.Year == year)
                    .ToListAsync();

                if (existingPayrolls.Any() && !(OverwriteExistingCheckBox.IsChecked ?? false))
                {
                    var result = MessageBox.Show(
                        $"توجد رواتب مسبقة لشهر {selectedMonth.Content} {year}. هل تريد المتابعة واستبدالها؟",
                        "تأكيد",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.No)
                        return;
                }

                // حذف الرواتب الموجودة إذا لزم الأمر
                if (existingPayrolls.Any())
                {
                    _context.Payrolls.RemoveRange(existingPayrolls);
                }

                // إنشاء رواتب جديدة
                var newPayrolls = new List<Payroll>();
                foreach (var employee in employees)
                {
                    var payroll = new Payroll
                    {
                        PayrollNumber = $"PAY{year:0000}{month:00}{employee.EmployeeId:000}",
                        EmployeeId = employee.EmployeeId,
                        PayrollDate = DateTime.Now,
                        Month = month,
                        Year = year,
                        BasicSalary = employee.BasicSalary,
                        HousingAllowance = employee.Allowances * 0.5m, // افتراضي 50% من البدلات للسكن
                        TransportationAllowance = employee.Allowances * 0.3m, // 30% للمواصلات
                        FoodAllowance = employee.Allowances * 0.2m, // 20% للطعام
                        OtherAllowances = 0,
                        OvertimeHours = 0,
                        OvertimeRate = employee.BasicSalary / 240, // افتراضي: الراتب الأساسي / 240 ساعة
                        SocialInsurance = employee.BasicSalary * 0.09m, // 9% تأمينات اجتماعية
                        IncomeTax = 0, // سيتم حسابها لاحقاً
                        Loan = employee.Deductions * 0.5m, // افتراضي 50% من الخصومات للقروض
                        OtherDeductions = employee.Deductions * 0.5m, // 50% خصومات أخرى
                        Status = PayrollStatus.Draft,
                        Notes = "تم إنشاؤه تلقائياً"
                    };

                    newPayrolls.Add(payroll);
                }

                _context.Payrolls.AddRange(newPayrolls);
                await _context.SaveChangesAsync();

                MessageBox.Show($"تم إنشاء {newPayrolls.Count} راتب بنجاح لشهر {selectedMonth.Content} {year}!", 
                    "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء الرواتب: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
