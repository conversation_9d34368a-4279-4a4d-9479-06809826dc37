using System.Windows;
using SimpleAccountingApp.Helpers;
using SimpleAccountingApp.Data;
using SimpleAccountingApp.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.ObjectModel;

namespace SimpleAccountingApp.Windows
{
    public partial class BalanceSheetWindow : Window
    {
        private AccountingDbContext _context;
        private ObservableCollection<Account> accounts = new();

        public BalanceSheetWindow()
        {
            InitializeComponent();
            _context = new AccountingDbContext();
            UpdateAsOfDateText();
            LoadBalanceSheetData();
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            UpdateAsOfDateText();
            await LoadBalanceSheetData();
            MessageBox.Show("تم تحديث التقرير بنجاح!", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void UpdateAsOfDateText()
        {
            if (AsOfDatePicker.SelectedDate.HasValue)
            {
                AsOfDateText.Text = $"كما في {AsOfDatePicker.SelectedDate.Value:yyyy/MM/dd}";
            }
        }

        private async Task LoadBalanceSheetData()
        {
            try
            {
                await _context.InitializeDatabaseAsync();

                // تحميل جميع الحسابات
                var accountsList = await _context.Accounts
                    .OrderBy(a => a.AccountType)
                    .ThenBy(a => a.AccountName)
                    .ToListAsync();

                accounts.Clear();

                // إذا لم توجد حسابات، إنشاء حسابات تجريبية
                if (!accountsList.Any())
                {
                    await CreateSampleAccounts();
                    accountsList = await _context.Accounts
                        .OrderBy(a => a.AccountType)
                        .ThenBy(a => a.AccountName)
                        .ToListAsync();
                }

                // حساب الأرصدة الفعلية
                await CalculateRealBalances(accountsList);

                foreach (var account in accountsList)
                {
                    accounts.Add(account);
                }

                // البيانات محملة ومتاحة للطباعة والتصدير
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الميزانية: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task CreateSampleAccounts()
        {
            var sampleAccounts = new[]
            {
                new Models.Account { AccountName = "النقدية", AccountType = Models.AccountType.Asset, Balance = 50000, AccountCode = "1001" },
                new Models.Account { AccountName = "البنوك", AccountType = Models.AccountType.Asset, Balance = 75000, AccountCode = "1002" },
                new Models.Account { AccountName = "العملاء", AccountType = Models.AccountType.Asset, Balance = 30000, AccountCode = "1101" },
                new Models.Account { AccountName = "المخزون", AccountType = Models.AccountType.Asset, Balance = 45000, AccountCode = "1201" },
                new Models.Account { AccountName = "الأثاث والمعدات", AccountType = Models.AccountType.Asset, Balance = 25000, AccountCode = "1501" },
                new Models.Account { AccountName = "الموردون", AccountType = Models.AccountType.Liability, Balance = 35000, AccountCode = "2001" },
                new Models.Account { AccountName = "مصروفات مستحقة", AccountType = Models.AccountType.Liability, Balance = 10000, AccountCode = "2101" },
                new Models.Account { AccountName = "رأس المال", AccountType = Models.AccountType.Equity, Balance = 150000, AccountCode = "3001" },
                new Models.Account { AccountName = "الأرباح المحتجزة", AccountType = Models.AccountType.Equity, Balance = 30000, AccountCode = "3101" }
            };

            _context.Accounts.AddRange(sampleAccounts);
            await _context.SaveChangesAsync();
        }

        private async Task CalculateRealBalances(List<Account> accountsList)
        {
            // حساب أرصدة البنوك من العمليات المصرفية
            var bankAccounts = await _context.BankAccounts.ToListAsync();
            var bankAccount = accountsList.FirstOrDefault(a => a.AccountName == "البنوك");
            if (bankAccount != null)
            {
                bankAccount.Balance = bankAccounts.Sum(b => b.Balance);
            }

            // حساب أرصدة العملاء من فواتير المبيعات
            var salesInvoices = await _context.SalesInvoices.ToListAsync();
            var customersAccount = accountsList.FirstOrDefault(a => a.AccountName == "العملاء");
            if (customersAccount != null)
            {
                customersAccount.Balance = salesInvoices.Sum(s => s.TotalAmount);
            }

            // حساب أرصدة الموردين من فواتير المشتريات
            var purchaseInvoices = await _context.PurchaseInvoices.ToListAsync();
            var suppliersAccount = accountsList.FirstOrDefault(a => a.AccountName == "الموردون");
            if (suppliersAccount != null)
            {
                suppliersAccount.Balance = purchaseInvoices.Sum(p => p.TotalAmount);
            }

            // حساب المخزون من المنتجات
            var products = await _context.Products.ToListAsync();
            var inventoryAccount = accountsList.FirstOrDefault(a => a.AccountName == "المخزون");
            if (inventoryAccount != null)
            {
                inventoryAccount.Balance = products.Sum(p => p.CurrentStock * p.PurchasePrice);
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            var assets = accounts.Where(a => a.AccountType == Models.AccountType.Asset).ToList();
            var liabilities = accounts.Where(a => a.AccountType == Models.AccountType.Liability).ToList();
            var equity = accounts.Where(a => a.AccountType == Models.AccountType.Equity).ToList();

            var totalAssets = assets.Sum(a => a.Balance);
            var totalLiabilities = liabilities.Sum(a => a.Balance);
            var totalEquity = equity.Sum(a => a.Balance);

            var reportContent = $@"
الميزانية العمومية
==================
كما في {DateTime.Now:yyyy/MM/dd}

الأصول:
--------";

            foreach (var asset in assets)
            {
                reportContent += $"\n- {asset.AccountName}: {asset.Balance:N2} ريال";
            }

            reportContent += $@"
إجمالي الأصول: {totalAssets:N2} ريال

الخصوم:
--------";

            foreach (var liability in liabilities)
            {
                reportContent += $"\n- {liability.AccountName}: {liability.Balance:N2} ريال";
            }

            reportContent += $@"
إجمالي الخصوم: {totalLiabilities:N2} ريال

حقوق الملكية:
--------------";

            foreach (var eq in equity)
            {
                reportContent += $"\n- {eq.AccountName}: {eq.Balance:N2} ريال";
            }

            reportContent += $@"
إجمالي حقوق الملكية: {totalEquity:N2} ريال

إجمالي الخصوم وحقوق الملكية: {totalLiabilities + totalEquity:N2} ريال
";

            ReportHelper.PrintText(reportContent, "الميزانية العمومية");
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير تصدير الميزانية العمومية قريباً", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
