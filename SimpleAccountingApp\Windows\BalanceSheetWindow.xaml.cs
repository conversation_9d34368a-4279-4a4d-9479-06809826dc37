using System.Windows;

namespace SimpleAccountingApp.Windows
{
    public partial class BalanceSheetWindow : Window
    {
        public BalanceSheetWindow()
        {
            InitializeComponent();
            UpdateAsOfDateText();
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            UpdateAsOfDateText();
            MessageBox.Show("تم تحديث التقرير بنجاح!", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void UpdateAsOfDateText()
        {
            if (AsOfDatePicker.SelectedDate.HasValue)
            {
                AsOfDateText.Text = $"كما في {AsOfDatePicker.SelectedDate.Value:yyyy/MM/dd}";
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("وظيفة الطباعة قيد التطوير", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("وظيفة التصدير قيد التطوير", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
