using System.Windows;
using SimpleAccountingApp.Helpers;

namespace SimpleAccountingApp.Windows
{
    public partial class BalanceSheetWindow : Window
    {
        public BalanceSheetWindow()
        {
            InitializeComponent();
            UpdateAsOfDateText();
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            UpdateAsOfDateText();
            MessageBox.Show("تم تحديث التقرير بنجاح!", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void UpdateAsOfDateText()
        {
            if (AsOfDatePicker.SelectedDate.HasValue)
            {
                AsOfDateText.Text = $"كما في {AsOfDatePicker.SelectedDate.Value:yyyy/MM/dd}";
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            var reportContent = @"
الميزانية العمومية
==================

الأصول:
--------
الأصول المتداولة:
- النقدية والبنوك: 125,000.00 ريال
- العملاء: 85,000.00 ريال
- المخزون: 150,000.00 ريال
إجمالي الأصول المتداولة: 360,000.00 ريال

الأصول الثابتة:
- الأثاث والمعدات: 75,000.00 ريال
- السيارات: 120,000.00 ريال
- مجمع الإهلاك: (25,000.00) ريال
إجمالي الأصول الثابتة: 170,000.00 ريال

إجمالي الأصول: 530,000.00 ريال

الخصوم وحقوق الملكية:
======================
الخصوم المتداولة:
- الموردون: 65,000.00 ريال
- مصروفات مستحقة: 15,000.00 ريال
إجمالي الخصوم المتداولة: 80,000.00 ريال

حقوق الملكية:
- رأس المال: 400,000.00 ريال
- الأرباح المحتجزة: 50,000.00 ريال
إجمالي حقوق الملكية: 450,000.00 ريال

إجمالي الخصوم وحقوق الملكية: 530,000.00 ريال
";
            ReportHelper.PrintText(reportContent, "الميزانية العمومية");
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير تصدير الميزانية العمومية قريباً", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
