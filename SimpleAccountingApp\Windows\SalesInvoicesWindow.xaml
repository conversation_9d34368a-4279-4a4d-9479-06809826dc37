<Window x:Class="SimpleAccountingApp.Windows.SalesInvoicesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="فواتير المبيعات - نظام المحاسبة المالية" 
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="50"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#4CAF50" Padding="15">
            <DockPanel>
                <Button DockPanel.Dock="Left" 
                       Content="✖ إغلاق" 
                       Background="#388E3C" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,5" 
                       Click="CloseButton_Click"/>
                
                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                    <TextBlock Text="🧾" FontSize="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="فواتير المبيعات" 
                              FontSize="20" 
                              FontWeight="Bold" 
                              Foreground="White" 
                              VerticalAlignment="Center"/>
                </StackPanel>
            </DockPanel>
        </Border>
        
        <!-- Toolbar -->
        <Border Grid.Row="1" Background="#F5F5F5" Padding="10" BorderBrush="#DDD" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <Button x:Name="AddInvoiceButton" 
                       Content="➕ فاتورة جديدة" 
                       Background="#4CAF50" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="5,0"
                       Click="AddInvoiceButton_Click"/>
                
                <Button x:Name="EditInvoiceButton" 
                       Content="✏️ تعديل" 
                       Background="#FF9800" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="5,0"
                       IsEnabled="False"
                       Click="EditInvoiceButton_Click"/>
                
                <Button x:Name="DeleteInvoiceButton" 
                       Content="🗑️ حذف" 
                       Background="#F44336" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="5,0"
                       IsEnabled="False"
                       Click="DeleteInvoiceButton_Click"/>
                
                <Button x:Name="PrintInvoiceButton" 
                       Content="🖨️ طباعة" 
                       Background="#2196F3" 
                       Foreground="White" 
                       BorderThickness="0"
                       Padding="15,8" 
                       Margin="5,0"
                       IsEnabled="False"
                       Click="PrintInvoiceButton_Click"/>
                
                <Separator Margin="10,0"/>
                
                <TextBlock Text="البحث:" VerticalAlignment="Center" Margin="10,0,5,0"/>
                <TextBox x:Name="SearchTextBox" 
                        Width="200" 
                        Height="30"
                        Padding="5"
                        TextChanged="SearchTextBox_TextChanged"/>
            </StackPanel>
        </Border>
        
        <!-- Invoices DataGrid -->
        <Grid Grid.Row="2" Margin="10">
            <DataGrid x:Name="InvoicesDataGrid"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     AlternatingRowBackground="#F9F9F9"
                     SelectionChanged="InvoicesDataGrid_SelectionChanged">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم الفاتورة" 
                                       Binding="{Binding InvoiceNumber}" 
                                       Width="120"/>
                    
                    <DataGridTextColumn Header="تاريخ الفاتورة" 
                                       Binding="{Binding InvoiceDate, StringFormat=yyyy/MM/dd}" 
                                       Width="120"/>
                    
                    <DataGridTextColumn Header="العميل" 
                                       Binding="{Binding CustomerName}" 
                                       Width="200"/>
                    
                    <DataGridTextColumn Header="المبلغ الفرعي" 
                                       Binding="{Binding SubTotal, StringFormat=N2}" 
                                       Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="الخصم" 
                                       Binding="{Binding DiscountAmount, StringFormat=N2}" 
                                       Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="الضريبة" 
                                       Binding="{Binding TaxAmount, StringFormat=N2}" 
                                       Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="المبلغ الإجمالي" 
                                       Binding="{Binding TotalAmount, StringFormat=N2}" 
                                       Width="130">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="Foreground" Value="Green"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="المبلغ المدفوع" 
                                       Binding="{Binding PaidAmount, StringFormat=N2}" 
                                       Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="المبلغ المتبقي" 
                                       Binding="{Binding RemainingAmount, StringFormat=N2}" 
                                       Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding RemainingAmount}" Value="0">
                                        <Setter Property="Foreground" Value="Green"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="الحالة" 
                                       Binding="{Binding StatusText}" 
                                       Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding Status}" Value="مدفوعة">
                                        <Setter Property="Foreground" Value="Green"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding Status}" Value="مدفوعة جزئياً">
                                        <Setter Property="Foreground" Value="Orange"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding Status}" Value="غير مدفوعة">
                                        <Setter Property="Foreground" Value="Red"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>
    </Grid>
</Window>
