using System.Windows;
using System.Windows.Input;

namespace AccountingApp.UI
{
    public partial class SimpleLoginWindow : Window
    {
        public SimpleLoginWindow()
        {
            InitializeComponent();
            
            // إعداد الأحداث
            PasswordBox.KeyDown += PasswordBox_KeyDown;
            UsernameTextBox.KeyDown += UsernameTextBox_KeyDown;
            
            // تركيز على حقل اسم المستخدم
            Loaded += (s, e) => UsernameTextBox.Focus();
        }

        private void UsernameTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                PasswordBox.Focus();
            }
        }

        private void PasswordBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                PerformLogin();
            }
        }

        private void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            PerformLogin();
        }

        private void PerformLogin()
        {
            try
            {
                // إخفاء رسالة الخطأ
                ErrorTextBlock.Visibility = Visibility.Collapsed;
                
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(UsernameTextBox.Text))
                {
                    ShowError("يرجى إدخال اسم المستخدم");
                    UsernameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(PasswordBox.Password))
                {
                    ShowError("يرجى إدخال كلمة المرور");
                    PasswordBox.Focus();
                    return;
                }

                // تعطيل زر تسجيل الدخول أثناء المعالجة
                LoginButton.IsEnabled = false;
                LoginButton.Content = "جاري التحقق...";

                // تسجيل دخول مبسط
                if (UsernameTextBox.Text.Trim() == "admin" && PasswordBox.Password == "admin123")
                {
                    // تسجيل الدخول نجح
                    var mainWindow = new SimpleMainWindow();
                    mainWindow.Show();
                    this.Close();
                }
                else
                {
                    ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
                    PasswordBox.Clear();
                    PasswordBox.Focus();
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ: {ex.Message}");
            }
            finally
            {
                // إعادة تفعيل زر تسجيل الدخول
                LoginButton.IsEnabled = true;
                LoginButton.Content = "تسجيل الدخول";
            }
        }

        private void ShowError(string message)
        {
            ErrorTextBlock.Text = message;
            ErrorTextBlock.Visibility = Visibility.Visible;
        }
    }
}
